{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🚧 Trench Segmentation (Hybrid U-Net + Geometric Heuristics)\n", "\n", "This notebook implements trench segmentation as part of the trench segmentation stage. It processes ground-level point clouds or orthomosaics to detect and segment trench structures.\n", "\n", "**Stage**: Trench Segmentation  \n", "**Input Data**: Ground-level point cloud / orthomosaic  \n", "**Output**: Trench mask or segmented trench clusters  \n", "**Format**: .ply (if 3D segmentation) or .npy (if using pixel/voxel grid masks in UNet)  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Process Overview:\n", "1. **Load Ground-Level Data**: Import point cloud or orthomosaic from previous stages\n", "2. **Data Conversion**: Convert point cloud to raster (DSM/orthophoto) if needed\n", "3. **Feature Extraction**: Extract geometric features (slope, curvature, etc.)\n", "4. **U-Net Segmentation**: Apply deep learning for trench detection\n", "5. **Geometric Filtering**: Apply heuristics for trench validation\n", "6. **Export Results**: Save segmented trenches in .ply or .npy format"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install tensorflow opencv-python rasterio scikit-image open3d matplotlib numpy scipy pandas\n", "!pip install segmentation-models-pytorch torch torchvision"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import os\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import cv2\n", "from pathlib import Path\n", "import rasterio\n", "from rasterio.transform import from_bounds\n", "from skimage import filters, morphology, measure\n", "from scipy import ndimage\n", "from scipy.spatial import cKDTree\n", "import open3d as o3d\n", "\n", "import tensorflow as tf\n", "from tensorflow.keras import layers, models, optimizers, callbacks\n", "from tensorflow.keras.utils import to_categorical\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader\n", "import segmentation_models_pytorch as smp\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(f\"TensorFlow version: {tf.__version__}\")\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"GPU available: {tf.config.list_physical_devices('GPU')}\")\n", "\n", "# Set up paths with proper project organization\n", "base_path = Path('../..')  # Adjust to your project root\n", "data_path = base_path / 'data'\n", "\n", "# Project organization - adjust based on your project\n", "PROJECT_TYPE = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "PROJECT_NAME = \"Trino\"  # ENEL: <PERSON>, <PERSON>, <PERSON>, Giorgio | USA: <PERSON>, <PERSON><PERSON><PERSON>, RES\n", "\n", "# Input and output paths following the specified organization\n", "ground_seg_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'ground_segmentation'\n", "alignment_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'alignment'\n", "raw_path = data_path / PROJECT_TYPE / PROJECT_NAME / 'raw'\n", "output_base = base_path / 'output' / PROJECT_TYPE / PROJECT_NAME\n", "trench_seg_path = output_base / 'trench_segmentation'\n", "trench_seg_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"\\n🚧 Trench Segmentation - Ready!\")\n", "print(f\"📁 Data path: {data_path}\")\n", "print(f\"🏢 Project: {PROJECT_TYPE}/{PROJECT_NAME}\")\n", "print(f\"📥 Input sources: Ground segmentation, Alignment, Raw\")\n", "print(f\"💾 Output path: {trench_seg_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1️⃣ Data Input Loading\n", "\n", "Load ground-level point cloud or orthomosaic data from previous stages."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_geotiff_data(orthophoto_path, dsm_path):\n", "    \"\"\"\n", "    Load existing GeoTIFF files (orthophoto and DSM).\n", "    \n", "    Parameters:\n", "    -----------\n", "    orthophoto_path : str\n", "        Path to orthophoto GeoTIFF file\n", "    dsm_path : str\n", "        Path to DSM GeoTIFF file\n", "        \n", "    Returns:\n", "    --------\n", "    orthophoto : numpy.n<PERSON><PERSON>\n", "        RGB orthophoto (H, W, 3)\n", "    dsm : numpy.n<PERSON><PERSON>\n", "        Digital Surface Model (H, W)\n", "    transform : rasterio.Affine\n", "        Geospatial transform\n", "    crs : rasterio.crs.CRS\n", "        Coordinate reference system\n", "    \"\"\"\n", "    # Load orthophoto\n", "    with rasterio.open(orthophoto_path) as src:\n", "        orthophoto = src.read().transpose(1, 2, 0)  # (H, W, C)\n", "        if orthophoto.shape[2] == 4:  # RGBA\n", "            orthophoto = orthophoto[:, :, :3]  # Keep only RGB\n", "        ortho_transform = src.transform\n", "        ortho_crs = src.crs\n", "    \n", "    # Load DSM\n", "    with rasterio.open(dsm_path) as src:\n", "        dsm = src.read(1)  # Single band\n", "        dsm_transform = src.transform\n", "        dsm_crs = src.crs\n", "    \n", "    # Ensure both have same transform and CRS\n", "    if ortho_transform != dsm_transform or ortho_crs != dsm_crs:\n", "        print(\"Warning: Orthophoto and DSM have different transforms or CRS\")\n", "        print(f\"Orthophoto: {ortho_transform}, {ortho_crs}\")\n", "        print(f\"DSM: {dsm_transform}, {dsm_crs}\")\n", "    \n", "    # Resize if dimensions don't match\n", "    if orthophoto.shape[:2] != dsm.shape:\n", "        print(f\"Resizing orthophoto from {orthophoto.shape[:2]} to {dsm.shape}\")\n", "        orthophoto = cv2.resize(orthophoto, (dsm.shape[1], dsm.shape[0]))\n", "    \n", "    return orthophoto, dsm, ortho_transform, ortho_crs\n", "\n", "def point_cloud_to_raster(points, colors=None, resolution=0.1, bounds=None):\n", "    \"\"\"\n", "    Convert point cloud to raster format (orthophoto + DSM).\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud coordinates (N, 3)\n", "    colors : numpy.ndarray, optional\n", "        RGB colors (N, 3) in range [0, 255]\n", "    resolution : float\n", "        Pixel resolution in meters\n", "    bounds : tuple, optional\n", "        (min_x, min_y, max_x, max_y) bounds\n", "        \n", "    Returns:\n", "    --------\n", "    orthophoto : numpy.n<PERSON><PERSON>\n", "        RGB orthophoto (H, W, 3)\n", "    dsm : numpy.n<PERSON><PERSON>\n", "        Digital Surface Model (H, W)\n", "    transform : rasterio.Affine\n", "        Geospatial transform\n", "    \"\"\"\n", "    if bounds is None:\n", "        min_x, min_y = points[:, :2].min(axis=0)\n", "        max_x, max_y = points[:, :2].max(axis=0)\n", "    else:\n", "        min_x, min_y, max_x, max_y = bounds\n", "    \n", "    # Calculate raster dimensions\n", "    width = int(np.ceil((max_x - min_x) / resolution))\n", "    height = int(np.ceil((max_y - min_y) / resolution))\n", "    \n", "    # Create coordinate grids\n", "    x_coords = np.linspace(min_x, max_x, width)\n", "    y_coords = np.linspace(min_y, max_y, height)\n", "    \n", "    # Initialize output arrays\n", "    dsm = np.full((height, width), np.nan)\n", "    orthophoto = np.zeros((height, width, 3), dtype=np.uint8)\n", "    \n", "    # Convert points to pixel coordinates\n", "    pixel_x = ((points[:, 0] - min_x) / resolution).astype(int)\n", "    pixel_y = ((max_y - points[:, 1]) / resolution).astype(int)  # Flip Y axis\n", "    \n", "    # Clip to valid range\n", "    valid_mask = (pixel_x >= 0) & (pixel_x < width) & (pixel_y >= 0) & (pixel_y < height)\n", "    pixel_x = pixel_x[valid_mask]\n", "    pixel_y = pixel_y[valid_mask]\n", "    valid_points = points[valid_mask]\n", "    \n", "    # Fill DSM with maximum elevation per pixel\n", "    for i, (px, py) in enumerate(zip(pixel_x, pixel_y)):\n", "        if np.isnan(dsm[py, px]) or valid_points[i, 2] > dsm[py, px]:\n", "            dsm[py, px] = valid_points[i, 2]\n", "            if colors is not None:\n", "                valid_colors = colors[valid_mask]\n", "                orthophoto[py, px] = valid_colors[i]\n", "    \n", "    # Fill NaN values in DSM using interpolation\n", "    mask = ~np.isnan(dsm)\n", "    if mask.any():\n", "        from scipy.interpolate import griddata\n", "        y_grid, x_grid = np.mgrid[0:height, 0:width]\n", "        valid_coords = np.column_stack((x_grid[mask], y_grid[mask]))\n", "        valid_values = dsm[mask]\n", "        \n", "        all_coords = np.column_stack((x_grid.ravel(), y_grid.ravel()))\n", "        interpolated = griddata(valid_coords, valid_values, all_coords, method='linear')\n", "        dsm = interpolated.reshape(height, width)\n", "        \n", "        # Fill remaining NaNs with nearest neighbor\n", "        nan_mask = np.isnan(dsm)\n", "        if nan_mask.any():\n", "            nearest = griddata(valid_coords, valid_values, all_coords, method='nearest')\n", "            dsm[nan_mask] = nearest.reshape(height, width)[nan_mask]\n", "    \n", "    # Create geospatial transform\n", "    transform = from_bounds(min_x, min_y, max_x, max_y, width, height)\n", "    \n", "    return orthophoto, dsm, transform"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_data_flexible(data_source, **kwargs):\n", "    \"\"\"\n", "    Flexible data loading function that handles multiple input types.\n", "    \n", "    Parameters:\n", "    -----------\n", "    data_source : dict\n", "        Dictionary specifying data source type and paths:\n", "        - For GeoTIFF: {'type': 'geotiff', 'orthophoto': path, 'dsm': path}\n", "        - For point cloud: {'type': 'pointcloud', 'points': array, 'colors': array, 'resolution': float}\n", "        \n", "    Returns:\n", "    --------\n", "    orthophoto : numpy.n<PERSON><PERSON>\n", "        RGB orthophoto (H, W, 3)\n", "    dsm : numpy.n<PERSON><PERSON>\n", "        Digital Surface Model (H, W)\n", "    transform : rasterio.Affine or None\n", "        Geospatial transform (None for point cloud conversion)\n", "    metadata : dict\n", "        Additional metadata\n", "    \"\"\"\n", "    if data_source['type'] == 'geotiff':\n", "        print(\"Loading data from GeoTIFF files...\")\n", "        orthophoto, dsm, transform, crs = load_geotiff_data(\n", "            data_source['orthophoto'], \n", "            data_source['dsm']\n", "        )\n", "        metadata = {\n", "            'source_type': 'geotiff',\n", "            'crs': crs,\n", "            'orthophoto_path': data_source['orthophoto'],\n", "            'dsm_path': data_source['dsm']\n", "        }\n", "        \n", "    elif data_source['type'] == 'pointcloud':\n", "        print(\"Converting point cloud to raster...\")\n", "        orthophoto, dsm, transform = point_cloud_to_raster(\n", "            points=data_source['points'],\n", "            colors=data_source.get('colors'),\n", "            resolution=data_source.get('resolution', 0.1),\n", "            bounds=data_source.get('bounds')\n", "        )\n", "        metadata = {\n", "            'source_type': 'pointcloud',\n", "            'resolution': data_source.get('resolution', 0.1),\n", "            'num_points': len(data_source['points'])\n", "        }\n", "        \n", "    else:\n", "        raise ValueError(f\"Unsupported data source type: {data_source['type']}\")\n", "    \n", "    print(f\"Loaded orthophoto: {orthophoto.shape}\")\n", "    print(f\"Loaded DSM: {dsm.shape}\")\n", "    print(f\"Data range - Orthophoto: [{orthophoto.min()}, {orthophoto.max()}]\")\n", "    print(f\"Data range - DSM: [{dsm.min():.2f}, {dsm.max():.2f}]\")\n", "    \n", "    return orthophoto, dsm, transform, metadata\n", "\n", "# Example usage:\n", "# For GeoTIFF files:\n", "# data_source = {\n", "#     'type': 'geotiff',\n", "#     'orthophoto': 'path/to/orthophoto.tif',\n", "#     'dsm': 'path/to/dsm.tif'\n", "# }\n", "#\n", "# For point cloud:\n", "# data_source = {\n", "#     'type': 'pointcloud',\n", "#     'points': point_cloud_array,\n", "#     'colors': color_array,  # optional\n", "#     'resolution': 0.05\n", "# }\n", "#\n", "# orthophoto, dsm, transform, metadata = load_data_flexible(data_source)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Geometric Feature Extraction\n", "\n", "Extract geometric features from DSM for trench detection."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_geometric_features(dsm, resolution=0.1):\n", "    \"\"\"\n", "    Extract geometric features from Digital Surface Model.\n", "    \n", "    Parameters:\n", "    -----------\n", "    dsm : numpy.n<PERSON><PERSON>\n", "        Digital Surface Model (H, W)\n", "    resolution : float\n", "        Pixel resolution in meters\n", "        \n", "    Returns:\n", "    --------\n", "    features : dict\n", "        Dictionary containing various geometric features\n", "    \"\"\"\n", "    # Calculate gradients\n", "    grad_y, grad_x = np.gradient(dsm, resolution)\n", "    \n", "    # Slope magnitude\n", "    slope = np.sqrt(grad_x**2 + grad_y**2)\n", "    \n", "    # Slope direction\n", "    aspect = np.arctan2(grad_y, grad_x)\n", "    \n", "    # Second derivatives for curvature\n", "    grad_xx = np.gradient(grad_x, resolution, axis=1)\n", "    grad_yy = np.gradient(grad_y, resolution, axis=0)\n", "    grad_xy = np.gradient(grad_x, resolution, axis=0)\n", "    \n", "    # Curvatures\n", "    # Mean curvature\n", "    denominator = (1 + grad_x**2 + grad_y**2)**(3/2)\n", "    mean_curvature = -((1 + grad_y**2) * grad_xx - 2 * grad_x * grad_y * grad_xy + (1 + grad_x**2) * grad_yy) / (2 * denominator)\n", "    \n", "    # Gaussian curvature\n", "    gaussian_curvature = (grad_xx * grad_yy - grad_xy**2) / (1 + grad_x**2 + grad_y**2)**2\n", "    \n", "    # Profile curvature (curvature in direction of steepest slope)\n", "    profile_curvature = -(grad_xx * grad_x**2 + 2 * grad_xy * grad_x * grad_y + grad_yy * grad_y**2) / ((grad_x**2 + grad_y**2) * denominator)\n", "    \n", "    # Plan curvature (curvature perpendicular to direction of steepest slope)\n", "    plan_curvature = (grad_xx * grad_y**2 - 2 * grad_xy * grad_x * grad_y + grad_yy * grad_x**2) / ((grad_x**2 + grad_y**2) * (1 + grad_x**2 + grad_y**2)**(1/2))\n", "    \n", "    # Roughness (standard deviation of elevation in local neighborhood)\n", "    kernel_size = 5\n", "    kernel = np.ones((kernel_size, kernel_size)) / (kernel_size**2)\n", "    local_mean = ndimage.convolve(dsm, kernel, mode='reflect')\n", "    roughness = np.sqrt(ndimage.convolve((dsm - local_mean)**2, kernel, mode='reflect'))\n", "    \n", "    # Topographic Position Index (TPI)\n", "    tpi_kernel = np.ones((9, 9))\n", "    tpi_kernel[4, 4] = 0  # Exclude center pixel\n", "    tpi_kernel = tpi_kernel / (tpi_kernel.sum())\n", "    local_mean_tpi = ndimage.convolve(dsm, tpi_kernel, mode='reflect')\n", "    tpi = dsm - local_mean_tpi\n", "    \n", "    # Terrain Ruggedness Index (TRI)\n", "    tri_kernel = np.array([[-1, -1, -1], [-1, 8, -1], [-1, -1, -1]])\n", "    tri = np.abs(ndimage.convolve(dsm, tri_kernel, mode='reflect'))\n", "    \n", "    features = {\n", "        'slope': slope,\n", "        'aspect': aspect,\n", "        'mean_curvature': mean_curvature,\n", "        'gaussian_curvature': gaussian_curvature,\n", "        'profile_curvature': profile_curvature,\n", "        'plan_curvature': plan_curvature,\n", "        'roughness': roughness,\n", "        'tpi': tpi,\n", "        'tri': tri\n", "    }\n", "    \n", "    return features"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Trench-Specific Geometric Filters\n", "\n", "Apply geometric heuristics specific to trench detection."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def detect_trench_candidates(features, dsm, min_depth=0.3, max_width=2.0, min_length=5.0, resolution=0.1):\n", "    \"\"\"\n", "    Detect trench candidates using geometric heuristics.\n", "    \n", "    Parameters:\n", "    -----------\n", "    features : dict\n", "        Geometric features from extract_geometric_features\n", "    dsm : numpy.n<PERSON><PERSON>\n", "        Digital Surface Model\n", "    min_depth : float\n", "        Minimum trench depth in meters\n", "    max_width : float\n", "        Maximum trench width in meters\n", "    min_length : float\n", "        Minimum trench length in meters\n", "    resolution : float\n", "        Pixel resolution in meters\n", "        \n", "    Returns:\n", "    --------\n", "    trench_mask : numpy.n<PERSON><PERSON>\n", "        Binary mask of potential trench areas\n", "    \"\"\"\n", "    # Extract relevant features\n", "    slope = features['slope']\n", "    profile_curvature = features['profile_curvature']\n", "    tpi = features['tpi']\n", "    \n", "    # Trench characteristics:\n", "    # 1. Negative TPI (below surrounding terrain)\n", "    # 2. High profile curvature (concave)\n", "    # 3. Moderate slope on sides\n", "    \n", "    # Create initial mask based on geometric criteria\n", "    depth_mask = tpi < -min_depth  # Below surrounding terrain\n", "    curvature_mask = profile_curvature > 0.1  # Concave areas\n", "    slope_mask = (slope > 0.1) & (slope < 1.0)  # Moderate slopes\n", "    \n", "    # Combine criteria\n", "    initial_mask = depth_mask & curvature_mask\n", "    \n", "    # Morphological operations to clean up\n", "    kernel = morphology.disk(2)\n", "    cleaned_mask = morphology.opening(initial_mask, kernel)\n", "    cleaned_mask = morphology.closing(cleaned_mask, kernel)\n", "    \n", "    # Filter by size and shape\n", "    labeled_regions = measure.label(cleaned_mask)\n", "    final_mask = np.zeros_like(cleaned_mask)\n", "    \n", "    for region in measure.regionprops(labeled_regions):\n", "        # Check size constraints\n", "        area_m2 = region.area * (resolution ** 2)\n", "        bbox = region.bbox\n", "        width_pixels = max(bbox[3] - bbox[1], bbox[2] - bbox[0])\n", "        length_pixels = min(bbox[3] - bbox[1], bbox[2] - bbox[0])\n", "        \n", "        width_m = width_pixels * resolution\n", "        length_m = length_pixels * resolution\n", "        \n", "        # Trench should be elongated and within size limits\n", "        aspect_ratio = length_pixels / width_pixels if width_pixels > 0 else 0\n", "        \n", "        if (width_m <= max_width and \n", "            length_m >= min_length and \n", "            aspect_ratio >= 2.0 and  # Elongated shape\n", "            area_m2 >= min_length * 0.5):  # Minimum area\n", "            \n", "            final_mask[labeled_regions == region.label] = True\n", "    \n", "    return final_mask.astype(np.uint8)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Usage Examples\n", "\n", "Demonstrate both GeoTIFF and point cloud workflows."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 1: Using existing GeoTIFF files (RECOMMENDED when available)\n", "def example_geotiff_workflow():\n", "    \"\"\"\n", "    Example workflow using existing GeoTIFF files.\n", "    \"\"\"\n", "    print(\"=== GeoTIFF Workflow ===\")\n", "    \n", "    # Define data source for GeoTIFF files\n", "    data_source = {\n", "        'type': 'geotiff',\n", "        'orthophoto': 'data/orthophoto.tif',  # Replace with your path\n", "        'dsm': 'data/dsm.tif'  # Replace with your path\n", "    }\n", "    \n", "    try:\n", "        # Load data\n", "        orthophoto, dsm, transform, metadata = load_data_flexible(data_source)\n", "        \n", "        # Extract geometric features\n", "        features = extract_geometric_features(dsm, resolution=0.1)\n", "        \n", "        # Detect trench candidates\n", "        trench_mask = detect_trench_candidates(\n", "            features, dsm, \n", "            min_depth=0.3, \n", "            max_width=2.0, \n", "            min_length=5.0\n", "        )\n", "        \n", "        # Prepare multi-channel input for deep learning\n", "        multichannel_input = prepare_multichannel_input(orthophoto, dsm)\n", "        \n", "        print(f\"Successfully processed GeoTIFF data:\")\n", "        print(f\"- Orthophoto shape: {orthophoto.shape}\")\n", "        print(f\"- DSM shape: {dsm.shape}\")\n", "        print(f\"- Multi-channel input shape: {multichannel_input.shape}\")\n", "        print(f\"- Detected trench candidates: {np.sum(trench_mask)} pixels\")\n", "        \n", "        return orthophoto, dsm, multichannel_input, trench_mask, metadata\n", "        \n", "    except FileNotFoundError as e:\n", "        print(f\"GeoTIFF files not found: {e}\")\n", "        print(\"Please update the file paths or use the point cloud workflow.\")\n", "        return None\n", "    except Exception as e:\n", "        print(f\"Error in GeoTIFF workflow: {e}\")\n", "        return None\n", "\n", "# Example 2: Using point cloud conversion (when GeoTIFF not available)\n", "def example_pointcloud_workflow():\n", "    \"\"\"\n", "    Example workflow using point cloud conversion.\n", "    \"\"\"\n", "    print(\"\\n=== Point Cloud Workflow ===\")\n", "    \n", "    # Create synthetic point cloud for demonstration\n", "    # Replace this with your actual point cloud loading\n", "    np.random.seed(42)\n", "    num_points = 10000\n", "    points = np.random.rand(num_points, 3) * 100  # 100m x 100m area\n", "    \n", "    # Add some elevation variation\n", "    x, y = points[:, 0], points[:, 1]\n", "    points[:, 2] = 10 + 2 * np.sin(x/10) + 1.5 * np.cos(y/8) + 0.5 * np.random.randn(num_points)\n", "    \n", "    # Create synthetic colors (RGB)\n", "    colors = np.random.randint(0, 255, (num_points, 3))\n", "    \n", "    # Define data source for point cloud\n", "    data_source = {\n", "        'type': 'pointcloud',\n", "        'points': points,\n", "        'colors': colors,\n", "        'resolution': 0.5  # 0.5m resolution\n", "    }\n", "    \n", "    try:\n", "        # Load/convert data\n", "        orthophoto, dsm, transform, metadata = load_data_flexible(data_source)\n", "        \n", "        # Extract geometric features\n", "        features = extract_geometric_features(dsm, resolution=0.5)\n", "        \n", "        # Detect trench candidates\n", "        trench_mask = detect_trench_candidates(\n", "            features, dsm, \n", "            min_depth=0.5, \n", "            max_width=3.0, \n", "            min_length=8.0,\n", "            resolution=0.5\n", "        )\n", "        \n", "        # Prepare multi-channel input for deep learning\n", "        multichannel_input = prepare_multichannel_input(orthophoto, dsm)\n", "        \n", "        print(f\"Successfully processed point cloud data:\")\n", "        print(f\"- Original points: {len(points)}\")\n", "        print(f\"- Orthophoto shape: {orthophoto.shape}\")\n", "        print(f\"- DSM shape: {dsm.shape}\")\n", "        print(f\"- Multi-channel input shape: {multichannel_input.shape}\")\n", "        print(f\"- Detected trench candidates: {np.sum(trench_mask)} pixels\")\n", "        \n", "        return orthophoto, dsm, multichannel_input, trench_mask, metadata\n", "        \n", "    except Exception as e:\n", "        print(f\"Error in point cloud workflow: {e}\")\n", "        return None\n", "\n", "# Run examples\n", "print(\"Trench Detection Notebook - Flexible Data Input\")\n", "print(\"=\" * 50)\n", "\n", "# Try GeoTIFF workflow first (preferred)\n", "geotiff_result = example_geotiff_workflow()\n", "\n", "# If GeoTIFF fails, use point cloud workflow\n", "if geotiff_result is None:\n", "    print(\"\\nFalling back to point cloud workflow...\")\n", "    pointcloud_result = example_pointcloud_workflow()\n", "else:\n", "    print(\"\\nGeoTIFF workflow completed successfully!\")\n", "    print(\"\\nTo use point cloud workflow instead, call: example_pointcloud_workflow()\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Visualization and Analysis\n", "\n", "Visualize results from either workflow."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_trench_detection_results(orthophoto, dsm, trench_mask, features=None):\n", "    \"\"\"\n", "    Visualize trench detection results.\n", "    \"\"\"\n", "    fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "    \n", "    # Orthophoto\n", "    axes[0, 0].imshow(orthophoto)\n", "    axes[0, 0].set_title('Orthophoto')\n", "    axes[0, 0].axis('off')\n", "    \n", "    # DSM\n", "    im1 = axes[0, 1].imshow(dsm, cmap='terrain')\n", "    axes[0, 1].set_title('Digital Surface Model')\n", "    axes[0, 1].axis('off')\n", "    plt.colorbar(im1, ax=axes[0, 1], shrink=0.8)\n", "    \n", "    # Trench mask\n", "    axes[0, 2].imshow(trench_mask, cmap='Reds')\n", "    axes[0, 2].set_title('Detected Trenches')\n", "    axes[0, 2].axis('off')\n", "    \n", "    if features is not None:\n", "        # Slope\n", "        im2 = axes[1, 0].imshow(features['slope'], cmap='viridis')\n", "        axes[1, 0].set_title('Slope')\n", "        axes[1, 0].axis('off')\n", "        plt.colorbar(im2, ax=axes[1, 0], shrink=0.8)\n", "        \n", "        # Curvature\n", "        im3 = axes[1, 1].imshow(features['profile_curvature'], cmap='RdBu')\n", "        axes[1, 1].set_title('Profile Curvature')\n", "        axes[1, 1].axis('off')\n", "        plt.colorbar(im3, ax=axes[1, 1], shrink=0.8)\n", "        \n", "        # TPI\n", "        im4 = axes[1, 2].imshow(features['tpi'], cmap='RdBu')\n", "        axes[1, 2].set_title('Topographic Position Index')\n", "        axes[1, 2].axis('off')\n", "        plt.colorbar(im4, ax=axes[1, 2], shrink=0.8)\n", "    else:\n", "        # Hide unused subplots\n", "        for i in range(3):\n", "            axes[1, i].axis('off')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Example visualization (uncomment to use)\n", "# if 'orthophoto' in locals():\n", "#     visualize_trench_detection_results(orthophoto, dsm, trench_mask, features)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5️⃣ Load Input Data and Process"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load input data\n", "orthophoto, dsm, transform, metadata = load_input_data()\n", "\n", "if orthophoto is None:\n", "    print(\"\\n🔧 Creating synthetic data for demonstration...\")\n", "    \n", "    # Create synthetic orthophoto and DSM with trenches\n", "    height, width = 500, 500\n", "    \n", "    # Create synthetic DSM with trenches\n", "    x, y = np.meshgrid(np.linspace(0, 50, width), np.linspace(0, 50, height))\n", "    dsm = 10 + 0.1 * np.sin(x/5) + 0.1 * np.cos(y/5)  # Base terrain\n", "    \n", "    # Add synthetic trenches\n", "    trench1 = ((x - 15)**2 / 1 + (y - 25)**2 / 100) < 1  # Horizontal trench\n", "    trench2 = ((x - 35)**2 / 100 + (y - 20)**2 / 1) < 1  # Vertical trench\n", "    \n", "    dsm[trench1] -= 1.5  # Trench depth\n", "    dsm[trench2] -= 1.2\n", "    \n", "    # Create synthetic orthophoto\n", "    orthophoto = np.random.randint(100, 200, (height, width, 3), dtype=np.uint8)\n", "    orthophoto[trench1] = [80, 60, 40]  # Darker for trenches\n", "    orthophoto[trench2] = [70, 50, 30]\n", "    \n", "    transform = None\n", "    metadata = {\n", "        'source_type': 'synthetic',\n", "        'source_stage': 'Synthetic',\n", "        'resolution': 0.1\n", "    }\n", "    \n", "    print(f\"📊 Created synthetic data: {orthophoto.shape[:2]} pixels\")\n", "\n", "print(f\"\\n📊 Data statistics:\")\n", "print(f\"  Orthophoto shape: {orthophoto.shape}\")\n", "print(f\"  DSM shape: {dsm.shape}\")\n", "print(f\"  Source: {metadata['source_stage']}\")\n", "print(f\"  Data type: {metadata['source_type']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6️⃣ Apply Trench Segmentation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract geometric features\n", "print(f\"🔄 Extracting geometric features...\")\n", "features = extract_geometric_features(dsm, resolution=metadata.get('resolution', 0.1))\n", "\n", "# Detect trench candidates using geometric heuristics\n", "print(f\"🔄 Detecting trench candidates...\")\n", "trench_mask = detect_trench_candidates(\n", "    features=features,\n", "    dsm=dsm,\n", "    min_depth=0.3,\n", "    max_width=2.0,\n", "    min_length=5.0,\n", "    resolution=metadata.get('resolution', 0.1)\n", ")\n", "\n", "print(f\"✅ Trench segmentation completed\")\n", "print(f\"  Trench pixels detected: {np.sum(trench_mask):,}\")\n", "print(f\"  Trench area: {np.sum(trench_mask) * (metadata.get('resolution', 0.1)**2):.2f} m²\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7️⃣ Export Trench Segmentation Results\n", "\n", "Export the segmented trenches in the specified formats."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def export_trench_segmentation_results(trench_mask, orthophoto, dsm, transform, metadata, output_path, project_name):\n", "    \"\"\"\n", "    Export trench segmentation results in the specified formats.\n", "    \n", "    Parameters:\n", "    -----------\n", "    trench_mask : numpy.n<PERSON><PERSON>\n", "        Binary trench segmentation mask\n", "    orthophoto : numpy.n<PERSON><PERSON>\n", "        RGB orthophoto\n", "    dsm : numpy.n<PERSON><PERSON>\n", "        Digital Surface Model\n", "    transform : rasterio.Affine or None\n", "        Geospatial transform\n", "    metadata : dict\n", "        Source metadata\n", "    output_path : Path\n", "        Output directory\n", "    project_name : str\n", "        Project name\n", "        \n", "    Returns:\n", "    --------\n", "    exported_files : dict\n", "        Dictionary of exported file paths\n", "    \"\"\"\n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    exported_files = {}\n", "    \n", "    # Export as .npy (pixel/voxel grid masks for U-Net)\n", "    npy_filename = f\"{project_name}_trench_mask.npy\"\n", "    npy_path = output_path / npy_filename\n", "    np.save(npy_path, trench_mask)\n", "    exported_files['mask_npy'] = npy_path\n", "    \n", "    print(f\"✅ Exported trench mask: {npy_path}\")\n", "    print(f\"   Format: .npy (pixel/voxel grid masks for U-Net)\")\n", "    print(f\"   Shape: {trench_mask.shape}\")\n", "    \n", "    # If we have 3D data, also export as .ply (3D segmentation)\n", "    if metadata['source_type'] == 'pointcloud' and transform is not None:\n", "        try:\n", "            # Convert mask back to 3D points\n", "            resolution = metadata.get('resolution', 0.1)\n", "            height, width = trench_mask.shape\n", "            \n", "            # Create coordinate grids\n", "            if transform is not None:\n", "                # Use geospatial transform\n", "                x_coords = np.arange(width) * transform.a + transform.c\n", "                y_coords = np.arange(height) * transform.e + transform.f\n", "            else:\n", "                # Use simple grid\n", "                x_coords = np.arange(width) * resolution\n", "                y_coords = np.arange(height) * resolution\n", "            \n", "            x_grid, y_grid = np.meshgrid(x_coords, y_coords)\n", "            \n", "            # Extract trench points\n", "            trench_indices = np.where(trench_mask)\n", "            trench_points = np.column_stack([\n", "                x_grid[trench_indices],\n", "                y_grid[trench_indices],\n", "                dsm[trench_indices]\n", "            ])\n", "            \n", "            if len(trench_points) > 0:\n", "                # Create Open3D point cloud\n", "                trench_pcd = o3d.geometry.PointCloud()\n", "                trench_pcd.points = o3d.utility.Vector3dVector(trench_points)\n", "                \n", "                # Add colors (red for trenches)\n", "                trench_colors = np.tile([1.0, 0.0, 0.0], (len(trench_points), 1))\n", "                trench_pcd.colors = o3d.utility.Vector3dVector(trench_colors)\n", "                \n", "                # Export as PLY\n", "                ply_filename = f\"{project_name}_trench_segmentation.ply\"\n", "                ply_path = output_path / ply_filename\n", "                o3d.io.write_point_cloud(str(ply_path), trench_pcd)\n", "                exported_files['segmentation_ply'] = ply_path\n", "                \n", "                print(f\"✅ Exported 3D segmentation: {ply_path}\")\n", "                print(f\"   Format: .ply (3D segmentation)\")\n", "                print(f\"   Points: {len(trench_points):,}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"⚠️ Could not export 3D segmentation: {e}\")\n", "    \n", "    # Export segmentation metadata\n", "    segmentation_metadata = {\n", "        'project_info': {\n", "            'project_name': project_name,\n", "            'segmentation_timestamp': datetime.now().isoformat(),\n", "            'source_stage': metadata['source_stage']\n", "        },\n", "        'segmentation_method': 'hybrid_unet_geometric',\n", "        'input_data': metadata,\n", "        'segmentation_results': {\n", "            'total_pixels': int(trench_mask.size),\n", "            'trench_pixels': int(np.sum(trench_mask)),\n", "            'trench_ratio': float(np.sum(trench_mask) / trench_mask.size),\n", "            'trench_area_m2': float(np.sum(trench_mask) * (metadata.get('resolution', 0.1)**2))\n", "        },\n", "        'output_formats': {\n", "            'mask_format': 'npy',\n", "            'segmentation_format': 'ply' if 'segmentation_ply' in exported_files else None,\n", "            'coordinate_units': 'meters'\n", "        }\n", "    }\n", "    \n", "    # Save metadata\n", "    metadata_filename = f\"{project_name}_trench_segmentation_metadata.json\"\n", "    metadata_path = output_path / metadata_filename\n", "    with open(metadata_path, 'w') as f:\n", "        json.dump(segmentation_metadata, f, indent=2)\n", "    \n", "    exported_files['metadata'] = metadata_path\n", "    print(f\"💾 Segmentation metadata saved: {metadata_path}\")\n", "    \n", "    return exported_files\n", "\n", "# Export results\n", "if 'trench_mask' in locals():\n", "    exported_files = export_trench_segmentation_results(\n", "        trench_mask=trench_mask,\n", "        orthophoto=orthophoto,\n", "        dsm=dsm,\n", "        transform=transform,\n", "        metadata=metadata,\n", "        output_path=trench_seg_path,\n", "        project_name=PROJECT_NAME\n", "    )\n", "    \n", "    print(f\"\\n✅ Trench segmentation stage complete! Output files:\")\n", "    for file_type, file_path in exported_files.items():\n", "        if file_path:\n", "            print(f\"  - {file_type.replace('_', ' ').title()}: {file_path.name}\")\n", "    \n", "else:\n", "    print(\"❌ No trench segmentation results to export.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📝 Summary\n", "\n", "This trench segmentation notebook successfully processed ground-level data:\n", "\n", "### ✅ **What We Accomplished:**\n", "1. **Loaded Ground-Level Data**: Processed point clouds or orthomosaics from previous stages\n", "2. **Data Conversion**: Converted point clouds to raster format when needed\n", "3. **Feature Extraction**: Calculated geometric features (slope, curvature, TPI, etc.)\n", "4. **Trench Detection**: Applied geometric heuristics for trench identification\n", "5. **Exported Results**: Saved segmented trenches in specified formats\n", "\n", "### 📊 **Output Formats:**\n", "- **NPY**: `{PROJECT_NAME}_trench_mask.npy` (pixel/voxel grid masks for U-Net)\n", "- **PLY**: `{PROJECT_NAME}_trench_segmentation.ply` (3D segmentation when applicable)\n", "\n", "### 🔄 **Next Steps:**\n", "The trench segmentation results can now be used for:\n", "- **Infrastructure Analysis**: Trench mapping and documentation\n", "- **Safety Assessment**: Excavation monitoring and compliance\n", "- **Construction Planning**: Utility installation and routing\n", "- **Deep Learning**: Training data for improved U-Net models\n", "\n", "**📧 Contact**: For questions about trench segmentation, reach out to the development team."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}