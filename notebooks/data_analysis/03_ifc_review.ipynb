{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# IFC File Organization and Contents Exploration\n", "\n", "**Stage**: Data Analysis - IFC Discovery  \n", "**Input Data**: IFC files (.ifc) from data/raw directory  \n", "**Output**: File organization mapping and drawing type classification  \n", "**Format**: JSON reports and structured file inventory  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis\n", "\n", "## Overview\n", "\n", "Explores IFC file organization and contents to:\n", "- **Identify key drawing types** - Foundation, tracker, trench drawings\n", "- **Map file structure** - Organize files by expected layers or zones\n", "- **Classify by purpose** - Categorize files for downstream processing\n", "- **Plan preprocessing** - Determine which files need metadata extraction\n", "\n", "## Drawing Type Categories\n", "\n", "- **Foundation**: Foundation plans, pile layouts, structural elements\n", "- **Tracker**: Solar tracker mounting systems and configurations\n", "- **Trench**: Cable trenches, conduit layouts, electrical infrastructure\n", "- **Site**: Overall site plans, boundaries, topography\n", "- **Other**: Miscellaneous drawings and reference files"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: ifcopenshell in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (0.8.2)\n", "Requirement already satisfied: pandas in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (2.3.0)\n", "Requirement already satisfied: tqdm in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (4.67.1)\n", "Requirement already satisfied: shapely in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from ifcopenshell) (2.1.1)\n", "Requirement already satisfied: numpy in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from ifcopenshell) (1.26.4)\n", "Requirement already satisfied: isodate in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from ifcopenshell) (0.7.2)\n", "Requirement already satisfied: python-dateutil in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from ifcopenshell) (2.9.0.post0)\n", "Requirement already satisfied: lark in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from ifcopenshell) (1.2.2)\n", "Requirement already satisfied: typing-extensions in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from ifcopenshell) (4.14.0)\n", "Requirement already satisfied: pytz>=2020.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pandas) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pandas) (2025.2)\n", "Requirement already satisfied: six>=1.5 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from python-dateutil->ifcopenshell) (1.17.0)\n"]}], "source": ["!python -m pip install ifcopenshell pandas tqdm"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ifcopenshell available - basic IFC info enabled\n", "IFC File Organization Exploration - Starting...\n", "Timestamp: 2025-06-26 16:32:23\n"]}], "source": ["# Import required libraries\n", "import os\n", "import json\n", "import re\n", "from pathlib import Path\n", "from datetime import datetime\n", "from typing import Dict, List, Any, Optional\n", "from collections import defaultdict\n", "\n", "import pandas as pd\n", "from tqdm import tqdm\n", "\n", "# Try to import IFC processing library for basic info\n", "try:\n", "    import ifcopenshell\n", "    IFC_AVAILABLE = True\n", "    print(\"ifcopenshell available - basic IFC info enabled\")\n", "except ImportError:\n", "    IFC_AVAILABLE = False\n", "    print(\"ifcopenshell not available - file organization only\")\n", "    print(\"Install with: pip install ifcopenshell\")\n", "\n", "print(\"IFC File Organization Exploration - Starting...\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Project root: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis\n", "Raw data path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw\n", "Output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs\n"]}], "source": ["# Setup paths\n", "project_root = Path('../..')  # Navigate to project root from notebooks/data_analysis/\n", "data_path = project_root / 'data'\n", "raw_path = data_path / 'raw'\n", "output_path = data_path / 'output_runs'\n", "\n", "# Create output directory\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"Project root: {project_root.resolve()}\")\n", "print(f\"Raw data path: {raw_path.resolve()}\")\n", "print(f\"Output path: {output_path.resolve()}\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "DISCOVERED IFC FILES:\n", "==================================================\n", "Total IFC files found: 0\n", "  No IFC files found!\n", "  Search path: ../../data/raw\n", "  Extensions: .ifc\n"]}], "source": ["# Discover IFC files\n", "def discover_ifc_files(base_path: Path) -> List[Path]:\n", "    \"\"\"Discover IFC files in the data directory.\"\"\"\n", "    extensions = ['*.ifc', '*.IFC']\n", "    files = []\n", "    \n", "    for ext in extensions:\n", "        files.extend(base_path.rglob(ext))\n", "    \n", "    return sorted(files)\n", "\n", "# Find IFC files\n", "ifc_files = discover_ifc_files(raw_path)\n", "\n", "print(f\"\\nDISCOVERED IFC FILES:\")\n", "print(\"=\" * 50)\n", "print(f\"Total IFC files found: {len(ifc_files)}\")\n", "\n", "if ifc_files:\n", "    for i, file_path in enumerate(ifc_files, 1):\n", "        relative_path = file_path.relative_to(raw_path)\n", "        file_size_mb = file_path.stat().st_size / (1024 * 1024)\n", "        print(f\"  {i:2d}. {relative_path} ({file_size_mb:.1f} MB)\")\n", "else:\n", "    print(\"  No IFC files found!\")\n", "    print(f\"  Search path: {raw_path}\")\n", "    print(f\"  Extensions: .ifc\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Drawing type classification function defined\n"]}], "source": ["# Drawing type classification based on filename patterns\n", "def classify_drawing_type(file_path: Path) -> str:\n", "    \"\"\"Classify IFC file by drawing type based on filename patterns.\"\"\"\n", "    \n", "    filename = file_path.name.lower()\n", "    \n", "    # Foundation patterns\n", "    foundation_patterns = [\n", "        r'foundation', r'pile', r'footing', r'structural', r'support',\n", "        r'base', r'anchor', r'concrete', r'steel.*frame'\n", "    ]\n", "    \n", "    # Tracker patterns\n", "    tracker_patterns = [\n", "        r'tracker', r'mount', r'solar.*panel', r'pv.*system', r'array',\n", "        r'module', r'rack', r'tilt', r'azimuth'\n", "    ]\n", "    \n", "    # Trench patterns\n", "    trench_patterns = [\n", "        r'trench', r'cable', r'conduit', r'electrical', r'power',\n", "        r'wiring', r'duct.*bank', r'underground', r'utility'\n", "    ]\n", "    \n", "    # Site patterns\n", "    site_patterns = [\n", "        r'site.*plan', r'layout', r'boundary', r'topo', r'survey',\n", "        r'overall', r'master.*plan', r'general'\n", "    ]\n", "    \n", "    # Check patterns in order of specificity\n", "    for pattern in foundation_patterns:\n", "        if re.search(pattern, filename):\n", "            return 'foundation'\n", "    \n", "    for pattern in tracker_patterns:\n", "        if re.search(pattern, filename):\n", "            return 'tracker'\n", "    \n", "    for pattern in trench_patterns:\n", "        if re.search(pattern, filename):\n", "            return 'trench'\n", "    \n", "    for pattern in site_patterns:\n", "        if re.search(pattern, filename):\n", "            return 'site'\n", "    \n", "    return 'other'\n", "\n", "print(\"Drawing type classification function defined\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "No IFC files found for analysis\n"]}], "source": ["# Analyze file organization and classify drawing types\n", "ifc_analysis = []\n", "drawing_types = defaultdict(list)\n", "site_organization = defaultdict(lambda: defaultdict(list))\n", "\n", "if ifc_files:\n", "    print(\"\\nANALYZING IFC FILE ORGANIZATION:\")\n", "    print(\"=\" * 50)\n", "    \n", "    for file_path in tqdm(ifc_files, desc=\"Classifying IFC files\"):\n", "        relative_path = file_path.relative_to(raw_path)\n", "        \n", "        # Basic file info\n", "        file_info = {\n", "            'file_path': str(relative_path),\n", "            'full_path': str(file_path),\n", "            'filename': file_path.name,\n", "            'file_size_mb': file_path.stat().st_size / (1024 * 1024),\n", "            'drawing_type': classify_drawing_type(file_path)\n", "        }\n", "        \n", "        # Extract site/project from path structure\n", "        path_parts = relative_path.parts\n", "        if len(path_parts) >= 2:\n", "            site_name = path_parts[1]  # Assuming structure: foundation_analysis/site_name/...\n", "            file_info['site'] = site_name\n", "        else:\n", "            file_info['site'] = 'unknown'\n", "        \n", "        # Try to get basic IFC info if available\n", "        if IFC_AVAILABLE:\n", "            try:\n", "                ifc_file = ifcopenshell.open(file_path)\n", "                file_info['ifc_schema'] = ifc_file.schema\n", "                file_info['ifc_entities'] = len(ifc_file.by_type('IfcRoot'))\n", "                \n", "                # Get project info if available\n", "                projects = ifc_file.by_type('IfcProject')\n", "                if projects:\n", "                    file_info['project_name'] = projects[0].Name or 'Unnamed'\n", "                    file_info['project_description'] = projects[0].Description or ''\n", "                \n", "            except Exception as e:\n", "                file_info['ifc_error'] = str(e)\n", "        \n", "        ifc_analysis.append(file_info)\n", "        drawing_types[file_info['drawing_type']].append(file_info)\n", "        site_organization[file_info['site']][file_info['drawing_type']].append(file_info)\n", "        \n", "        # Print classification result\n", "        print(f\"{relative_path} → {file_info['drawing_type'].upper()}\")\n", "    \n", "else:\n", "    print(\"\\nNo IFC files found for analysis\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "No IFC files available for organization analysis\n", "\n", "IFC organization exploration completed: 2025-06-26 16:32:23\n"]}], "source": ["# Generate organization summary and mapping\n", "if ifc_analysis:\n", "    print(f\"\\nIFC FILE ORGANIZATION SUMMARY:\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Drawing type distribution\n", "    print(f\"DRAWING TYPE DISTRIBUTION:\")\n", "    for drawing_type, files in drawing_types.items():\n", "        print(f\"  {drawing_type.upper()}: {len(files)} file(s)\")\n", "        for file_info in files[:3]:  # Show first 3 examples\n", "            print(f\"    - {file_info['filename']}\")\n", "        if len(files) > 3:\n", "            print(f\"    ... and {len(files) - 3} more\")\n", "        print()\n", "    \n", "    # Site organization\n", "    print(f\"SITE ORGANIZATION:\")\n", "    for site, site_files in site_organization.items():\n", "        total_files = sum(len(files) for files in site_files.values())\n", "        print(f\"  {site.upper()}: {total_files} file(s)\")\n", "        for drawing_type, files in site_files.items():\n", "            print(f\"    {drawing_type}: {len(files)} file(s)\")\n", "        print()\n", "    \n", "    # Expected layers/zones mapping\n", "    print(f\"EXPECTED LAYERS/ZONES MAPPING:\")\n", "    layer_mapping = {\n", "        'foundation': ['Foundation elements', 'Pile locations', 'Structural supports', 'Anchor points'],\n", "        'tracker': ['Solar panel arrays', 'Mounting systems', 'Tracker mechanisms', 'Module layouts'],\n", "        'trench': ['Cable routes', 'Conduit systems', 'Electrical infrastructure', 'Underground utilities'],\n", "        'site': ['Site boundaries', 'Topography', 'Access roads', 'General layout'],\n", "        'other': ['Reference drawings', 'Details', 'Sections', 'Miscellaneous']\n", "    }\n", "    \n", "    for drawing_type, expected_layers in layer_mapping.items():\n", "        if drawing_type in drawing_types:\n", "            print(f\"  {drawing_type.upper()} ({len(drawing_types[drawing_type])} files):\")\n", "            for layer in expected_layers:\n", "                print(f\"    - {layer}\")\n", "            print()\n", "    \n", "    # Preprocessing recommendations\n", "    print(f\"PREPROCESSING RECOMMENDATIONS:\")\n", "    print(\"=\" * 50)\n", "    \n", "    foundation_count = len(drawing_types.get('foundation', []))\n", "    tracker_count = len(drawing_types.get('tracker', []))\n", "    trench_count = len(drawing_types.get('trench', []))\n", "    \n", "    if foundation_count > 0:\n", "        print(f\"FOUNDATION FILES ({foundation_count}):\")\n", "        print(f\"  Priority: HIGH - Critical for foundation analysis\")\n", "        print(f\"  Metadata extraction: Recommended\")\n", "        print(f\"  Focus: Pile locations, structural elements, dimensions\")\n", "        print()\n", "    \n", "    if tracker_count > 0:\n", "        print(f\"TRACKER FILES ({tracker_count}):\")\n", "        print(f\"  Priority: MEDIUM - Important for context\")\n", "        print(f\"  Metadata extraction: Optional\")\n", "        print(f\"  Focus: Array layouts, mounting configurations\")\n", "        print()\n", "    \n", "    if trench_count > 0:\n", "        print(f\"TRENCH FILES ({trench_count}):\")\n", "        print(f\"  Priority: LOW - Secondary analysis\")\n", "        print(f\"  Metadata extraction: As needed\")\n", "        print(f\"  Focus: Cable routing, utility conflicts\")\n", "        print()\n", "    \n", "    # Save results\n", "    results = {\n", "        'analysis_date': datetime.now().isoformat(),\n", "        'total_files': len(ifc_analysis),\n", "        'drawing_type_summary': {dt: len(files) for dt, files in drawing_types.items()},\n", "        'site_summary': {site: {dt: len(files) for dt, files in site_files.items()} \n", "                        for site, site_files in site_organization.items()},\n", "        'layer_mapping': layer_mapping,\n", "        'files': ifc_analysis\n", "    }\n", "    \n", "    results_path = output_path / f\"ifc_organization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json\"\n", "    with open(results_path, 'w') as f:\n", "        json.dump(results, f, indent=2)\n", "    \n", "    print(f\"Results saved: {results_path}\")\n", "    \n", "else:\n", "    print(\"\\nNo IFC files available for organization analysis\")\n", "\n", "print(f\"\\nIFC organization exploration completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}