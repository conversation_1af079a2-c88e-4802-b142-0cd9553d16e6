{"cells": [{"cell_type": "markdown", "metadata": {"id": "EDRF4Jd2DyNO"}, "source": ["# Comprehensive Project Data Assessment for Thesis\n", "\n", "## Research Context\n", "\n", "**Thesis Title**: Monitoring Structural Integrity Using Machine Learning and Remotely Sensed Imagery\n", "\n", "**Research Objective**: Comparative analysis of traditional vs machine learning methods for solar foundation quality assessment\n", "\n", "**Data Requirements**:\n", "- Point clouds for 3D analysis and ML training\n", "- CAD files for reference geometry and alignment\n", "- Orthomosaics for 2D analysis and validation\n", "- IFC files for BIM integration (where available)\n", "\n", "**Timeline Constraint**: 14-week thesis completion requires careful dataset selection\n", "\n", "**Methodology**: Systematic exploration of all available data sources using AWS CLI commands to make evidence-based dataset selection decisions"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ZbeZZyVbFz6e", "outputId": "074d1cf1-f60a-40da-fc21-77c795b0a231"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pandas in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (2.3.0)\n", "Requirement already satisfied: numpy in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (1.26.4)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pandas) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pandas) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from pandas) (2025.2)\n", "Requirement already satisfied: six>=1.5 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from python-dateutil>=2.8.2->pandas) (1.17.0)\n"]}], "source": ["!pip install pandas numpy"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["import os\n", "from pathlib import Path\n", "import importlib\n", "\n", "import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dxHyY-XJGut2", "outputId": "15f0420e-2cff-430b-fbc0-2ddae8f81845"}, "outputs": [], "source": ["# Detect whether running in Colab or Local\n", "def is_colab():\n", "    return importlib.util.find_spec(\"google.colab\") is not None\n", "\n", "IN_COLAB = is_colab()"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["if IN_COLAB:\n", "    from google.colab import drive\n", "    drive.mount(\"/content/drive\", force_remount=True)\n", "    base_path = Path(\"/content/drive/MyDrive/thesis_datasets_20250613/asbuilt-foundation-analysis/data/raw\")\n", "    output_path = Path(\"/content/drive/MyDrive/Colab Notebooks/asbuilt-foundation-analysis/data/analysis_output\")\n", "else:\n", "    base_path = Path(\"../../data/raw/\")\n", "    output_path = Path(\"../../data/analysis_output\")\n", "\n", "output_path.mkdir(parents=True, exist_ok=True)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["def get_file_info(base_path):\n", "    entries = []\n", "    for site_dir in base_path.iterdir():\n", "        if not site_dir.is_dir():\n", "            continue\n", "        site_name = site_dir.name\n", "        for subfolder in ['cad', 'pointcloud', 'ortho']:\n", "            subdir = site_dir / subfolder\n", "            if not subdir.exists():\n", "                continue\n", "            for root, _, files in os.walk(subdir):\n", "                for file in files:\n", "                    full_path = os.path.join(root, file)\n", "                    size_mb = round(os.path.getsize(full_path) / (1024 * 1024), 2)\n", "                    extension = os.path.splitext(file)[1].lower()\n", "                    entries.append({\n", "                        \"Site\": site_name,\n", "                        \"Data_Type\": subfolder,\n", "                        \"File Path\": full_path,\n", "                        \"Size (MB)\": size_mb,\n", "                        \"Extension\": extension\n", "                    })\n", "    return pd.<PERSON><PERSON>rame(entries)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["def check_extension_availability(df, extension=\".las\"):\n", "    df = df.copy()\n", "    df['Extension'] = df['Extension'].str.lower()\n", "    projects_with_ext = df[df['Extension'] == extension]['Site'].unique()\n", "\n", "    return (\n", "        df[['Site']].drop_duplicates().assign(\n", "            **{f\"has_{extension}\": lambda x: x['Site'].isin(projects_with_ext).map({True: 'Yes', False: 'No'})}\n", "        ).sort_values(\"Site\").reset_index(drop=True)\n", "    )"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["pd.set_option('display.max_rows', None)\n", "pd.set_option('display.max_colwidth', None)  # Optional: shows full file paths"]}, {"cell_type": "markdown", "metadata": {"id": "imXQXUY3DyNR"}, "source": ["## Project Inventory\n", "\n", "Complete list of available projects with point cloud locations:"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["# Load All Files and Split by Type\n", "all_files_df = get_file_info(base_path)"]}, {"cell_type": "code", "execution_count": 54, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 480}, "id": "bthGAqoBFxWf", "outputId": "83648dd0-4c92-4ba0-a438-ce3ced45c1f0"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "Total File Entries: 103\n", "Unique Sites: 6\n", "Data Types Present: ['cad', 'pointcloud', 'ortho']\n", "File Extensions: ['', '.csv', '.docx', '.dwg', '.dxf', '.json', '.kml', '.kmz', '.las', '.pdf', '.py', '.rar', '.tif', '.zip']\n", "============================================================\n"]}], "source": ["# Summary information\n", "print(\"=\" * 60)\n", "print(f\"Total File Entries: {len(all_files_df)}\")\n", "print(f\"Unique Sites: {all_files_df['Site'].nunique()}\")\n", "print(f\"Data Types Present: {all_files_df['Data_Type'].unique().tolist()}\")\n", "print(f\"File Extensions: {sorted(all_files_df['Extension'].unique())}\")\n", "print(\"=\" * 60)"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["all_files_df.to_csv(output_path / \"all_file_info.csv\", index=False)"]}, {"cell_type": "markdown", "metadata": {"id": "NoKsoO3ODyNS"}, "source": ["## Systematic Data Exploration\n"]}, {"cell_type": "markdown", "metadata": {"id": "Vj-W9_yspFcB"}, "source": ["#### Point Cloud Analysis"]}, {"cell_type": "code", "execution_count": 45, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 143}, "id": "PzXJkij4vAjZ", "outputId": "521dfa02-a1f9-4f54-ee1d-46c9db984f88"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Site</th>\n", "      <th>has_.las</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>althea_rpcs</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>motali_de_castro</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>sunstreams_mccarthy</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  Site has_.las\n", "0          althea_rpcs      Yes\n", "1     motali_de_castro      Yes\n", "2  sunstreams_mccarthy      Yes"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["pc_df = all_files_df[all_files_df['Data_Type'] == 'pointcloud']\n", "check_extension_availability(pc_df, \".las\")"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Site</th>\n", "      <th>Data_Type</th>\n", "      <th>File Path</th>\n", "      <th><PERSON><PERSON> (MB)</th>\n", "      <th>Extension</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>sunstreams_mccarthy</td>\n", "      <td>pointcloud</td>\n", "      <td>../../data/raw/sunstreams_mccarthy/pointcloud/Buffer_las(rev1).las</td>\n", "      <td>36.13</td>\n", "      <td>.las</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>althea_rpcs</td>\n", "      <td>pointcloud</td>\n", "      <td>../../data/raw/althea_rpcs/pointcloud/Point_Cloud_0.5.las</td>\n", "      <td>113.53</td>\n", "      <td>.las</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>althea_rpcs</td>\n", "      <td>pointcloud</td>\n", "      <td>../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las</td>\n", "      <td>1310.75</td>\n", "      <td>.las</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95</th>\n", "      <td>motali_de_castro</td>\n", "      <td>pointcloud</td>\n", "      <td>../../data/raw/motali_de_castro/pointcloud/area4_point.las</td>\n", "      <td>7182.78</td>\n", "      <td>.las</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>motali_de_castro</td>\n", "      <td>pointcloud</td>\n", "      <td>../../data/raw/motali_de_castro/pointcloud/Area2_point.las</td>\n", "      <td>10920.83</td>\n", "      <td>.las</td>\n", "    </tr>\n", "    <tr>\n", "      <th>97</th>\n", "      <td>motali_de_castro</td>\n", "      <td>pointcloud</td>\n", "      <td>../../data/raw/motali_de_castro/pointcloud/Area1_Part2_Points.las</td>\n", "      <td>14199.54</td>\n", "      <td>.las</td>\n", "    </tr>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>motali_de_castro</td>\n", "      <td>pointcloud</td>\n", "      <td>../../data/raw/motali_de_castro/pointcloud/Area1_part1_point.las</td>\n", "      <td>15590.83</td>\n", "      <td>.las</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   Site   Data_Type  \\\n", "15  sunstreams_mccarthy  pointcloud   \n", "30          althea_rpcs  pointcloud   \n", "31          althea_rpcs  pointcloud   \n", "95     motali_de_castro  pointcloud   \n", "96     motali_de_castro  pointcloud   \n", "97     motali_de_castro  pointcloud   \n", "98     motali_de_castro  pointcloud   \n", "\n", "                                                             File Path  \\\n", "15  ../../data/raw/sunstreams_mccarthy/pointcloud/Buffer_las(rev1).las   \n", "30           ../../data/raw/althea_rpcs/pointcloud/Point_Cloud_0.5.las   \n", "31               ../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las   \n", "95          ../../data/raw/motali_de_castro/pointcloud/area4_point.las   \n", "96          ../../data/raw/motali_de_castro/pointcloud/Area2_point.las   \n", "97   ../../data/raw/motali_de_castro/pointcloud/Area1_Part2_Points.las   \n", "98    ../../data/raw/motali_de_castro/pointcloud/Area1_part1_point.las   \n", "\n", "    Size (MB) Extension  \n", "15      36.13      .las  \n", "30     113.53      .las  \n", "31    1310.75      .las  \n", "95    7182.78      .las  \n", "96   10920.83      .las  \n", "97   14199.54      .las  \n", "98   15590.83      .las  "]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["pc_df"]}, {"cell_type": "markdown", "metadata": {"id": "9TuvdhxDDyNT"}, "source": ["#### CAD Data Analysis"]}, {"cell_type": "code", "execution_count": 47, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 175}, "id": "lhr08mcwuQCV", "outputId": "4e4e4266-b7cc-4dbd-db9f-d561396c7be7"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Site</th>\n", "      <th>has_.dwg</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>althea_rpcs</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>motali_de_castro</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>mudjar_enel</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>nortan_res</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>pian<PERSON>_<PERSON>_giorgio</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>sunstreams_mccarthy</td>\n", "      <td>No</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  Site has_.dwg\n", "0          althea_rpcs      Yes\n", "1     motali_de_castro      Yes\n", "2          mudjar_enel      Yes\n", "3           nortan_res      Yes\n", "4     piani_<PERSON>_giorgio      <PERSON>\n", "5  sunstreams_mccarthy       No"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["cad_df = all_files_df[all_files_df['Data_Type'] == 'cad']\n", "check_extension_availability(cad_df, extension=\".dwg\")"]}, {"cell_type": "code", "execution_count": 48, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "xGuXYsgyDyNT", "outputId": "938e4564-f3b4-4fde-d1ce-0262dc3b0a52"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Site</th>\n", "      <th>Data_Type</th>\n", "      <th>File Path</th>\n", "      <th><PERSON><PERSON> (MB)</th>\n", "      <th>Extension</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>nortan_res</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/nortan_res/cad/2024.09.12_Norton_PILE-PLAN-PB11.pdf</td>\n", "      <td>2.65</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>nortan_res</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/nortan_res/cad/2024.08.26 - Norton_PILE-SP_All Piles_Preliminary.dwg</td>\n", "      <td>2.05</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>mudjar_enel</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/mudjar_enel/cad/mudejar_ejemplo.kml</td>\n", "      <td>0.00</td>\n", "      <td>.kml</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>mudjar_enel</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/mudjar_enel/cad/gre.eec.d.21.es.p.11730.00.149.04.dwg</td>\n", "      <td>36.42</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>mudjar_enel</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/mudjar_enel/cad/psfv_-_mudéjar_1.kmz</td>\n", "      <td>1.51</td>\n", "      <td>.kmz</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>sunstreams_mccarthy</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/sunstreams_mccarthy/cad/PB01_-_POWERBLOCK_PLAN.pdf</td>\n", "      <td>3.59</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>sunstreams_mccarthy</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/sunstreams_mccarthy/cad/S-501_ FSLR S6+ Pier Tolerances  Rev.2 markup (1).pdf</td>\n", "      <td>0.61</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>sunstreams_mccarthy</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/sunstreams_mccarthy/cad/PB03_-_POWERBLOCK_PLAN.pdf</td>\n", "      <td>3.69</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>sunstreams_mccarthy</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/sunstreams_mccarthy/cad/SS4_BLOCKS_DATASEE_1to3 (1).kmz</td>\n", "      <td>0.00</td>\n", "      <td>.kmz</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>sunstreams_mccarthy</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/sunstreams_mccarthy/cad/PB02_-_POWERBLOCK_PLAN.json</td>\n", "      <td>0.01</td>\n", "      <td>.json</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>sunstreams_mccarthy</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/sunstreams_mccarthy/cad/PB01_-_POWERBLOCK_PLAN_mt.json</td>\n", "      <td>0.00</td>\n", "      <td>.json</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>sunstreams_mccarthy</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/sunstreams_mccarthy/cad/PB01_-_POWERBLOCK_PLAN.json</td>\n", "      <td>0.01</td>\n", "      <td>.json</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>sunstreams_mccarthy</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/sunstreams_mccarthy/cad/PB01_structured_output.csv</td>\n", "      <td>0.22</td>\n", "      <td>.csv</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>sunstreams_mccarthy</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/sunstreams_mccarthy/cad/S-203_ PIER PLAN BLOCK--3 Rev.2.pdf</td>\n", "      <td>0.78</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>sunstreams_mccarthy</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/sunstreams_mccarthy/cad/PB02_-_POWERBLOCK_PLAN.pdf</td>\n", "      <td>3.56</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>pian<PERSON>_<PERSON>_giorgio</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/piani_di_giorgio/cad/enel-6319_pian_di_giorgio-wdt-1.pdf</td>\n", "      <td>4.39</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>pian<PERSON>_<PERSON>_giorgio</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/piani_di_giorgio/cad/gre.eec.d.00.it.p.13316.00.066.05.pdf</td>\n", "      <td>13.68</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>pian<PERSON>_<PERSON>_giorgio</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/piani_di_giorgio/cad/gre.eec.d.00.it.p.13316.00.072.00_pv_field_-_electrical_layout_of_the_pv_park_and_cable_paths.pdf</td>\n", "      <td>44.47</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>pian<PERSON>_<PERSON>_giorgio</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/piani_di_giorgio/cad/Layout_Generale_Pi<PERSON>_<PERSON>_<PERSON>_R06.dwg</td>\n", "      <td>310.89</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>pian<PERSON>_<PERSON>_giorgio</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/piani_di_giorgio/cad/ENEL-6319_<PERSON><PERSON> di <PERSON>-S-1.dwg</td>\n", "      <td>7.48</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>pian<PERSON>_<PERSON>_giorgio</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/piani_di_giorgio/cad/gre.eec.d.00.it.p.13316.00.066.05_pv_field_-_general_layout_plan_of_pv_field.pdf</td>\n", "      <td>13.68</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>pian<PERSON>_<PERSON>_giorgio</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/piani_di_giorgio/cad/gre.eec.d.00.it.p.13316.00.067.00.pdf</td>\n", "      <td>5.62</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>pian<PERSON>_<PERSON>_giorgio</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/piani_di_giorgio/cad/gre.eec.d.00.it.p.13316.00.188.05_drawing_of_earthworks.pdf</td>\n", "      <td>150.85</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>althea_rpcs</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/althea_rpcs/cad/ForumEnergyPartners_Althea1&amp;2_Eng_RPCS-FHM_081924.pdf</td>\n", "      <td>1.49</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>althea_rpcs</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/althea_rpcs/cad/Althea- Full Site Flight Boundary and GCP.kml</td>\n", "      <td>0.01</td>\n", "      <td>.kml</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>althea_rpcs</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/althea_rpcs/cad/ForumEnergy_AltheaI&amp;II_Eng_Ampacity-PPP_031925.dwg</td>\n", "      <td>9.56</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>althea_rpcs</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/althea_rpcs/cad/ForumEnergyPartners_AltheaI&amp;II_Eng_RPCS-PPP_082824.dwg</td>\n", "      <td>9.61</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>althea_rpcs</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/althea_rpcs/cad/Forum_AltheaI&amp;II_CA_NXH_ENG_L3_03-11-2025_Rev 4.pdf</td>\n", "      <td>12.89</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/mdc_project.zip</td>\n", "      <td>322.08</td>\n", "      <td>.zip</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/dxf_inspector.py</td>\n", "      <td>0.00</td>\n", "      <td>.py</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/.DS_Store</td>\n", "      <td>0.01</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19.rar</td>\n", "      <td>18.57</td>\n", "      <td>.rar</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/dxf_pile.py</td>\n", "      <td>0.00</td>\n", "      <td>.py</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/extracted_piles.csv</td>\n", "      <td>0.00</td>\n", "      <td>.csv</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.118.00_Inquadramento Layout di impianto/GRE.EEC.D.00.IT.P.12645.00.118.05_Inquadramento Layout di impianto.pdf</td>\n", "      <td>15.36</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.118.00_Inquadramento Layout di impianto/GRE.EEC.D.00.IT.P.12645.00.118.05_Inquadramento Layout di impianto.dwg</td>\n", "      <td>12.23</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.165.00 - Plans and Details Access Road and Platform/GRE.EEC.D.00.IT.P.12645.00.165.00-Access Road and Platform.dwg</td>\n", "      <td>2.66</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.165.00 - Plans and Details Access Road and Platform/GRE.EEC.D.00.IT.P.12645.00.165.00-Access Road and Platform.pdf</td>\n", "      <td>6.58</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.172.00_Outside Fencing and Gates/GRE.EEC.D.00.IT.P.12645.00.172.01_Outside Fencing and Gates.dwg</td>\n", "      <td>29.65</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.172.00_Outside Fencing and Gates/GRE.EEC.D.00.IT.P.12645.00.172.01_Outside Fencing and Gates_Dettagli.pdf</td>\n", "      <td>2.63</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.172.00_Outside Fencing and Gates/GRE.EEC.D.00.IT.P.12645.00.172.01_Outside Fencing and Gates_Dettagli.dwg</td>\n", "      <td>1.59</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.166.00 - Civil Works - Drawing of Earthworks/GRE.EEC.D.00.IT.P.12645.00.166.02 - Civil Works - Drawing of Earthworks2_2.dwg</td>\n", "      <td>2.87</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.166.00 - Civil Works - Drawing of Earthworks/GRE.EEC.D.00.IT.P.12645.00.166.02 - Civil Works - Drawing of Earthworks1_2.dwg</td>\n", "      <td>2.01</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.166.00 - Civil Works - Drawing of Earthworks/GRE.EEC.D.00.IT.P.12645.00.166.02 - Civil Works - Drawing of Earthworks.pdf</td>\n", "      <td>7.46</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.166.00 - Civil Works - Drawing of Earthworks/p.py</td>\n", "      <td>0.00</td>\n", "      <td>.py</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.114.00_Inquadramento catastale impianto/GRE.EEC.D.00.IT.P.12645.00.114.03_Inquadramento catastale impianto.dwg</td>\n", "      <td>28.30</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.114.00_Inquadramento catastale impianto/GRE.EEC.D.00.IT.P.12645.00.114.03_Inquadramento catastale impianto.pdf</td>\n", "      <td>1.15</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.116.00_Schema Elettrico Unifilare/GRE.EEC.D.00.IT.P.12645.00.116.05.dwg</td>\n", "      <td>0.80</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.116.00_Schema Elettrico Unifilare/GRE.EEC.D.00.IT.P.12645.00.116.05_SLD.pdf</td>\n", "      <td>1.51</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/.DS_Store</td>\n", "      <td>0.01</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/.DS_Store</td>\n", "      <td>0.01</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.D.00.IT.P.12645.00.134.00.pdf</td>\n", "      <td>2.59</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/Plinto Area - Alzamento a 700mm.pdf</td>\n", "      <td>1.33</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.D.00.IT.P.12645.00.327.00.pdf</td>\n", "      <td>0.79</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/Plinto - Alzamento a 700mm.pdf</td>\n", "      <td>0.10</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.D.00.IT.P.12645.00.322.00.pdf</td>\n", "      <td>6.32</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg</td>\n", "      <td>4.64</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.R.00.IT.P.12645.00.133.00.pdf</td>\n", "      <td>5.49</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.pdf</td>\n", "      <td>2.22</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/DXF/dxf_inspector.py</td>\n", "      <td>0.00</td>\n", "      <td>.py</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/DXF/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf</td>\n", "      <td>15.52</td>\n", "      <td>.dxf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/DXF/dxf_inspector_1.py</td>\n", "      <td>0.02</td>\n", "      <td>.py</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/DXF/pile_centers.csv</td>\n", "      <td>0.00</td>\n", "      <td>.csv</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/DXF/pile_centers.py</td>\n", "      <td>0.00</td>\n", "      <td>.py</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/DXF/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP_pile_data.json</td>\n", "      <td>0.25</td>\n", "      <td>.json</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.123.00_Generale - Posizione Cabina di consegna/GRE.EEC.D.00.IT.P.12645.00.123.01_Posizione Cabina Consegna.dwg</td>\n", "      <td>28.31</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.123.00_Generale - Posizione Cabina di consegna/GRE.EEC.D.00.IT.P.12645.00.123.01_Posizione Cabina Consegna.pdf</td>\n", "      <td>1.13</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.160.00_Corografia con punto di connessione/GRE.EEC.D.00.IT.P.12645.00.160.04_Corografia con punto di connessione.dwg</td>\n", "      <td>0.49</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.160.00_Corografia con punto di connessione/GRE.EEC.D.00.IT.P.12645.00.160.04_Corografia con punto di connessione.pdf</td>\n", "      <td>3.59</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>74</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.115.00_Sezioni cavidotti impianto/GRE.EEC.D.00.IT.P.12645.00.115.04_Sezioni cavidotti impianto.pdf</td>\n", "      <td>12.52</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.115.00_Sezioni cavidotti impianto/GRE.EEC.D.00.IT.P.12645.00.115.04_Sezioni cavidotti impianto.dwg</td>\n", "      <td>0.76</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.253.00_Earthing System Layout/GRE.EEC.D.00.IT.P.12645.00.253.02_Earthing System Layout.pdf</td>\n", "      <td>22.69</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.253.00_Earthing System Layout/GRE.EEC.D.00.IT.P.12645.00.253.02_Earthing System Layout.dwg</td>\n", "      <td>12.89</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.191.00 - TC Single Line Diagram/GRE.EEC.D.00.IT.P.12645.00.191.01 - TC Single Line Diagram.dwg</td>\n", "      <td>0.44</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.191.00 - TC Single Line Diagram/GRE.EEC.D.00.IT.P.12645.00.191.01 - TC Single Line Diagram.pdf</td>\n", "      <td>0.44</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps/Flow Depth at cell_AO_2024.03.25_post.dwg</td>\n", "      <td>1.95</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps.pdf</td>\n", "      <td>33.69</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps-Post.pdf</td>\n", "      <td>1.86</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>83</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps-Ante.pdf</td>\n", "      <td>3.20</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps/Flow Depth at cell_AO_2024.03.25_ante.dwg</td>\n", "      <td>1.15</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>85</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.249.00_Electrical Layout/GRE.EEC.D.00.IT.P.12645.00.249.02_Electrical Layout.dwg</td>\n", "      <td>11.86</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>86</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.249.00_Electrical Layout/GRE.EEC.D.00.IT.P.12645.00.249.02_Electrical Layout.pdf</td>\n", "      <td>86.04</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.119.00 - General Layout with Meteo Station and Sensors Placement/GRE.EEC.D.00.IT.P.12645.00.119.05 -General Layout with Meteo Station and  Sensor.pdf</td>\n", "      <td>4.05</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.119.00 - General Layout with Meteo Station and Sensors Placement/GRE.EEC.D.00.IT.P.12645.00.119.05 -General Layout with Meteo Station and  Sensor.dwg</td>\n", "      <td>0.84</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>89</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.121.00_Configurazione del Parco Fotovoltaico/GRE.EEC.D.00.IT.P.12645.00.121.04_Configurazione del parco fotovoltaico.pdf</td>\n", "      <td>1.71</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.121.00_Configurazione del Parco Fotovoltaico/GRE.EEC.D.00.IT.P.12645.00.121.04_Configurazione del parco fotovoltaico.docx</td>\n", "      <td>8.92</td>\n", "      <td>.docx</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.R.00.IT.P.12645.00.256.00_Table of All electrical cables/GRE.EEC.R.00.IT.P.12645.00.256.03_Table of All Electric Cables.docx</td>\n", "      <td>5.22</td>\n", "      <td>.docx</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.R.00.IT.P.12645.00.256.00_Table of All electrical cables/GRE.EEC.R.00.IT.P.12645.00.256.03_Table of All Electric Cables.pdf</td>\n", "      <td>2.44</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.169.00_General Layout and Detail of Cabins Foundations/GRE.EEC.D.00.IT.P.12645.00.169.01_General Layout and Detail of Cabins Foundations.dwg</td>\n", "      <td>10.64</td>\n", "      <td>.dwg</td>\n", "    </tr>\n", "    <tr>\n", "      <th>94</th>\n", "      <td>motali_de_castro</td>\n", "      <td>cad</td>\n", "      <td>../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.169.00_General Layout and Detail of Cabins Foundations/GRE.EEC.D.00.IT.P.12645.00.169.01_General Layout and Detail of Cabins Foundations.pdf</td>\n", "      <td>3.95</td>\n", "      <td>.pdf</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   Site Data_Type  \\\n", "0            nortan_res       cad   \n", "1            nortan_res       cad   \n", "2           mudjar_enel       cad   \n", "3           mudjar_enel       cad   \n", "4           mudjar_enel       cad   \n", "5   sunstreams_mccarthy       cad   \n", "6   sunstreams_mccarthy       cad   \n", "7   sunstreams_mccarthy       cad   \n", "8   sunstreams_mccarthy       cad   \n", "9   sunstreams_mccarthy       cad   \n", "10  sunstreams_mccarthy       cad   \n", "11  sunstreams_mccarthy       cad   \n", "12  sunstreams_mccarthy       cad   \n", "13  sunstreams_mccarthy       cad   \n", "14  sunstreams_mccarthy       cad   \n", "17     piani_di_giorgio       cad   \n", "18     piani_di_giorgio       cad   \n", "19     piani_di_giorgio       cad   \n", "20     piani_di_giorgio       cad   \n", "21     piani_di_giorgio       cad   \n", "22     piani_di_giorgio       cad   \n", "23     piani_di_giorgio       cad   \n", "24     piani_di_giorgio       cad   \n", "25          althea_rpcs       cad   \n", "26          althea_rpcs       cad   \n", "27          althea_rpcs       cad   \n", "28          althea_rpcs       cad   \n", "29          althea_rpcs       cad   \n", "33     motali_de_castro       cad   \n", "34     motali_de_castro       cad   \n", "35     motali_de_castro       cad   \n", "36     motali_de_castro       cad   \n", "37     motali_de_castro       cad   \n", "38     motali_de_castro       cad   \n", "39     motali_de_castro       cad   \n", "40     motali_de_castro       cad   \n", "41     motali_de_castro       cad   \n", "42     motali_de_castro       cad   \n", "43     motali_de_castro       cad   \n", "44     motali_de_castro       cad   \n", "45     motali_de_castro       cad   \n", "46     motali_de_castro       cad   \n", "47     motali_de_castro       cad   \n", "48     motali_de_castro       cad   \n", "49     motali_de_castro       cad   \n", "50     motali_de_castro       cad   \n", "51     motali_de_castro       cad   \n", "52     motali_de_castro       cad   \n", "53     motali_de_castro       cad   \n", "54     motali_de_castro       cad   \n", "55     motali_de_castro       cad   \n", "56     motali_de_castro       cad   \n", "57     motali_de_castro       cad   \n", "58     motali_de_castro       cad   \n", "59     motali_de_castro       cad   \n", "60     motali_de_castro       cad   \n", "61     motali_de_castro       cad   \n", "62     motali_de_castro       cad   \n", "63     motali_de_castro       cad   \n", "64     motali_de_castro       cad   \n", "65     motali_de_castro       cad   \n", "66     motali_de_castro       cad   \n", "67     motali_de_castro       cad   \n", "68     motali_de_castro       cad   \n", "69     motali_de_castro       cad   \n", "70     motali_de_castro       cad   \n", "71     motali_de_castro       cad   \n", "72     motali_de_castro       cad   \n", "73     motali_de_castro       cad   \n", "74     motali_de_castro       cad   \n", "75     motali_de_castro       cad   \n", "76     motali_de_castro       cad   \n", "77     motali_de_castro       cad   \n", "78     motali_de_castro       cad   \n", "79     motali_de_castro       cad   \n", "80     motali_de_castro       cad   \n", "81     motali_de_castro       cad   \n", "82     motali_de_castro       cad   \n", "83     motali_de_castro       cad   \n", "84     motali_de_castro       cad   \n", "85     motali_de_castro       cad   \n", "86     motali_de_castro       cad   \n", "87     motali_de_castro       cad   \n", "88     motali_de_castro       cad   \n", "89     motali_de_castro       cad   \n", "90     motali_de_castro       cad   \n", "91     motali_de_castro       cad   \n", "92     motali_de_castro       cad   \n", "93     motali_de_castro       cad   \n", "94     motali_de_castro       cad   \n", "\n", "                                                                                                                                                                                                               File Path  \\\n", "0                                                                                                                                                     ../../data/raw/nortan_res/cad/2024.09.12_Norton_PILE-PLAN-PB11.pdf   \n", "1                                                                                                                                    ../../data/raw/nortan_res/cad/2024.08.26 - Norton_PILE-SP_All Piles_Preliminary.dwg   \n", "2                                                                                                                                                                     ../../data/raw/mudjar_enel/cad/mudejar_ejemplo.kml   \n", "3                                                                                                                                                   ../../data/raw/mudjar_enel/cad/gre.eec.d.21.es.p.11730.00.149.04.dwg   \n", "4                                                                                                                                                                    ../../data/raw/mudjar_enel/cad/psfv_-_mudéjar_1.kmz   \n", "5                                                                                                                                                      ../../data/raw/sunstreams_mccarthy/cad/PB01_-_POWERBLOCK_PLAN.pdf   \n", "6                                                                                                                           ../../data/raw/sunstreams_mccarthy/cad/S-501_ FSLR S6+ Pier Tolerances  Rev.2 markup (1).pdf   \n", "7                                                                                                                                                      ../../data/raw/sunstreams_mccarthy/cad/PB03_-_POWERBLOCK_PLAN.pdf   \n", "8                                                                                                                                                 ../../data/raw/sunstreams_mccarthy/cad/SS4_BLOCKS_DATASEE_1to3 (1).kmz   \n", "9                                                                                                                                                     ../../data/raw/sunstreams_mccarthy/cad/PB02_-_POWERBLOCK_PLAN.json   \n", "10                                                                                                                                                 ../../data/raw/sunstreams_mccarthy/cad/PB01_-_POWERBLOCK_PLAN_mt.json   \n", "11                                                                                                                                                    ../../data/raw/sunstreams_mccarthy/cad/PB01_-_POWERBLOCK_PLAN.json   \n", "12                                                                                                                                                     ../../data/raw/sunstreams_mccarthy/cad/PB01_structured_output.csv   \n", "13                                                                                                                                            ../../data/raw/sunstreams_mccarthy/cad/S-203_ PIER PLAN BLOCK--3 Rev.2.pdf   \n", "14                                                                                                                                                     ../../data/raw/sunstreams_mccarthy/cad/PB02_-_POWERBLOCK_PLAN.pdf   \n", "17                                                                                                                                               ../../data/raw/piani_di_giorgio/cad/enel-6319_pian_di_giorgio-wdt-1.pdf   \n", "18                                                                                                                                             ../../data/raw/piani_di_giorgio/cad/gre.eec.d.00.it.p.13316.00.066.05.pdf   \n", "19                                                                                 ../../data/raw/piani_di_giorgio/cad/gre.eec.d.00.it.p.13316.00.072.00_pv_field_-_electrical_layout_of_the_pv_park_and_cable_paths.pdf   \n", "20                                                                                                                                           ../../data/raw/piani_di_giorgio/cad/Layout_Generale_Pi<PERSON>_<PERSON>_<PERSON>_R06.dwg   \n", "21                                                                                                                                                 ../../data/raw/piani_di_giorgio/cad/ENEL-6319_<PERSON><PERSON> di <PERSON>-S-1.dwg   \n", "22                                                                                                  ../../data/raw/piani_di_giorgio/cad/gre.eec.d.00.it.p.13316.00.066.05_pv_field_-_general_layout_plan_of_pv_field.pdf   \n", "23                                                                                                                                             ../../data/raw/piani_di_giorgio/cad/gre.eec.d.00.it.p.13316.00.067.00.pdf   \n", "24                                                                                                                       ../../data/raw/piani_di_giorgio/cad/gre.eec.d.00.it.p.13316.00.188.05_drawing_of_earthworks.pdf   \n", "25                                                                                                                                  ../../data/raw/althea_rpcs/cad/ForumEnergyPartners_Althea1&2_Eng_RPCS-FHM_081924.pdf   \n", "26                                                                                                                                          ../../data/raw/althea_rpcs/cad/Althea- Full Site Flight Boundary and GCP.kml   \n", "27                                                                                                                                     ../../data/raw/althea_rpcs/cad/ForumEnergy_AltheaI&II_Eng_Ampacity-PPP_031925.dwg   \n", "28                                                                                                                                 ../../data/raw/althea_rpcs/cad/ForumEnergyPartners_AltheaI&II_Eng_RPCS-PPP_082824.dwg   \n", "29                                                                                                                                    ../../data/raw/althea_rpcs/cad/Forum_AltheaI&II_CA_NXH_ENG_L3_03-11-2025_Rev 4.pdf   \n", "33                                                                                                                                                                   ../../data/raw/motali_de_castro/cad/mdc_project.zip   \n", "34                                                                                                                                                                  ../../data/raw/motali_de_castro/cad/dxf_inspector.py   \n", "35                                                                                                                                                                         ../../data/raw/motali_de_castro/cad/.DS_Store   \n", "36                                                                                                                                                           ../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19.rar   \n", "37                                                                                                                                                                       ../../data/raw/motali_de_castro/cad/dxf_pile.py   \n", "38                                                                                                                                                               ../../data/raw/motali_de_castro/cad/extracted_piles.csv   \n", "39                                         ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.118.00_Inquadramento Layout di impianto/GRE.EEC.D.00.IT.P.12645.00.118.05_Inquadramento Layout di impianto.pdf   \n", "40                                         ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.118.00_Inquadramento Layout di impianto/GRE.EEC.D.00.IT.P.12645.00.118.05_Inquadramento Layout di impianto.dwg   \n", "41                                     ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.165.00 - Plans and Details Access Road and Platform/GRE.EEC.D.00.IT.P.12645.00.165.00-Access Road and Platform.dwg   \n", "42                                     ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.165.00 - Plans and Details Access Road and Platform/GRE.EEC.D.00.IT.P.12645.00.165.00-Access Road and Platform.pdf   \n", "43                                                       ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.172.00_Outside Fencing and Gates/GRE.EEC.D.00.IT.P.12645.00.172.01_Outside Fencing and Gates.dwg   \n", "44                                              ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.172.00_Outside Fencing and Gates/GRE.EEC.D.00.IT.P.12645.00.172.01_Outside Fencing and Gates_Dettagli.pdf   \n", "45                                              ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.172.00_Outside Fencing and Gates/GRE.EEC.D.00.IT.P.12645.00.172.01_Outside Fencing and Gates_Dettagli.dwg   \n", "46                            ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.166.00 - Civil Works - Drawing of Earthworks/GRE.EEC.D.00.IT.P.12645.00.166.02 - Civil Works - Drawing of Earthworks2_2.dwg   \n", "47                            ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.166.00 - Civil Works - Drawing of Earthworks/GRE.EEC.D.00.IT.P.12645.00.166.02 - Civil Works - Drawing of Earthworks1_2.dwg   \n", "48                               ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.166.00 - Civil Works - Drawing of Earthworks/GRE.EEC.D.00.IT.P.12645.00.166.02 - Civil Works - Drawing of Earthworks.pdf   \n", "49                                                                                                      ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.166.00 - Civil Works - Drawing of Earthworks/p.py   \n", "50                                         ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.114.00_Inquadramento catastale impianto/GRE.EEC.D.00.IT.P.12645.00.114.03_Inquadramento catastale impianto.dwg   \n", "51                                         ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.114.00_Inquadramento catastale impianto/GRE.EEC.D.00.IT.P.12645.00.114.03_Inquadramento catastale impianto.pdf   \n", "52                                                                                ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.116.00_Schema Elettrico Unifilare/GRE.EEC.D.00.IT.P.12645.00.116.05.dwg   \n", "53                                                                            ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.116.00_Schema Elettrico Unifilare/GRE.EEC.D.00.IT.P.12645.00.116.05_SLD.pdf   \n", "54                                                                                                                                                     ../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/.DS_Store   \n", "55                                                                                                                                         ../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/.DS_Store   \n", "56                                                                                                             ../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.D.00.IT.P.12645.00.134.00.pdf   \n", "57                                                                                                               ../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/Plinto Area - Alzamento a 700mm.pdf   \n", "58                                                                                                             ../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.D.00.IT.P.12645.00.327.00.pdf   \n", "59                                                                                                                    ../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/Plinto - Alzamento a 700mm.pdf   \n", "60                                                                                                             ../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.D.00.IT.P.12645.00.322.00.pdf   \n", "61                                                                                                         ../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg   \n", "62                                                                                                             ../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.R.00.IT.P.12645.00.133.00.pdf   \n", "63                                                                                                         ../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.pdf   \n", "64                                                                                                                              ../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/DXF/dxf_inspector.py   \n", "65                                                                                                     ../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/DXF/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf   \n", "66                                                                                                                            ../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/DXF/dxf_inspector_1.py   \n", "67                                                                                                                              ../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/DXF/pile_centers.csv   \n", "68                                                                                                                               ../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/DXF/pile_centers.py   \n", "69                                                                                          ../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/DXF/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP_pile_data.json   \n", "70                                         ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.123.00_Generale - Posizione Cabina di consegna/GRE.EEC.D.00.IT.P.12645.00.123.01_Posizione Cabina Consegna.dwg   \n", "71                                         ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.123.00_Generale - Posizione Cabina di consegna/GRE.EEC.D.00.IT.P.12645.00.123.01_Posizione Cabina Consegna.pdf   \n", "72                                   ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.160.00_Corografia con punto di connessione/GRE.EEC.D.00.IT.P.12645.00.160.04_Corografia con punto di connessione.dwg   \n", "73                                   ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.160.00_Corografia con punto di connessione/GRE.EEC.D.00.IT.P.12645.00.160.04_Corografia con punto di connessione.pdf   \n", "74                                                     ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.115.00_Sezioni cavidotti impianto/GRE.EEC.D.00.IT.P.12645.00.115.04_Sezioni cavidotti impianto.pdf   \n", "75                                                     ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.115.00_Sezioni cavidotti impianto/GRE.EEC.D.00.IT.P.12645.00.115.04_Sezioni cavidotti impianto.dwg   \n", "76                                                             ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.253.00_Earthing System Layout/GRE.EEC.D.00.IT.P.12645.00.253.02_Earthing System Layout.pdf   \n", "77                                                             ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.253.00_Earthing System Layout/GRE.EEC.D.00.IT.P.12645.00.253.02_Earthing System Layout.dwg   \n", "78                                                         ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.191.00 - TC Single Line Diagram/GRE.EEC.D.00.IT.P.12645.00.191.01 - TC Single Line Diagram.dwg   \n", "79                                                         ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.191.00 - TC Single Line Diagram/GRE.EEC.D.00.IT.P.12645.00.191.01 - TC Single Line Diagram.pdf   \n", "80                                                                                       ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps/Flow Depth at cell_AO_2024.03.25_post.dwg   \n", "81                                                                           ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps.pdf   \n", "82                                                                      ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps-Post.pdf   \n", "83                                                                      ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps-Ante.pdf   \n", "84                                                                                       ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.131.00 - Flooding Maps/Flow Depth at cell_AO_2024.03.25_ante.dwg   \n", "85                                                                       ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.249.00_Electrical Layout/GRE.EEC.D.00.IT.P.12645.00.249.02_Electrical Layout.dwg   \n", "86                                                                       ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.249.00_Electrical Layout/GRE.EEC.D.00.IT.P.12645.00.249.02_Electrical Layout.pdf   \n", "87  ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.119.00 - General Layout with Meteo Station and Sensors Placement/GRE.EEC.D.00.IT.P.12645.00.119.05 -General Layout with Meteo Station and  Sensor.pdf   \n", "88  ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.119.00 - General Layout with Meteo Station and Sensors Placement/GRE.EEC.D.00.IT.P.12645.00.119.05 -General Layout with Meteo Station and  Sensor.dwg   \n", "89                               ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.121.00_Configurazione del Parco Fotovoltaico/GRE.EEC.D.00.IT.P.12645.00.121.04_Configurazione del parco fotovoltaico.pdf   \n", "90                              ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.121.00_Configurazione del Parco Fotovoltaico/GRE.EEC.D.00.IT.P.12645.00.121.04_Configurazione del parco fotovoltaico.docx   \n", "91                                              ../../data/raw/motali_de_castro/cad/GRE.EEC.R.00.IT.P.12645.00.256.00_Table of All electrical cables/GRE.EEC.R.00.IT.P.12645.00.256.03_Table of All Electric Cables.docx   \n", "92                                               ../../data/raw/motali_de_castro/cad/GRE.EEC.R.00.IT.P.12645.00.256.00_Table of All electrical cables/GRE.EEC.R.00.IT.P.12645.00.256.03_Table of All Electric Cables.pdf   \n", "93           ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.169.00_General Layout and Detail of Cabins Foundations/GRE.EEC.D.00.IT.P.12645.00.169.01_General Layout and Detail of Cabins Foundations.dwg   \n", "94           ../../data/raw/motali_de_castro/cad/GRE.EEC.D.00.IT.P.12645.00.169.00_General Layout and Detail of Cabins Foundations/GRE.EEC.D.00.IT.P.12645.00.169.01_General Layout and Detail of Cabins Foundations.pdf   \n", "\n", "    Size (MB) Extension  \n", "0        2.65      .pdf  \n", "1        2.05      .dwg  \n", "2        0.00      .kml  \n", "3       36.42      .dwg  \n", "4        1.51      .kmz  \n", "5        3.59      .pdf  \n", "6        0.61      .pdf  \n", "7        3.69      .pdf  \n", "8        0.00      .kmz  \n", "9        0.01     .json  \n", "10       0.00     .json  \n", "11       0.01     .json  \n", "12       0.22      .csv  \n", "13       0.78      .pdf  \n", "14       3.56      .pdf  \n", "17       4.39      .pdf  \n", "18      13.68      .pdf  \n", "19      44.47      .pdf  \n", "20     310.89      .dwg  \n", "21       7.48      .dwg  \n", "22      13.68      .pdf  \n", "23       5.62      .pdf  \n", "24     150.85      .pdf  \n", "25       1.49      .pdf  \n", "26       0.01      .kml  \n", "27       9.56      .dwg  \n", "28       9.61      .dwg  \n", "29      12.89      .pdf  \n", "33     322.08      .zip  \n", "34       0.00       .py  \n", "35       0.01            \n", "36      18.57      .rar  \n", "37       0.00       .py  \n", "38       0.00      .csv  \n", "39      15.36      .pdf  \n", "40      12.23      .dwg  \n", "41       2.66      .dwg  \n", "42       6.58      .pdf  \n", "43      29.65      .dwg  \n", "44       2.63      .pdf  \n", "45       1.59      .dwg  \n", "46       2.87      .dwg  \n", "47       2.01      .dwg  \n", "48       7.46      .pdf  \n", "49       0.00       .py  \n", "50      28.30      .dwg  \n", "51       1.15      .pdf  \n", "52       0.80      .dwg  \n", "53       1.51      .pdf  \n", "54       0.01            \n", "55       0.01            \n", "56       2.59      .pdf  \n", "57       1.33      .pdf  \n", "58       0.79      .pdf  \n", "59       0.10      .pdf  \n", "60       6.32      .pdf  \n", "61       4.64      .dwg  \n", "62       5.49      .pdf  \n", "63       2.22      .pdf  \n", "64       0.00       .py  \n", "65      15.52      .dxf  \n", "66       0.02       .py  \n", "67       0.00      .csv  \n", "68       0.00       .py  \n", "69       0.25     .json  \n", "70      28.31      .dwg  \n", "71       1.13      .pdf  \n", "72       0.49      .dwg  \n", "73       3.59      .pdf  \n", "74      12.52      .pdf  \n", "75       0.76      .dwg  \n", "76      22.69      .pdf  \n", "77      12.89      .dwg  \n", "78       0.44      .dwg  \n", "79       0.44      .pdf  \n", "80       1.95      .dwg  \n", "81      33.69      .pdf  \n", "82       1.86      .pdf  \n", "83       3.20      .pdf  \n", "84       1.15      .dwg  \n", "85      11.86      .dwg  \n", "86      86.04      .pdf  \n", "87       4.05      .pdf  \n", "88       0.84      .dwg  \n", "89       1.71      .pdf  \n", "90       8.92     .docx  \n", "91       5.22     .docx  \n", "92       2.44      .pdf  \n", "93      10.64      .dwg  \n", "94       3.95      .pdf  "]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["cad_df"]}, {"cell_type": "markdown", "metadata": {"id": "N6tVVuF_DyNT"}, "source": ["#### Orthomosaic Analysis"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Site</th>\n", "      <th>has_.tif</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>althea_rpcs</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>motali_de_castro</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>sunstreams_mccarthy</td>\n", "      <td>No</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  Site has_.tif\n", "0          althea_rpcs       No\n", "1     motali_de_castro      Yes\n", "2  sunstreams_mccarthy       No"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["ortho_df = all_files_df[all_files_df['Data_Type'] == 'ortho']\n", "check_extension_availability(ortho_df, \".tif\")"]}, {"cell_type": "code", "execution_count": 50, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "QWPFdkzsL4gY", "outputId": "58439b74-c14b-46a0-a0a0-8e0ed42a61a9"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Site</th>\n", "      <th>Data_Type</th>\n", "      <th>File Path</th>\n", "      <th><PERSON><PERSON> (MB)</th>\n", "      <th>Extension</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>sunstreams_mccarthy</td>\n", "      <td>ortho</td>\n", "      <td>../../data/raw/sunstreams_mccarthy/ortho/RGB_Ortho.zip</td>\n", "      <td>6792.98</td>\n", "      <td>.zip</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>althea_rpcs</td>\n", "      <td>ortho</td>\n", "      <td>../../data/raw/althea_rpcs/ortho/result.zip</td>\n", "      <td>2300.39</td>\n", "      <td>.zip</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>motali_de_castro</td>\n", "      <td>ortho</td>\n", "      <td>../../data/raw/motali_de_castro/ortho/Area4_Ortho_SCQM.tif</td>\n", "      <td>659.82</td>\n", "      <td>.tif</td>\n", "    </tr>\n", "    <tr>\n", "      <th>100</th>\n", "      <td>motali_de_castro</td>\n", "      <td>ortho</td>\n", "      <td>../../data/raw/motali_de_castro/ortho/Area2_Ortho.tif</td>\n", "      <td>1010.96</td>\n", "      <td>.tif</td>\n", "    </tr>\n", "    <tr>\n", "      <th>101</th>\n", "      <td>motali_de_castro</td>\n", "      <td>ortho</td>\n", "      <td>../../data/raw/motali_de_castro/ortho/Area1_part2_ortho.tif</td>\n", "      <td>1683.25</td>\n", "      <td>.tif</td>\n", "    </tr>\n", "    <tr>\n", "      <th>102</th>\n", "      <td>motali_de_castro</td>\n", "      <td>ortho</td>\n", "      <td>../../data/raw/motali_de_castro/ortho/Area1_Point_ortho.tif</td>\n", "      <td>1403.07</td>\n", "      <td>.tif</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    Site Data_Type  \\\n", "16   sunstreams_mccarthy     ortho   \n", "32           althea_rpcs     ortho   \n", "99      motali_de_castro     ortho   \n", "100     motali_de_castro     ortho   \n", "101     motali_de_castro     ortho   \n", "102     motali_de_castro     ortho   \n", "\n", "                                                       File Path  Size (MB)  \\\n", "16        ../../data/raw/sunstreams_mccarthy/ortho/RGB_Ortho.zip    6792.98   \n", "32                   ../../data/raw/althea_rpcs/ortho/result.zip    2300.39   \n", "99    ../../data/raw/motali_de_castro/ortho/Area4_Ortho_SCQM.tif     659.82   \n", "100        ../../data/raw/motali_de_castro/ortho/Area2_Ortho.tif    1010.96   \n", "101  ../../data/raw/motali_de_castro/ortho/Area1_part2_ortho.tif    1683.25   \n", "102  ../../data/raw/motali_de_castro/ortho/Area1_Point_ortho.tif    1403.07   \n", "\n", "    Extension  \n", "16       .zip  \n", "32       .zip  \n", "99       .tif  \n", "100      .tif  \n", "101      .tif  \n", "102      .tif  "]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["ortho_df"]}, {"cell_type": "markdown", "metadata": {"id": "BPSnFkiDDyNh"}, "source": ["## Comprehensive Data Compilation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Unified Data Summary Table\n", "\n", "Create a comprehensive summary table combining all analysis results:"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["def summarize_data(df, prefix):\n", "    summary = df.groupby('Site').agg({\n", "        'Size (MB)': ['sum', 'count'],\n", "        'Extension': lambda x: ', '.join(sorted(x.unique()))\n", "    }).round(2)\n", "    summary.columns = [f\"{prefix}_Size_MB\", f\"{prefix}_File_Count\", f\"{prefix}_Extensions\"]\n", "    return summary\n"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PC_Size_MB</th>\n", "      <th>PC_File_Count</th>\n", "      <th>PC_Extensions</th>\n", "      <th>CAD_Size_MB</th>\n", "      <th>CAD_File_Count</th>\n", "      <th>CAD_Extensions</th>\n", "      <th>Ortho_Size_MB</th>\n", "      <th>Ortho_File_Count</th>\n", "      <th>Ortho_Extensions</th>\n", "      <th>Total_Size_GB</th>\n", "      <th>Data_Completeness</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Site</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>althea_rpcs</th>\n", "      <td>1424.28</td>\n", "      <td>2</td>\n", "      <td>.las</td>\n", "      <td>33.56</td>\n", "      <td>5</td>\n", "      <td>.dwg, .kml, .pdf</td>\n", "      <td>2300.39</td>\n", "      <td>1</td>\n", "      <td>.zip</td>\n", "      <td>3.67</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>motali_de_castro</th>\n", "      <td>47893.98</td>\n", "      <td>4</td>\n", "      <td>.las</td>\n", "      <td>755.53</td>\n", "      <td>62</td>\n", "      <td>, .csv, .docx, .dwg, .dxf, .json, .pdf, .py, .rar, .zip</td>\n", "      <td>4757.10</td>\n", "      <td>4</td>\n", "      <td>.tif</td>\n", "      <td>52.15</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sunstreams_mccarthy</th>\n", "      <td>36.13</td>\n", "      <td>1</td>\n", "      <td>.las</td>\n", "      <td>12.47</td>\n", "      <td>10</td>\n", "      <td>.csv, .json, .kmz, .pdf</td>\n", "      <td>6792.98</td>\n", "      <td>1</td>\n", "      <td>.zip</td>\n", "      <td>6.68</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mudjar_enel</th>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>N/A</td>\n", "      <td>37.93</td>\n", "      <td>3</td>\n", "      <td>.dwg, .kml, .kmz</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>N/A</td>\n", "      <td>0.04</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>nortan_res</th>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>N/A</td>\n", "      <td>4.70</td>\n", "      <td>2</td>\n", "      <td>.dwg, .pdf</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>N/A</td>\n", "      <td>0.00</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>pian<PERSON>_<PERSON>_giorgio</th>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>N/A</td>\n", "      <td>551.06</td>\n", "      <td>8</td>\n", "      <td>.dwg, .pdf</td>\n", "      <td>0.00</td>\n", "      <td>0</td>\n", "      <td>N/A</td>\n", "      <td>0.54</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     PC_Size_MB  PC_File_Count PC_Extensions  CAD_Size_MB  \\\n", "Site                                                                        \n", "althea_rpcs             1424.28              2          .las        33.56   \n", "motali_de_castro       47893.98              4          .las       755.53   \n", "sunstreams_mccarthy       36.13              1          .las        12.47   \n", "mudjar_enel                0.00              0           N/A        37.93   \n", "nortan_res                 0.00              0           N/A         4.70   \n", "pian<PERSON>_di_giorgio           0.00              0           N/A       551.06   \n", "\n", "                     CAD_File_Count  \\\n", "Site                                  \n", "althea_rpcs                       5   \n", "motali_de_castro                 62   \n", "sunstreams_mccarthy              10   \n", "mudjar_enel                       3   \n", "nortan_res                        2   \n", "pian<PERSON>_<PERSON>_giorgio                  8   \n", "\n", "                                                              CAD_Extensions  \\\n", "Site                                                                           \n", "althea_rpcs                                                 .dwg, .kml, .pdf   \n", "motali_de_castro     , .csv, .docx, .dwg, .dxf, .json, .pdf, .py, .rar, .zip   \n", "sunstreams_mccarthy                                  .csv, .json, .kmz, .pdf   \n", "mudjar_enel                                                 .dwg, .kml, .kmz   \n", "nortan_res                                                        .dwg, .pdf   \n", "piani_di_giorgio                                                  .dwg, .pdf   \n", "\n", "                     Ortho_Size_MB  Ortho_File_Count Ortho_Extensions  \\\n", "Site                                                                    \n", "althea_rpcs                2300.39                 1             .zip   \n", "motali_de_castro           4757.10                 4             .tif   \n", "sunstreams_mccarthy        6792.98                 1             .zip   \n", "mudjar_enel                   0.00                 0              N/A   \n", "nortan_res                    0.00                 0              N/A   \n", "pian<PERSON>_di_giorgio              0.00                 0              N/A   \n", "\n", "                     Total_Size_GB  Data_Completeness  \n", "Site                                                   \n", "althea_rpcs                   3.67                  3  \n", "motali_de_castro             52.15                  3  \n", "sunstreams_mccarthy           6.68                  3  \n", "mudjar_enel                   0.04                  1  \n", "nortan_res                    0.00                  1  \n", "pian<PERSON>_<PERSON>_giorgio              0.54                  1  "]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["pc_summary = summarize_data(pc_df, \"PC\")\n", "cad_summary = summarize_data(cad_df, \"CAD\")\n", "ortho_summary = summarize_data(ortho_df, \"Ortho\")\n", "\n", "combined_summary = pd.concat([pc_summary, cad_summary, ortho_summary], axis=1).fillna({\n", "    'PC_Size_MB': 0, 'PC_File_Count': 0, 'PC_Extensions': 'N/A',\n", "    'CAD_Size_MB': 0, 'CAD_File_Count': 0, 'CAD_Extensions': 'N/A',\n", "    'Ortho_Size_MB': 0, 'Ortho_File_Count': 0, 'Ortho_Extensions': 'N/A',\n", "})\n", "combined_summary[['PC_File_Count', 'CAD_File_Count', 'Ortho_File_Count']] = combined_summary[\n", "    ['PC_File_Count', 'CAD_File_Count', 'Ortho_File_Count']].astype(int)\n", "\n", "combined_summary['Total_Size_GB'] = (\n", "    (combined_summary['PC_Size_MB'] + combined_summary['CAD_Size_MB'] + combined_summary['Ortho_Size_MB']) / 1024\n", ").round(2)\n", "\n", "combined_summary['Data_Completeness'] = (\n", "    (combined_summary['PC_File_Count'] > 0).astype(int) +\n", "    (combined_summary['CAD_File_Count'] > 0).astype(int) +\n", "    (combined_summary['Ortho_File_Count'] > 0).astype(int)\n", ")\n", "\n", "combined_summary.to_csv(output_path / \"site_data_summary.csv\")\n", "combined_summary\n"]}, {"cell_type": "markdown", "metadata": {"id": "jvCTQ770e3Z2"}, "source": ["### Overview and Data Types\n", "\n", "**Systematic Results Compilation:** Based on the systematic exploration of all 6 projects above, compile findings for each data type:"]}, {"cell_type": "markdown", "metadata": {"id": "9TU58ogpDyNi"}, "source": ["## Final Summary and Actions"]}, {"cell_type": "markdown", "metadata": {"id": "_dkXrNkLWwLH"}, "source": ["**Comprehensive Data Exploration Summary**\n", "\n", "Systematic assessment of all available data types across all projects.\n", "\n", "**Data Types Explored**:\n", "- **Point Cloud**: Primary source for 3D spatial ML training\n", "- **CAD**: Geometric reference for alignment & validation\n", "- **Orthomosaic**: 2D imagery for visual QA & analysis\n", "- **IFC**: BIM data (*only available for Trino*\n", "\n", "### Folder Convention\n", "Each project folder follows this format:\n", "\n", "data/raw/<site_name>/\n", "├── cad/ # CAD drawings (DWG, DXF)\n", "├──ifc/ # Industry Foundation Classes (.ifc)\n", "├── pointcloud/ # 3D point clouds (LAS, LAZ)\n", "└── ortho/ # Orthomosaics (TIF, JPG, PNG)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Objective\n", "To verify the availability, size, and types of files across all sites without ranking or filtering. The results serve as a baseline for:\n", "- Coverage validation\n", "- Input preparation for downstream ML and QA tasks\n", "- File-based audit and backup\n", "\n", "### Data Types Tracked\n", "- **Point Cloud**: Used for 3D spatial modeling and ML-based geometric analysis\n", "- **CAD Files**: Used for reference geometry, overlay, and alignment\n", "- **Orthomosaics**: 2D aerial imagery used for QA and contextual mapping\n", "\n", "### Output Summary\n", "For each site:\n", "- Total number of files by type\n", "- Total size in MB and GB\n", "- File format coverage (e.g., `.las`, `.dwg`, `.tif`)\n", "- Exported summary table for downstream consumption\n", "\n", "### Output Location\n", "All summary CSVs are saved under:\n", "data/analysis_output\n", "\n", "Files include:\n", "- `all_file_info.csv`: All files and metadata\n", "- `site_data_summary.csv`: Summary of sizes and file counts per site\n"]}, {"cell_type": "markdown", "metadata": {"id": "GxpqACgvaK7i"}, "source": ["## Summary and Next Steps\n", "\n", "**Summary**:\n", "- All project sites were scanned to assess available CAD, point cloud, and orthomosaic data\n", "- File-level metadata (type, size, extension) has been compiled into structured outputs\n", "- No site was excluded — all data will be considered for quality analysis and modeling\n", "\n", "**Next Steps**:\n", "1. Perform qualitative and quantitative **point cloud assessment**:\n", "   - Check .las/.laz integrity, size, density proxies, spatial coverage\n", "   - Visual inspection and statistics (number of files, area coverage)\n", "2. Explore **CAD and IFC file organization and contents**:\n", "   - Identify key drawing types (foundation, tracker, trench)\n", "   - Map file structure to expected layers or zones\n", "3. Review available **orthomosaic GeoTIFFs**:\n", "   - Confirm resolution and coverage\n", "   - Plan usage for overlay, visualization, or 2D baseline generation\n", "4. Use inventory outputs to plan:\n", "   - Downstream conversion (e.g., to DXF/GeoJSON)\n", "   - Alignment workflows across CAD, point cloud, and ortho\n", "   - ML training data generation using high-quality segments\n", "\n", "**Academic Contribution**:\n", "- Builds a reusable, multi-modal site-level data profile for remote sensing QA\n", "- Lays groundwork for structured dataset curation aligned to thesis objectives\n", "- Ensures early identification of spatial inconsistencies before modeling"]}], "metadata": {"colab": {"collapsed_sections": ["XIwATdS_DyNh", "9TU58ogpDyNi"], "provenance": [], "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 0}