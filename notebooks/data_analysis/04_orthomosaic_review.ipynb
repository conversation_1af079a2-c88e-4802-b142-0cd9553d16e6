{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 04_orthomosaic_review\n", "## Orthomosaic Quality Assessment & Inference Report\n", "This notebook reviews and scores orthomosaic outputs based on point density, visual clarity, and alignment quality. It provides summarized insights and recommendations for further action.\n", "\n", "**Stage**: Data Analysis - Orthomosaic Assessment  \n", "**Input Data**: GeoTIFF files (.tif, .tiff) from data/raw directory  \n", "**Output**: Resolution and coverage assessment for planning usage  \n", "**Format**: JSON reports and summary statistics  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis\n", "\n", "## Overview\n", "\n", "Reviews available orthomosaic GeoTIFF files to:\n", "- **Confirm resolution and coverage** - Assess pixel size and spatial extent\n", "- **Plan usage for overlay** - Determine suitability for point cloud overlay\n", "- **Plan visualization** - Assess quality for visual presentations\n", "- **Plan 2D baseline generation** - Evaluate for reference baseline creation\n", "\n", "## Usage Categories\n", "\n", "- **Overlay**: High-resolution orthomosaics for point cloud alignment\n", "- **Visualization**: Medium-resolution for presentations and reports\n", "- **2D Baseline**: High-resolution for accurate reference measurements"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: rasterio in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (1.4.3)\n", "Requirement already satisfied: affine in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from rasterio) (2.4.0)\n", "Requirement already satisfied: attrs in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from rasterio) (25.3.0)\n", "Requirement already satisfied: certifi in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from rasterio) (2025.6.15)\n", "Requirement already satisfied: click>=4.0 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from rasterio) (8.2.1)\n", "Requirement already satisfied: cligj>=0.5 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from rasterio) (0.7.2)\n", "Requirement already satisfied: numpy>=1.24 in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from rasterio) (1.26.4)\n", "Requirement already satisfied: click-plugins in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from rasterio) (*******)\n", "Requirement already satisfied: pyparsing in /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages (from rasterio) (3.2.3)\n"]}], "source": ["!python -m pip install rasterio"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Orthomosaic GeoTIFF Review - Starting...\n", "Timestamp: 2025-06-26 18:54:33\n"]}], "source": ["# Import required libraries\n", "import json\n", "from pathlib import Path\n", "from datetime import datetime\n", "from typing import Dict, List, Any\n", "import rasterio\n", "\n", "# Import additional libraries\n", "import pandas as pd\n", "from tqdm import tqdm\n", "\n", "print(\"Orthomosaic GeoTIFF Review - Starting...\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Project root: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis\n", "Raw data path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw\n", "Output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs\n"]}], "source": ["# Setup paths\n", "project_root = Path('../..')  # Navigate to project root from notebooks/data_analysis/\n", "data_path = project_root / 'data'\n", "raw_path = data_path / 'raw'\n", "output_path = data_path / 'output_runs'\n", "\n", "# Create output directory\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"Project root: {project_root.resolve()}\")\n", "print(f\"Raw data path: {raw_path.resolve()}\")\n", "print(f\"Output path: {output_path.resolve()}\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "DISCOVERED ORTHOMOSAIC FILES:\n", "==================================================\n", "Total GeoTIFF files found: 5\n", "   1. motali_de_castro/ortho/Area1_Point_ortho.tif (1403.1 MB)\n", "   2. motali_de_castro/ortho/Area1_part2_ortho.tif (1683.2 MB)\n", "   3. motali_de_castro/ortho/Area2_Ortho.tif (1011.0 MB)\n", "   4. motali_de_castro/ortho/Area4_Ortho_SCQM.tif (659.8 MB)\n", "   5. mudjar_enel/ortho.tif (0.0 MB)\n"]}], "source": ["# Discover orthomosaic GeoTIFF files\n", "def discover_orthomosaic_files(base_path: Path) -> List[Path]:\n", "    \"\"\"Discover GeoTIFF files in the data directory.\"\"\"\n", "    extensions = ['*.tif', '*.tiff', '*.TIF', '*.TIFF']\n", "    files = []\n", "    \n", "    for ext in extensions:\n", "        files.extend(base_path.rglob(ext))\n", "    \n", "    return sorted(files)\n", "\n", "# Find orthomosaic files\n", "ortho_files = discover_orthomosaic_files(raw_path)\n", "\n", "print(f\"\\nDISCOVERED ORTHOMOSAIC FILES:\")\n", "print(\"=\" * 50)\n", "print(f\"Total GeoTIFF files found: {len(ortho_files)}\")\n", "\n", "if ortho_files:\n", "    for i, file_path in enumerate(ortho_files, 1):\n", "        relative_path = file_path.relative_to(raw_path)\n", "        file_size_mb = file_path.stat().st_size / (1024 * 1024)\n", "        print(f\"  {i:2d}. {relative_path} ({file_size_mb:.1f} MB)\")\n", "else:\n", "    print(\"  No GeoTIFF files found!\")\n", "    print(f\"  Search path: {raw_path}\")\n", "    print(f\"  Extensions: .tif, .tiff\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Orthomosaic assessment function defined\n"]}], "source": ["# Simple orthomosaic assessment function\n", "def assess_orthomosaic_simple(file_path: Path) -> Dict[str, Any]:\n", "    \"\"\"Perform basic assessment on an orthomosaic GeoTIFF file.\"\"\"\n", "    \n", "    try:\n", "        with rasterio.open(file_path) as src:\n", "            width, height = src.width, src.height\n", "            bands = src.count\n", "            dtype = str(src.dtypes[0])\n", "            crs = src.crs.to_string() if src.crs else \"Unknown\"\n", "\n", "            transform = src.transform\n", "            pixel_size_x = abs(transform[0])\n", "            pixel_size_y = abs(transform[4])\n", "\n", "            bounds = src.bounds\n", "            area_m2 = (bounds.right - bounds.left) * (bounds.top - bounds.bottom)\n", "            area_km2 = area_m2 / 1_000_000\n", "\n", "            resolution_category = (\n", "                \"High\" if pixel_size_x < 0.1 else\n", "                \"Medium\" if pixel_size_x < 0.5 else\n", "                \"Low\"\n", "            )\n", "\n", "            return {\n", "                \"file_path\": str(file_path),\n", "                \"file_size_mb\": file_path.stat().st_size / (1024 * 1024),\n", "                \"dimensions\": {\"width\": width, \"height\": height},\n", "                \"bands\": bands,\n", "                \"data_type\": dtype,\n", "                \"crs\": crs,\n", "                \"pixel_size_m\": {\"x\": pixel_size_x, \"y\": pixel_size_y},\n", "                \"bounds\": {\n", "                    \"left\": bounds.left, \"bottom\": bounds.bottom,\n", "                    \"right\": bounds.right, \"top\": bounds.top\n", "                },\n", "                \"coverage_area_km2\": area_km2,\n", "                \"resolution_category\": resolution_category,\n", "                \"suitable_for_overlay\": pixel_size_x < 1.0,\n", "                \"suitable_for_visualization\": True,\n", "                \"suitable_for_2d_baseline\": pixel_size_x < 0.5\n", "            }\n", "\n", "    except Exception as e:\n", "        return {\n", "            \"file_path\": str(file_path),\n", "            \"error\": str(e),\n", "            \"suitable_for_overlay\": <PERSON><PERSON><PERSON>,\n", "            \"suitable_for_visualization\": <PERSON><PERSON><PERSON>,\n", "            \"suitable_for_2d_baseline\": False\n", "        }\n", "\n", "print(\"Orthomosaic assessment function defined\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "ASSESSING ORTHOMOSAIC FILES:\n", "==================================================\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Processing orthomosaics: 100%|██████████| 5/5 [00:00<00:00, 578.97it/s]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>File</th>\n", "      <th><PERSON><PERSON> (MB)</th>\n", "      <th>Dimensions</th>\n", "      <th>Bands</th>\n", "      <th>Resolution (m/pixel)</th>\n", "      <th>Resolution Category</th>\n", "      <th>Coverage (km²)</th>\n", "      <th>CRS</th>\n", "      <th>Overlay</th>\n", "      <th>Visualization</th>\n", "      <th>2D Baseline</th>\n", "      <th>Note/Error</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>motali_de_castro/ortho/Area1_Point_ortho.tif</td>\n", "      <td>1403.1</td>\n", "      <td>33223 x 22329</td>\n", "      <td>3.0</td>\n", "      <td>0.017</td>\n", "      <td>High</td>\n", "      <td>0.22</td>\n", "      <td>EPSG:32632</td>\n", "      <td>YES</td>\n", "      <td>YES</td>\n", "      <td>YES</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>motali_de_castro/ortho/Area1_part2_ortho.tif</td>\n", "      <td>1683.2</td>\n", "      <td>35017 x 20619</td>\n", "      <td>4.0</td>\n", "      <td>0.017</td>\n", "      <td>High</td>\n", "      <td>0.21</td>\n", "      <td>EPSG:32632</td>\n", "      <td>YES</td>\n", "      <td>YES</td>\n", "      <td>YES</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>motali_de_castro/ortho/Area2_Ortho.tif</td>\n", "      <td>1011.0</td>\n", "      <td>22214 x 29696</td>\n", "      <td>3.0</td>\n", "      <td>0.012</td>\n", "      <td>High</td>\n", "      <td>0.09</td>\n", "      <td>EPSG:32632</td>\n", "      <td>YES</td>\n", "      <td>YES</td>\n", "      <td>YES</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>motali_de_castro/ortho/Area4_Ortho_SCQM.tif</td>\n", "      <td>659.8</td>\n", "      <td>20353 x 17820</td>\n", "      <td>3.0</td>\n", "      <td>0.012</td>\n", "      <td>High</td>\n", "      <td>0.05</td>\n", "      <td>EPSG:32632</td>\n", "      <td>YES</td>\n", "      <td>YES</td>\n", "      <td>YES</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>mudjar_enel/ortho.tif</td>\n", "      <td>NaN</td>\n", "      <td>ERROR</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>N/A</td>\n", "      <td>N/A</td>\n", "      <td>N/A</td>\n", "      <td>'../../data/raw/mudjar_enel/ortho.tif' not rec...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                           File  Size (MB)     Dimensions  \\\n", "0  motali_de_castro/ortho/Area1_Point_ortho.tif     1403.1  33223 x 22329   \n", "1  motali_de_castro/ortho/Area1_part2_ortho.tif     1683.2  35017 x 20619   \n", "2        motali_de_castro/ortho/Area2_Ortho.tif     1011.0  22214 x 29696   \n", "3   motali_de_castro/ortho/Area4_Ortho_SCQM.tif      659.8  20353 x 17820   \n", "4                         mudjar_enel/ortho.tif        NaN          ERROR   \n", "\n", "   Bands  Resolution (m/pixel) Resolution Category  Coverage (km²)  \\\n", "0    3.0                 0.017                High            0.22   \n", "1    4.0                 0.017                High            0.21   \n", "2    3.0                 0.012                High            0.09   \n", "3    3.0                 0.012                High            0.05   \n", "4    NaN                   NaN                None             NaN   \n", "\n", "          CRS Overlay Visualization 2D Baseline  \\\n", "0  EPSG:32632     YES           YES         YES   \n", "1  EPSG:32632     YES           YES         YES   \n", "2  EPSG:32632     YES           YES         YES   \n", "3  EPSG:32632     YES           YES         YES   \n", "4        None     N/A           N/A         N/A   \n", "\n", "                                          Note/Error  \n", "0                                                     \n", "1                                                     \n", "2                                                     \n", "3                                                     \n", "4  '../../data/raw/mudjar_enel/ortho.tif' not rec...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Assess all orthomosaic files\n", "ortho_results = []\n", "\n", "if ortho_files:\n", "    print(\"\\nASSESSING ORTHOMOSAIC FILES:\")\n", "    print(\"=\" * 50)\n", "    \n", "    for file_path in tqdm(ortho_files, desc=\"Processing orthomosaics\"):\n", "        result = assess_orthomosaic_simple(file_path)\n", "        result[\"file\"] = str(file_path.relative_to(raw_path))  # relative filename for table\n", "        ortho_results.append(result)\n", "\n", "    # Build DataFrame\n", "    records = []\n", "    for res in ortho_results:\n", "        if 'error' in res:\n", "            records.append({\n", "                \"File\": res[\"file\"],\n", "                \"Size (MB)\": None,\n", "                \"Dimensions\": \"ERROR\",\n", "                \"Bands\": None,\n", "                \"Resolution (m/pixel)\": None,\n", "                \"Resolution Category\": None,\n", "                \"Coverage (km²)\": None,\n", "                \"CRS\": None,\n", "                \"Overlay\": \"N/A\",\n", "                \"Visualization\": \"N/A\",\n", "                \"2D Baseline\": \"N/A\",\n", "                \"Note/Error\": res.get(\"error\")\n", "            })\n", "        else:\n", "            records.append({\n", "                \"File\": res[\"file\"],\n", "                \"Size (MB)\": round(res[\"file_size_mb\"], 1),\n", "                \"Dimensions\": f\"{res['dimensions']['width']} x {res['dimensions']['height']}\",\n", "                \"Bands\": res[\"bands\"],\n", "                \"Resolution (m/pixel)\": round(res[\"pixel_size_m\"][\"x\"], 3),\n", "                \"Resolution Category\": res[\"resolution_category\"],\n", "                \"Coverage (km²)\": round(res[\"coverage_area_km2\"], 2),\n", "                \"CRS\": res[\"crs\"],\n", "                \"Overlay\": \"YES\" if res[\"suitable_for_overlay\"] else \"NO\",\n", "                \"Visualization\": \"YES\" if res[\"suitable_for_visualization\"] else \"NO\",\n", "                \"2D Baseline\": \"YES\" if res[\"suitable_for_2d_baseline\"] else \"NO\",\n", "                \"Note/Error\": res.get(\"note\", \"\")\n", "            })\n", "\n", "    df_results = pd.DataFrame(records)\n", "    display(df_results)  # Works in notebooks\n", "\n", "    # Optional: Save to CSV\n", "    df_results.to_csv(f\"{output_path}/orthomosaic_quality_assessment.csv\", index=False)\n", "else:\n", "    print(\"\\nNo orthomosaic files found for assessment.\")\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ORTHOMOSAIC SUMMARY:\n", "==================================================\n", "Total files: 5\n", "Suitable for overlay: 4\n", "Suitable for visualization: 4\n", "Suitable for 2D baseline: 4\n", "\n", "Results saved: ../../data/output_runs/orthomosaic_assessment_20250626_185401.json\n"]}], "source": ["if ortho_results:\n", "    overlay_count = sum(r.get('suitable_for_overlay', False) for r in ortho_results)\n", "    viz_count = sum(r.get('suitable_for_visualization', False) for r in ortho_results)\n", "    baseline_count = sum(r.get('suitable_for_2d_baseline', False) for r in ortho_results)\n", "\n", "    print(\"ORTHOMOSAIC SUMMARY:\")\n", "    print(\"=\" * 50)\n", "    print(f\"Total files: {len(ortho_results)}\")\n", "    print(f\"Suitable for overlay: {overlay_count}\")\n", "    print(f\"Suitable for visualization: {viz_count}\")\n", "    print(f\"Suitable for 2D baseline: {baseline_count}\")\n", "\n", "    # Save full results\n", "    results_path = output_path / f\"orthomosaic_assessment_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json\"\n", "    with open(results_path, 'w') as f:\n", "        json.dump({\n", "            'assessment_date': datetime.now().isoformat(),\n", "            'total_files': len(ortho_results),\n", "            'summary': {\n", "                'overlay_suitable': overlay_count,\n", "                'visualization_suitable': viz_count,\n", "                'baseline_suitable': baseline_count\n", "            },\n", "            'files': ortho_results\n", "        }, f, indent=2)\n", "\n", "    print(f\"\\nResults saved: {results_path}\")\n", "\n", "else:\n", "    print(\"No orthomosaic files available for assessment.\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["USAGE PLANNING RECOMMENDATIONS:\n", "==================================================\n", "\n", "**OVERLAY USAGE:**\n", "Use for: Alignment verification, visual validation, context mapping\n", "\n", "**VISUALIZATION USAGE:**\n", "Use for: Reports, presentations, stakeholder communication\n", "\n", "**2D BASELINE USAGE:**\n", "Use for: Reference measurements, change detection, validation"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}