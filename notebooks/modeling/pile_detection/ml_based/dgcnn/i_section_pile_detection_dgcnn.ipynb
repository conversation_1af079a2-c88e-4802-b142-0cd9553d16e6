{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# I-Section Pile Detection (DGCNN)\n", "\n", "This notebook implements deep learning-based I-section pile detection as part of the pile detection stage. It uses DGCNN (Dynamic Graph CNN) to process ground-filtered or aligned point clouds and detect I-section pile structures.\n", "\n", "**Stage**: <PERSON>le Detection  \n", "**Input Data**: Ground-filtered or aligned point cloud  \n", "**Output**: Pile center coordinates + types (I-section, cylindrical, etc.)  \n", "**Format**: .csv (columns: x, y, z, pile_type, confidence, etc.)  \n", "**Model**: DGCNN for point-wise classification  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Process Overview:\n", "1. **Load Ground-Filtered Data**: Import processed point cloud from ground segmentation or alignment\n", "2. **Patch Generation**: Create overlapping 3D patches for DGCNN processing\n", "3. **I-Section Detection**: Apply DGCNN model for point-wise classification\n", "4. **Post-Processing**: Cluster detected points and extract pile centers\n", "5. **Export Results**: Save detected pile data in .csv format"]}, {"cell_type": "markdown", "metadata": {"tags": ["parameters"]}, "source": ["## Papermill Parameters\n", "\n", "These parameters can be overridden when running with Papermill for batch processing."]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters - can be overridden during execution\n", "project_type = \"foundation_analysis\"  # Project type for data organization\n", "site_name = \"castro_area4\"            # Site identifier for processing\n", "ground_method = \"csf\"                 # Options: csf, pmf, ransac\n", "confidence_threshold = 0.7\n", "model_path = \"../../models/dgcnn_isection_pile.pth\"\n", "input_data_dir = \"../../data/processed\"\n", "output_dir = \"../../data/output_runs\"\n", "mlflow_experiment_name = \"pile_detection_dgcnn\"\n", "mlflow_run_name = f\"i_section_{project_type}_{site_name}_{ground_method}\"\n", "enable_cross_validation = True\n", "cv_folds = 5\n", "enable_enhanced_analysis = True"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Python version: 3.11.11 | packaged by conda-forge | (main, Mar  3 2025, 20:44:07) [Clang 18.1.8 ]\n", "Torch version: 2.5.1\n", "Torch location: /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torch/__init__.py\n", "Platform: macOS-13.4.1-arm64-arm-64bit\n", "Prefix: /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv\n"]}], "source": ["import sys\n", "import torch\n", "import platform\n", "\n", "print(\"Python version:\", sys.version)\n", "print(\"Torch version:\", torch.__version__)\n", "print(\"Torch location:\", torch.__file__)\n", "print(\"Platform:\", platform.platform())\n", "print(\"Prefix:\", sys.prefix)\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DynamicEdgeConv instantiation successful.\n"]}], "source": ["from torch_geometric.nn import DynamicEdgeConv\n", "import torch.nn as nn\n", "\n", "conv = DynamicEdgeConv(\n", "    nn=nn.Sequential(nn.<PERSON>(6, 64), nn.<PERSON><PERSON><PERSON>()),\n", "    k=20\n", ")\n", "print(\"DynamicEdgeConv instantiation successful.\")\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PyTorch version: 2.5.1\n", "CUDA available: False\n", "Using device: cpu\n"]}], "source": ["# Import libraries\n", "import os\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import open3d as o3d\n", "from sklearn.neighbors import NearestNeighbors\n", "from sklearn.cluster import DBSCAN\n", "from scipy.spatial import ConvexHull\n", "from scipy.spatial.distance import cdist\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.utils.data import Dataset, DataLoader\n", "from torch_geometric.nn import DynamicEdgeConv, global_max_pool\n", "from torch_geometric.data import Data, Batch\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Enhanced analysis and tracking imports\n", "import pandas as pd\n", "import json\n", "import time\n", "from datetime import datetime\n", "from sklearn.model_selection import KFold, cross_val_score\n", "from sklearn.metrics import precision_recall_fscore_support, confusion_matrix, roc_curve, auc\n", "from sklearn.metrics import precision_recall_curve, average_precision_score\n", "import seaborn as sns\n", "from scipy import stats\n", "\n", "# MLflow tracking\n", "try:\n", "    import mlflow\n", "    import mlflow.pytorch\n", "    import mlflow.sklearn\n", "    MLFLOW_AVAILABLE = True\n", "    print(\"MLflow available for experiment tracking\")\n", "except ImportError:\n", "    MLFLOW_AVAILABLE = False\n", "    print(\"MLflow not available - install with: pip install mlflow\")\n", "\n", "# Create output directories\n", "output_dir = Path(output_dir)\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Initialize MLflow if available\n", "if MLFLOW_AVAILABLE:\n", "    mlflow.set_experiment(mlflow_experiment_name)\n", "    mlflow.start_run(run_name=mlflow_run_name)\n", "    \n", "    # Log parameters\n", "    mlflow.log_param(\"site_name\", site_name)\n", "    mlflow.log_param(\"ground_method\", ground_method)\n", "    mlflow.log_param(\"confidence_threshold\", confidence_threshold)\n", "    mlflow.log_param(\"model_architecture\", \"DGCNN\")\n", "    mlflow.log_param(\"pile_type\", \"i_section\")\n", "    mlflow.log_param(\"enable_cross_validation\", enable_cross_validation)\n", "    mlflow.log_param(\"cv_folds\", cv_folds)\n", "\n", "print(f\"Processing site: {site_name}\")\n", "print(f\"Ground segmentation method: {ground_method}\")\n", "print(f\"Confidence threshold: {confidence_threshold}\")\n", "print(f\"Cross-validation enabled: {enable_cross_validation}\")\n", "print(f\"Enhanced analysis enabled: {enable_enhanced_analysis}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. I-<PERSON> Characteristics\n", "\n", "Define the geometric characteristics of I-section piles for detection."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I-Section Pile Detection Configuration:\n", "Flange width range: (0.1, 0.4) m\n", "Web thickness range: (0.01, 0.05) m\n", "Height range: (0.1, 1.0) m\n", "Patch size: 2.0 m\n"]}], "source": ["class ISectionPileConfig:\n", "    \"\"\"\n", "    Configuration for I-section pile detection.\n", "    \"\"\"\n", "    def __init__(self):\n", "        # I-section dimensions (typical ranges)\n", "        self.flange_width_range = (0.1, 0.4)  # meters\n", "        self.web_thickness_range = (0.01, 0.05)  # meters\n", "        self.flange_thickness_range = (0.01, 0.05)  # meters\n", "        self.height_range = (0.1, 1.0)  # meters\n", "        \n", "        # Detection parameters\n", "        self.patch_size = 2.0  # meters\n", "        self.min_points_per_patch = 100\n", "        self.overlap_ratio = 0.5\n", "        \n", "        # DGCNN parameters\n", "        self.k_neighbors = 20\n", "        self.num_points = 1024\n", "        self.feature_dims = [64, 128, 256, 512]\n", "        self.num_classes = 2  # pile vs non-pile\n", "\n", "config = ISectionPileConfig()\n", "print(\"I-Section Pile Detection Configuration:\")\n", "print(f\"Flange width range: {config.flange_width_range} m\")\n", "print(f\"Web thickness range: {config.web_thickness_range} m\")\n", "print(f\"Height range: {config.height_range} m\")\n", "print(f\"Patch size: {config.patch_size} m\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Point Cloud Patch Generation\n", "\n", "Generate patches from point clouds for training and inference."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def generate_patches_from_point_cloud(points, colors=None, patch_size=2.0, overlap_ratio=0.5, min_points=100):\n", "    \"\"\"\n", "    Generate overlapping patches from a point cloud.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud coordinates (N, 3)\n", "    colors : numpy.ndarray, optional\n", "        RGB colors (N, 3)\n", "    patch_size : float\n", "        Size of each patch in meters\n", "    overlap_ratio : float\n", "        Overlap ratio between adjacent patches\n", "    min_points : int\n", "        Minimum number of points per patch\n", "        \n", "    Returns:\n", "    --------\n", "    patches : list\n", "        List of patch dictionaries containing points, colors, and metadata\n", "    \"\"\"\n", "    # Calculate bounds\n", "    min_coords = points.min(axis=0)\n", "    max_coords = points.max(axis=0)\n", "    \n", "    # Calculate step size\n", "    step_size = patch_size * (1 - overlap_ratio)\n", "    \n", "    patches = []\n", "    patch_id = 0\n", "    \n", "    # Generate grid of patch centers\n", "    x_centers = np.arange(min_coords[0], max_coords[0], step_size)\n", "    y_centers = np.arange(min_coords[1], max_coords[1], step_size)\n", "    \n", "    for x_center in x_centers:\n", "        for y_center in y_centers:\n", "            # Define patch bounds\n", "            x_min = x_center - patch_size / 2\n", "            x_max = x_center + patch_size / 2\n", "            y_min = y_center - patch_size / 2\n", "            y_max = y_center + patch_size / 2\n", "            \n", "            # Find points within patch\n", "            mask = ((points[:, 0] >= x_min) & (points[:, 0] <= x_max) &\n", "                   (points[:, 1] >= y_min) & (points[:, 1] <= y_max))\n", "            \n", "            patch_points = points[mask]\n", "            \n", "            if len(patch_points) >= min_points:\n", "                # Center the patch points\n", "                patch_center = np.array([x_center, y_center, patch_points[:, 2].mean()])\n", "                centered_points = patch_points - patch_center\n", "                \n", "                patch_data = {\n", "                    'id': patch_id,\n", "                    'points': centered_points,\n", "                    'original_points': patch_points,\n", "                    'center': patch_center,\n", "                    'bounds': (x_min, y_min, x_max, y_max),\n", "                    'num_points': len(patch_points)\n", "                }\n", "                \n", "                if colors is not None:\n", "                    patch_data['colors'] = colors[mask]\n", "                \n", "                patches.append(patch_data)\n", "                patch_id += 1\n", "    \n", "    print(f\"Generated {len(patches)} patches from point cloud\")\n", "    return patches"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. I-Section Pile Geometric Analysis\n", "\n", "Analyze point cloud patches for I-section pile characteristics."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def analyze_i_section_geometry(points, tolerance=0.05):\n", "    \"\"\"\n", "    Analyze point cloud patch for I-section pile geometry.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud patch (N, 3)\n", "    tolerance : float\n", "        Tolerance for geometric analysis\n", "        \n", "    Returns:\n", "    --------\n", "    features : dict\n", "        Geometric features indicating I-section characteristics\n", "    \"\"\"\n", "    if len(points) < 10:\n", "        return None\n", "    \n", "    # Project points to XY plane for cross-section analysis\n", "    xy_points = points[:, :2]\n", "    \n", "    # Find convex hull\n", "    try:\n", "        hull = ConvexHull(xy_points)\n", "        hull_points = xy_points[hull.vertices]\n", "    except:\n", "        return None\n", "    \n", "    # Calculate bounding box\n", "    min_coords = points.min(axis=0)\n", "    max_coords = points.max(axis=0)\n", "    dimensions = max_coords - min_coords\n", "    \n", "    # Analyze cross-sectional shape\n", "    # For I-section, we expect:\n", "    # 1. Elongated shape in one horizontal direction\n", "    # 2. Three main clusters: two flanges and web\n", "    # 3. Specific width-to-thickness ratios\n", "    \n", "    # Calculate aspect ratios\n", "    xy_aspect = dimensions[0] / dimensions[1] if dimensions[1] > 0 else 0\n", "    if xy_aspect < 1:\n", "        xy_aspect = 1 / xy_aspect\n", "    \n", "    # Analyze point distribution for I-section pattern\n", "    # Cluster points in cross-section\n", "    clustering = DBSCAN(eps=tolerance, min_samples=5)\n", "    cluster_labels = clustering.fit_predict(xy_points)\n", "    \n", "    unique_labels = np.unique(cluster_labels)\n", "    num_clusters = len(unique_labels[unique_labels >= 0])  # Exclude noise (-1)\n", "    \n", "    # Calculate density distribution\n", "    # For I-section, density should be higher at flanges and web\n", "    center_x, center_y = xy_points.mean(axis=0)\n", "    \n", "    # Divide into grid and calculate density\n", "    grid_size = 10\n", "    x_bins = np.linspace(xy_points[:, 0].min(), xy_points[:, 0].max(), grid_size)\n", "    y_bins = np.linspace(xy_points[:, 1].min(), xy_points[:, 1].max(), grid_size)\n", "    \n", "    density_grid = np.zeros((grid_size-1, grid_size-1))\n", "    for i in range(grid_size-1):\n", "        for j in range(grid_size-1):\n", "            mask = ((xy_points[:, 0] >= x_bins[i]) & (xy_points[:, 0] < x_bins[i+1]) &\n", "                   (xy_points[:, 1] >= y_bins[j]) & (xy_points[:, 1] < y_bins[j+1]))\n", "            density_grid[j, i] = mask.sum()\n", "    \n", "    # Calculate features\n", "    features = {\n", "        'num_points': len(points),\n", "        'dimensions': dimensions,\n", "        'xy_aspect_ratio': xy_aspect,\n", "        'height': dimensions[2],\n", "        'width': max(dimensions[0], dimensions[1]),\n", "        'thickness': min(dimensions[0], dimensions[1]),\n", "        'num_clusters': num_clusters,\n", "        'hull_area': hull.volume if hasattr(hull, 'volume') else 0,\n", "        'density_variance': np.var(density_grid),\n", "        'density_max': np.max(density_grid),\n", "        'compactness': len(points) / hull.volume if hasattr(hull, 'volume') and hull.volume > 0 else 0\n", "    }\n", "    \n", "    return features"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. DGCNN Model for I-Section Pile Detection\n", "\n", "Implement Dynamic Graph CNN for point-wise classification."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["class DGCNNISectionPile(nn.Module):\n", "    \"\"\"\n", "    DGCNN model for I-section pile detection.\n", "    \"\"\"\n", "    def __init__(self, k=20, feature_dims=[64, 128, 256, 512], num_classes=2, dropout=0.5):\n", "        super(DGCNNISectionPile, self).__init__()\n", "        self.k = k\n", "        self.num_classes = num_classes\n", "        \n", "        # Edge convolution layers\n", "        self.conv1 = DynamicEdgeConv(nn.Sequential(\n", "            nn.Linear(6, feature_dims[0]),  # 6 = 3 (xyz) * 2 (point and neighbor)\n", "            nn.BatchNorm1d(feature_dims[0]),\n", "            nn.ReLU(),\n", "            nn.Linear(feature_dims[0], feature_dims[0])\n", "        ), k=k, aggr='max')\n", "        \n", "        self.conv2 = DynamicEdgeConv(nn.Sequential(\n", "            nn.Linear(feature_dims[0] * 2, feature_dims[1]),\n", "            nn.BatchNorm1d(feature_dims[1]),\n", "            nn.ReLU(),\n", "            nn.Linear(feature_dims[1], feature_dims[1])\n", "        ), k=k, aggr='max')\n", "        \n", "        self.conv3 = DynamicEdgeConv(nn.Sequential(\n", "            nn.Linear(feature_dims[1] * 2, feature_dims[2]),\n", "            nn.BatchNorm1d(feature_dims[2]),\n", "            nn.ReLU(),\n", "            nn.Linear(feature_dims[2], feature_dims[2])\n", "        ), k=k, aggr='max')\n", "        \n", "        self.conv4 = DynamicEdgeConv(nn.Sequential(\n", "            nn.Linear(feature_dims[2] * 2, feature_dims[3]),\n", "            nn.BatchNorm1d(feature_dims[3]),\n", "            nn.ReLU(),\n", "            nn.Linear(feature_dims[3], feature_dims[3])\n", "        ), k=k, aggr='max')\n", "        \n", "        # Global feature aggregation\n", "        total_features = sum(feature_dims)\n", "        \n", "        # Classification head\n", "        self.classifier = nn.Sequential(\n", "            nn.Linear(total_features, 512),\n", "            nn.BatchNorm1d(512),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.<PERSON><PERSON>(512, 256),\n", "            nn.BatchNorm1d(256),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(256, num_classes)\n", "        )\n", "        \n", "        # Point-wise classification head\n", "        self.point_classifier = nn.Sequential(\n", "            nn.Linear(total_features, 256),\n", "            nn.BatchNorm1d(256),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.<PERSON>(256, 128),\n", "            nn.<PERSON>chNorm1d(128),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(128, num_classes)\n", "        )\n", "    \n", "    def forward(self, data):\n", "        x, batch = data.x, data.batch\n", "        \n", "        # Extract features through edge convolutions\n", "        x1 = self.conv1(x, batch)\n", "        x2 = self.conv2(x1, batch)\n", "        x3 = self.conv3(x2, batch)\n", "        x4 = self.conv4(x3, batch)\n", "        \n", "        # Concatenate all features\n", "        x_concat = torch.cat([x1, x2, x3, x4], dim=1)\n", "        \n", "        # Global classification (patch-level)\n", "        global_features = global_max_pool(x_concat, batch)\n", "        global_pred = self.classifier(global_features)\n", "        \n", "        # Point-wise classification\n", "        point_pred = self.point_classifier(x_concat)\n", "        \n", "        return global_pred, point_pred"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Ground-Filtered Point Cloud Loading\n", "\n", "Load processed point cloud data from ground segmentation or alignment stage."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing site: castro\n", "Ground segmentation method: ransac\n", "Confidence threshold: 0.7\n"]}], "source": ["# Configuration for data paths\n", "import pandas as pd\n", "import laspy\n", "\n", "# Define input and output paths\n", "#input_data_dir = Path(\"../../data/processed/ground_segmentation/output_runs/Castro_ransac_pmf_20250618_182818\")\n", "input_ply_path = \"../preprocessing/ground_segmentation/output_runs/Castro_ransac_pmf_20250618_182818\"\n", "\n", "output_dir = Path(\"../../output_runs/pile_detection\")\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Parameters for Papermill execution\n", "site_name = \"castro\"  # Will be parameterized\n", "ground_method = \"ransac\"   # Options: csf, pmf, ransac\n", "confidence_threshold = 0.7\n", "model_path = \"../../models/dgcnn_isection_pile.pth\"\n", "\n", "print(f\"Processing site: {site_name}\")\n", "print(f\"Ground segmentation method: {ground_method}\")\n", "print(f\"Confidence threshold: {confidence_threshold}\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def load_ground_filtered_point_cloud(site_name, method=\"csf\"):\n", "    \"\"\"\n", "    Load ground-filtered point cloud from previous processing stage.\n", "    \n", "    Parameters:\n", "    -----------\n", "    site_name : str\n", "        Name of the site to process\n", "    method : str\n", "        Ground segmentation method used (csf, pmf, ransac)\n", "        \n", "    Returns:\n", "    --------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud coordinates (N, 3)\n", "    colors : numpy.ndarray\n", "        RGB colors if available (N, 3)\n", "    metadata : dict\n", "        Additional metadata about the point cloud\n", "    \"\"\"\n", "    # Look for ground-filtered files\n", "    las_file = input_data_dir / f\"{site_name}_ground_filtered_{method}.las\"\n", "    \n", "    if not las_file.exists():\n", "        # Try alternative naming conventions\n", "        alternative_files = list(input_data_dir.glob(f\"{site_name}*{method}*.las\"))\n", "        if alternative_files:\n", "            las_file = alternative_files[0]\n", "        else:\n", "            raise FileNotFoundError(f\"No ground-filtered LAS file found for {site_name} with method {method}\")\n", "    \n", "    print(f\"Loading point cloud from: {las_file}\")\n", "    \n", "    # Load LAS file\n", "    las = laspy.read(las_file)\n", "    \n", "    # Extract coordinates\n", "    points = np.vstack([las.x, las.y, las.z]).transpose()\n", "    \n", "    # Extract colors if available\n", "    colors = None\n", "    if hasattr(las, 'red') and hasattr(las, 'green') and hasattr(las, 'blue'):\n", "        colors = np.vstack([las.red, las.green, las.blue]).transpose()\n", "        colors = colors / 65535.0  # Normalize to [0, 1]\n", "    \n", "    # Extract metadata\n", "    metadata = {\n", "        'num_points': len(points),\n", "        'bounds': {\n", "            'x_min': points[:, 0].min(), 'x_max': points[:, 0].max(),\n", "            'y_min': points[:, 1].min(), 'y_max': points[:, 1].max(),\n", "            'z_min': points[:, 2].min(), 'z_max': points[:, 2].max()\n", "        },\n", "        'file_path': str(las_file),\n", "        'ground_method': method\n", "    }\n", "    \n", "    print(f\"Loaded {len(points):,} points\")\n", "    print(f\"Bounds: X[{metadata['bounds']['x_min']:.2f}, {metadata['bounds']['x_max']:.2f}] \")\n", "    print(f\"        Y[{metadata['bounds']['y_min']:.2f}, {metadata['bounds']['y_max']:.2f}] \")\n", "    print(f\"        Z[{metadata['bounds']['z_min']:.2f}, {metadata['bounds']['z_max']:.2f}]\")\n", "    \n", "    return points, colors, metadata"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["import open3d as o3d\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "def load_ground_filtered_ply(site_dir, filename=\"nonground_points.ply\"):\n", "    \"\"\"\n", "    Load ground-filtered point cloud from a PLY file (e.g., nonground_points.ply).\n", "    \n", "    Parameters:\n", "    -----------\n", "    site_dir : str or Path\n", "        Directory where the PLY file is located\n", "    filename : str\n", "        Name of the PLY file (default: 'nonground_points.ply')\n", "        \n", "    Returns:\n", "    --------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud coordinates (N, 3)\n", "    colors : numpy.ndarray or None\n", "        RGB colors if available (N, 3)\n", "    metadata : dict\n", "        Metadata such as bounds and file path\n", "    \"\"\"\n", "    site_dir = Path(site_dir)\n", "    ply_file = site_dir / filename\n", "    \n", "    if not ply_file.exists():\n", "        raise FileNotFoundError(f\"No PLY file found at {ply_file}\")\n", "    \n", "    print(f\"Loading point cloud from: {ply_file}\")\n", "    \n", "    # Load using Open3D\n", "    pcd = o3d.io.read_point_cloud(str(ply_file))\n", "    \n", "    points = np.asarray(pcd.points)\n", "    colors = np.asarray(pcd.colors) if pcd.has_colors() else None\n", "    \n", "    metadata = {\n", "        'num_points': len(points),\n", "        'bounds': {\n", "            'x_min': points[:, 0].min(), 'x_max': points[:, 0].max(),\n", "            'y_min': points[:, 1].min(), 'y_max': points[:, 1].max(),\n", "            'z_min': points[:, 2].min(), 'z_max': points[:, 2].max()\n", "        },\n", "        'file_path': str(ply_file),\n", "        'source': 'ply'\n", "    }\n", "    \n", "    print(f\"Loaded {len(points):,} points from PLY\")\n", "    print(f\"Bounds: X[{metadata['bounds']['x_min']:.2f}, {metadata['bounds']['x_max']:.2f}]\")\n", "    print(f\"        Y[{metadata['bounds']['y_min']:.2f}, {metadata['bounds']['y_max']:.2f}]\")\n", "    print(f\"        Z[{metadata['bounds']['z_min']:.2f}, {metadata['bounds']['z_max']:.2f}]\")\n", "    \n", "    return points, colors, metadata\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading point cloud from: ../preprocessing/ground_segmentation/output_runs/Castro_ransac_pmf_20250618_182818/ransac_only_nonground.ply\n", "Loaded 512,367 points from PLY\n", "Bounds: X[707850.84, 708080.53]\n", "        Y[4692891.92, 4693090.33]\n", "        Z[51.12, 68.45]\n", "Point cloud statistics:\n", "Total points: 512,367\n", "Point density: 11.24 points/m²\n"]}], "source": ["# Load the ground-filtered point cloud\n", "#points, colors, metadata = load_ground_filtered_point_cloud(site_name, ground_method)\n", "points, colors, metadata = load_ground_filtered_ply(input_ply_path, filename=\"ransac_only_nonground.ply\")\n", "\n", "# Display basic statistics\n", "print(f\"Point cloud statistics:\")\n", "print(f\"Total points: {len(points):,}\")\n", "print(f\"Point density: {len(points) / ((metadata['bounds']['x_max'] - metadata['bounds']['x_min']) * (metadata['bounds']['y_max'] - metadata['bounds']['y_min'])):.2f} points/m²\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Data Preprocessing for DGCNN Input\n", "\n", "Prepare point cloud patches for DGCNN model input."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["class PointCloudDataset(Dataset):\n", "    \"\"\"\n", "    Dataset class for point cloud patches.\n", "    \"\"\"\n", "    def __init__(self, patches, num_points=1024, augment=False):\n", "        self.patches = patches\n", "        self.num_points = num_points\n", "        self.augment = augment\n", "    \n", "    def __len__(self):\n", "        return len(self.patches)\n", "    \n", "    def __getitem__(self, idx):\n", "        patch = self.patches[idx]\n", "        points = patch['points']\n", "        \n", "        # Sample or pad points to fixed size\n", "        if len(points) >= self.num_points:\n", "            # Random sampling\n", "            indices = np.random.choice(len(points), self.num_points, replace=False)\n", "            points = points[indices]\n", "        else:\n", "            # Pad with repeated points\n", "            repeat_indices = np.random.choice(len(points), self.num_points - len(points), replace=True)\n", "            points = np.vstack([points, points[repeat_indices]])\n", "        \n", "        # Data augmentation (if enabled)\n", "        if self.augment:\n", "            # Random rotation around Z-axis\n", "            angle = np.random.uniform(0, 2 * np.pi)\n", "            cos_a, sin_a = np.cos(angle), np.sin(angle)\n", "            rotation_matrix = np.array([\n", "                [cos_a, -sin_a, 0],\n", "                [sin_a, cos_a, 0],\n", "                [0, 0, 1]\n", "            ])\n", "            points = points @ rotation_matrix.T\n", "            \n", "            # Random jittering\n", "            noise = np.random.normal(0, 0.01, points.shape)\n", "            points += noise\n", "        \n", "        # Convert to torch tensor\n", "        points_tensor = torch.FloatTensor(points)\n", "        \n", "        # Create PyTorch Geometric data object\n", "        data = Data(x=points_tensor)\n", "        \n", "        return data, patch['id']"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["def preprocess_patches_for_dgcnn(patches, num_points=1024):\n", "    \"\"\"\n", "    Preprocess patches for DGCNN input.\n", "    \n", "    Parameters:\n", "    -----------\n", "    patches : list\n", "        List of patch dictionaries\n", "    num_points : int\n", "        Number of points per patch for DGCNN\n", "        \n", "    Returns:\n", "    --------\n", "    dataset : PointCloudDataset\n", "        Dataset ready for DGCNN inference\n", "    \"\"\"\n", "    print(f\"Preprocessing {len(patches)} patches for DGCNN input\")\n", "    print(f\"Target points per patch: {num_points}\")\n", "    \n", "    # Filter patches with sufficient points\n", "    valid_patches = [p for p in patches if len(p['points']) >= config.min_points_per_patch]\n", "    print(f\"Valid patches after filtering: {len(valid_patches)}\")\n", "    \n", "    # Create dataset\n", "    dataset = PointCloudDataset(valid_patches, num_points=num_points, augment=False)\n", "    \n", "    return dataset"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generating patches from point cloud...\n", "Generated 12050 patches from point cloud\n", "Generated 12050 patches\n", "Average points per patch: 135.4\n", "Min points per patch: 100\n", "Max points per patch: 387\n"]}], "source": ["# Generate patches from the loaded point cloud\n", "print(\"Generating patches from point cloud...\")\n", "patches = generate_patches_from_point_cloud(\n", "    points, \n", "    colors=colors,\n", "    patch_size=config.patch_size,\n", "    overlap_ratio=config.overlap_ratio,\n", "    min_points=config.min_points_per_patch\n", ")\n", "\n", "print(f\"Generated {len(patches)} patches\")\n", "if len(patches) > 0:\n", "    print(f\"Average points per patch: {np.mean([p['num_points'] for p in patches]):.1f}\")\n", "    print(f\"Min points per patch: {min([p['num_points'] for p in patches])}\")\n", "    print(f\"Max points per patch: {max([p['num_points'] for p in patches])}\")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Preprocessing 12050 patches for DGCNN input\n", "Target points per patch: 1024\n", "Valid patches after filtering: 12050\n", "Created dataset with 12050 patches\n", "Batch size: 8\n", "Number of batches: 1507\n"]}], "source": ["from torch_geometric.loader import DataLoader  \n", "\n", "# Preprocess patches for DGCNN\n", "dataset = preprocess_patches_for_dgcnn(patches, num_points=config.num_points)\n", "dataloader = DataLoader(dataset, batch_size=8, shuffle=False, num_workers=0)\n", "\n", "print(f\"Created dataset with {len(dataset)} patches\")\n", "print(f\"Batch size: 8\")\n", "print(f\"Number of batches: {len(dataloader)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Model Loading and Inference\n", "\n", "Load the trained DGCNN model and perform inference on patches."]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["def load_dgcnn_model(model_path, device):\n", "    \"\"\"\n", "    <PERSON>ad trained DGCNN model for inference.\n", "    \n", "    Parameters:\n", "    -----------\n", "    model_path : str\n", "        Path to the trained model file\n", "    device : torch.device\n", "        Device to load the model on\n", "        \n", "    Returns:\n", "    --------\n", "    model : DGCNNISectionPile\n", "        Loaded model ready for inference\n", "    \"\"\"\n", "    # Initialize model\n", "    model = DGCNNISectionPile(\n", "        k=config.k_neighbors,\n", "        feature_dims=config.feature_dims,\n", "        num_classes=config.num_classes\n", "    )\n", "    \n", "    # Load model weights if available\n", "    if Path(model_path).exists():\n", "        print(f\"Loading model weights from: {model_path}\")\n", "        checkpoint = torch.load(model_path, map_location=device)\n", "        model.load_state_dict(checkpoint['model_state_dict'])\n", "        print(f\"Model loaded successfully (epoch {checkpoint.get('epoch', 'unknown')})\")\n", "    else:\n", "        print(f\"Warning: Model file not found at {model_path}\")\n", "        print(\"Using randomly initialized model (for demonstration purposes)\")\n", "    \n", "    model.to(device)\n", "    model.eval()\n", "    \n", "    return model"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["def run_inference(model, dataloader, device, confidence_threshold=0.7):\n", "    \"\"\"\n", "    Run inference on all patches using the DGCNN model.\n", "    \n", "    Parameters:\n", "    -----------\n", "    model : DGCNNISectionPile\n", "        Trained DGCNN model\n", "    dataloader : DataLoader\n", "        DataLoader containing patches\n", "    device : torch.device\n", "        Device for computation\n", "    confidence_threshold : float\n", "        Minimum confidence for pile detection\n", "        \n", "    Returns:\n", "    --------\n", "    results : list\n", "        List of detection results for each patch\n", "    \"\"\"\n", "    results = []\n", "    \n", "    print(f\"Running inference on {len(dataloader)} batches...\")\n", "    \n", "    with torch.no_grad():\n", "        for batch_idx, (batch_data, patch_ids) in enumerate(dataloader):\n", "            # Move data to device\n", "            batch_data = batch_data.to(device)\n", "            \n", "            # Forward pass\n", "            global_pred, point_pred = model(batch_data)\n", "            \n", "            # Apply softmax to get probabilities\n", "            global_probs = F.softmax(global_pred, dim=1)\n", "            point_probs = F.softmax(point_pred, dim=1)\n", "            \n", "            # Process each sample in the batch\n", "            batch_size = len(patch_ids)\n", "            for i in range(batch_size):\n", "                patch_id = patch_ids[i]\n", "                \n", "                # Global prediction (patch-level)\n", "                global_confidence = global_probs[i, 1].item()  # Probability of pile class\n", "                is_pile_patch = global_confidence > confidence_threshold\n", "                \n", "                # Point-wise predictions\n", "                start_idx = i * config.num_points\n", "                end_idx = start_idx + config.num_points\n", "                point_confidences = point_probs[start_idx:end_idx, 1].cpu().numpy()\n", "                \n", "                result = {\n", "                    'patch_id': patch_id,\n", "                    'global_confidence': global_confidence,\n", "                    'is_pile_patch': is_pile_patch,\n", "                    'point_confidences': point_confidences,\n", "                    'num_pile_points': (point_confidences > confidence_threshold).sum()\n", "                }\n", "                \n", "                results.append(result)\n", "            \n", "            if (batch_idx + 1) % 10 == 0:\n", "                print(f\"Processed {batch_idx + 1}/{len(dataloader)} batches\")\n", "    \n", "    print(f\"Inference completed. Processed {len(results)} patches.\")\n", "    return results"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Warning: Model file not found at ../../models/dgcnn_isection_pile.pth\n", "Using randomly initialized model (for demonstration purposes)\n", "Model parameters: 1,601,796 total, 1,601,796 trainable\n"]}], "source": ["# Load the DGCNN model\n", "model = load_dgcnn_model(model_path, device)\n", "\n", "# Display model information\n", "total_params = sum(p.numel() for p in model.parameters())\n", "trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)\n", "print(f\"Model parameters: {total_params:,} total, {trainable_params:,} trainable\")"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running inference on 1507 batches...\n", "Processed 10/1507 batches\n", "Processed 20/1507 batches\n", "Processed 30/1507 batches\n", "Processed 40/1507 batches\n", "Processed 50/1507 batches\n", "Processed 60/1507 batches\n", "Processed 70/1507 batches\n", "Processed 80/1507 batches\n", "Processed 90/1507 batches\n", "Processed 100/1507 batches\n", "Processed 110/1507 batches\n", "Processed 120/1507 batches\n", "Processed 130/1507 batches\n", "Processed 140/1507 batches\n", "Processed 150/1507 batches\n", "Processed 160/1507 batches\n", "Processed 170/1507 batches\n", "Processed 180/1507 batches\n", "Processed 190/1507 batches\n", "Processed 200/1507 batches\n", "Processed 210/1507 batches\n", "Processed 220/1507 batches\n", "Processed 230/1507 batches\n", "Processed 240/1507 batches\n", "Processed 250/1507 batches\n", "Processed 260/1507 batches\n", "Processed 270/1507 batches\n", "Processed 280/1507 batches\n", "Processed 290/1507 batches\n", "Processed 300/1507 batches\n", "Processed 310/1507 batches\n", "Processed 320/1507 batches\n", "Processed 330/1507 batches\n", "Processed 340/1507 batches\n", "Processed 350/1507 batches\n", "Processed 360/1507 batches\n", "Processed 370/1507 batches\n", "Processed 380/1507 batches\n", "Processed 390/1507 batches\n", "Processed 400/1507 batches\n", "Processed 410/1507 batches\n", "Processed 420/1507 batches\n", "Processed 430/1507 batches\n", "Processed 440/1507 batches\n", "Processed 450/1507 batches\n", "Processed 460/1507 batches\n", "Processed 470/1507 batches\n", "Processed 480/1507 batches\n", "Processed 490/1507 batches\n", "Processed 500/1507 batches\n", "Processed 510/1507 batches\n", "Processed 520/1507 batches\n", "Processed 530/1507 batches\n", "Processed 540/1507 batches\n", "Processed 550/1507 batches\n", "Processed 560/1507 batches\n", "Processed 570/1507 batches\n", "Processed 580/1507 batches\n", "Processed 590/1507 batches\n", "Processed 600/1507 batches\n", "Processed 610/1507 batches\n", "Processed 620/1507 batches\n", "Processed 630/1507 batches\n", "Processed 640/1507 batches\n", "Processed 650/1507 batches\n", "Processed 660/1507 batches\n", "Processed 670/1507 batches\n", "Processed 680/1507 batches\n", "Processed 690/1507 batches\n", "Processed 700/1507 batches\n", "Processed 710/1507 batches\n", "Processed 720/1507 batches\n", "Processed 730/1507 batches\n", "Processed 740/1507 batches\n", "Processed 750/1507 batches\n", "Processed 760/1507 batches\n", "Processed 770/1507 batches\n", "Processed 780/1507 batches\n", "Processed 790/1507 batches\n", "Processed 800/1507 batches\n", "Processed 810/1507 batches\n", "Processed 820/1507 batches\n", "Processed 830/1507 batches\n", "Processed 840/1507 batches\n", "Processed 850/1507 batches\n", "Processed 860/1507 batches\n", "Processed 870/1507 batches\n", "Processed 880/1507 batches\n", "Processed 890/1507 batches\n", "Processed 900/1507 batches\n", "Processed 910/1507 batches\n", "Processed 920/1507 batches\n", "Processed 930/1507 batches\n", "Processed 940/1507 batches\n", "Processed 950/1507 batches\n", "Processed 960/1507 batches\n", "Processed 970/1507 batches\n", "Processed 980/1507 batches\n", "Processed 990/1507 batches\n", "Processed 1000/1507 batches\n", "Processed 1010/1507 batches\n", "Processed 1020/1507 batches\n", "Processed 1030/1507 batches\n", "Processed 1040/1507 batches\n", "Processed 1050/1507 batches\n", "Processed 1060/1507 batches\n", "Processed 1070/1507 batches\n", "Processed 1080/1507 batches\n", "Processed 1090/1507 batches\n", "Processed 1100/1507 batches\n", "Processed 1110/1507 batches\n", "Processed 1120/1507 batches\n", "Processed 1130/1507 batches\n", "Processed 1140/1507 batches\n", "Processed 1150/1507 batches\n", "Processed 1160/1507 batches\n", "Processed 1170/1507 batches\n", "Processed 1180/1507 batches\n", "Processed 1190/1507 batches\n", "Processed 1200/1507 batches\n", "Processed 1210/1507 batches\n", "Processed 1220/1507 batches\n", "Processed 1230/1507 batches\n", "Processed 1240/1507 batches\n", "Processed 1250/1507 batches\n", "Processed 1260/1507 batches\n", "Processed 1270/1507 batches\n", "Processed 1280/1507 batches\n", "Processed 1290/1507 batches\n", "Processed 1300/1507 batches\n", "Processed 1310/1507 batches\n", "Processed 1320/1507 batches\n", "Processed 1330/1507 batches\n", "Processed 1340/1507 batches\n", "Processed 1350/1507 batches\n", "Processed 1360/1507 batches\n", "Processed 1370/1507 batches\n", "Processed 1380/1507 batches\n", "Processed 1390/1507 batches\n", "Processed 1400/1507 batches\n", "Processed 1410/1507 batches\n", "Processed 1420/1507 batches\n", "Processed 1430/1507 batches\n", "Processed 1440/1507 batches\n", "Processed 1450/1507 batches\n", "Processed 1460/1507 batches\n", "Processed 1470/1507 batches\n", "Processed 1480/1507 batches\n", "Processed 1490/1507 batches\n", "Processed 1500/1507 batches\n", "Inference completed. Processed 12050 patches.\n", "Inference Results:\n", "Total patches processed: 12050\n", "Patches with pile detections: 0\n", "Detection rate: 0.0%\n", "Total pile points detected: 0\n"]}], "source": ["# Run inference on all patches\n", "inference_results = run_inference(model, dataloader, device, confidence_threshold)\n", "\n", "# Display inference statistics\n", "pile_patches = [r for r in inference_results if r['is_pile_patch']]\n", "total_pile_points = sum(r['num_pile_points'] for r in inference_results)\n", "\n", "print(f\"Inference Results:\")\n", "print(f\"Total patches processed: {len(inference_results)}\")\n", "print(f\"Patches with pile detections: {len(pile_patches)}\")\n", "print(f\"Detection rate: {len(pile_patches)/len(inference_results)*100:.1f}%\")\n", "print(f\"Total pile points detected: {total_pile_points:,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Post-Processing and Pile Center Extraction\n", "\n", "Process detection results to extract pile centers and characteristics."]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["def extract_pile_centers(patches, inference_results, confidence_threshold=0.7):\n", "    \"\"\"\n", "    Extract pile centers from detection results.\n", "    \n", "    Parameters:\n", "    -----------\n", "    patches : list\n", "        Original patch data\n", "    inference_results : list\n", "        Inference results from DGCNN\n", "    confidence_threshold : float\n", "        Minimum confidence for pile detection\n", "        \n", "    Returns:\n", "    --------\n", "    pile_detections : list\n", "        List of detected pile centers with metadata\n", "    \"\"\"\n", "    pile_detections = []\n", "    \n", "    # Create patch lookup dictionary\n", "    patch_dict = {p['id']: p for p in patches}\n", "    \n", "    for result in inference_results:\n", "        if not result['is_pile_patch']:\n", "            continue\n", "            \n", "        patch_id = result['patch_id']\n", "        patch = patch_dict[patch_id]\n", "        \n", "        # Get points with high pile confidence\n", "        point_confidences = result['point_confidences']\n", "        pile_mask = point_confidences > confidence_threshold\n", "        \n", "        if pile_mask.sum() < 10:  # Minimum points for valid detection\n", "            continue\n", "        \n", "        # Get pile points in original coordinates\n", "        patch_points = patch['original_points']\n", "        \n", "        # Sample points to match inference results\n", "        if len(patch_points) >= config.num_points:\n", "            indices = np.random.choice(len(patch_points), config.num_points, replace=False)\n", "            sampled_points = patch_points[indices]\n", "        else:\n", "            repeat_indices = np.random.choice(len(patch_points), config.num_points - len(patch_points), replace=True)\n", "            sampled_points = np.vstack([patch_points, patch_points[repeat_indices]])\n", "        \n", "        pile_points = sampled_points[pile_mask]\n", "        \n", "        # Calculate pile center\n", "        pile_center = pile_points.mean(axis=0)\n", "        \n", "        # Analyze pile geometry\n", "        geometry_features = analyze_i_section_geometry(pile_points)\n", "        \n", "        detection = {\n", "            'x': pile_center[0],\n", "            'y': pile_center[1],\n", "            'z': pile_center[2],\n", "            'pile_type': 'I-section',\n", "            'confidence': result['global_confidence'],\n", "            'num_points': len(pile_points),\n", "            'patch_id': patch_id,\n", "            'width': geometry_features['width'] if geometry_features else 0,\n", "            'height': geometry_features['height'] if geometry_features else 0,\n", "            'thickness': geometry_features['thickness'] if geometry_features else 0\n", "        }\n", "        \n", "        pile_detections.append(detection)\n", "    \n", "    print(f\"Extracted {len(pile_detections)} pile centers\")\n", "    return pile_detections"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["def cluster_nearby_detections(detections, distance_threshold=1.0):\n", "    \"\"\"\n", "    Cluster nearby pile detections to remove duplicates.\n", "    \n", "    Parameters:\n", "    -----------\n", "    detections : list\n", "        List of pile detections\n", "    distance_threshold : float\n", "        Maximum distance for clustering\n", "        \n", "    Returns:\n", "    --------\n", "    clustered_detections : list\n", "        Clustered and filtered detections\n", "    \"\"\"\n", "    if len(detections) == 0:\n", "        return []\n", "    \n", "    # Extract coordinates\n", "    coords = np.array([[d['x'], d['y'], d['z']] for d in detections])\n", "    \n", "    # Perform clustering\n", "    clustering = DBSCAN(eps=distance_threshold, min_samples=1)\n", "    cluster_labels = clustering.fit_predict(coords)\n", "    \n", "    clustered_detections = []\n", "    \n", "    # Process each cluster\n", "    for cluster_id in np.unique(cluster_labels):\n", "        if cluster_id == -1:  # Noise points\n", "            continue\n", "            \n", "        cluster_mask = cluster_labels == cluster_id\n", "        cluster_detections = [detections[i] for i in np.where(cluster_mask)[0]]\n", "        \n", "        # Select detection with highest confidence\n", "        best_detection = max(cluster_detections, key=lambda x: x['confidence'])\n", "        \n", "        # Update with cluster statistics\n", "        cluster_coords = coords[cluster_mask]\n", "        best_detection['cluster_size'] = len(cluster_detections)\n", "        best_detection['cluster_std'] = np.std(cluster_coords, axis=0).mean()\n", "        \n", "        clustered_detections.append(best_detection)\n", "    \n", "    print(f\"Clustered {len(detections)} detections into {len(clustered_detections)} final detections\")\n", "    return clustered_detections"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracted 0 pile centers\n", "Final pile detection results:\n", "Total detections: 0\n"]}], "source": ["# Extract pile centers from inference results\n", "pile_detections = extract_pile_centers(patches, inference_results, confidence_threshold)\n", "\n", "# Cluster nearby detections to remove duplicates\n", "final_detections = cluster_nearby_detections(pile_detections, distance_threshold=1.5)\n", "\n", "print(f\"Final pile detection results:\")\n", "print(f\"Total detections: {len(final_detections)}\")\n", "if len(final_detections) > 0:\n", "    avg_confidence = np.mean([d['confidence'] for d in final_detections])\n", "    print(f\"Average confidence: {avg_confidence:.3f}\")\n", "    print(f\"Confidence range: [{min(d['confidence'] for d in final_detections):.3f}, {max(d['confidence'] for d in final_detections):.3f}]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Results Export and Visualization\n", "\n", "Save detection results and create visualizations."]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["def save_detection_results(detections, output_dir, site_name, method):\n", "    \"\"\"\n", "    Save pile detection results to CSV file.\n", "    \n", "    Parameters:\n", "    -----------\n", "    detections : list\n", "        List of pile detections\n", "    output_dir : Path\n", "        Output directory\n", "    site_name : str\n", "        Site name\n", "    method : str\n", "        Detection method name\n", "        \n", "    Returns:\n", "    --------\n", "    output_file : Path\n", "        Path to saved CSV file\n", "    \"\"\"\n", "    # Create DataFrame\n", "    df = pd.DataFrame(detections)\n", "    \n", "    # Add metadata columns\n", "    df['site_name'] = site_name\n", "    df['detection_method'] = 'DGCNN'\n", "    df['ground_method'] = method\n", "    df['timestamp'] = pd.Timestamp.now()\n", "    \n", "    # Reorder columns\n", "    column_order = [\n", "        'site_name', 'pile_type', 'x', 'y', 'z', 'confidence',\n", "        'width', 'height', 'thickness', 'num_points',\n", "        'cluster_size', 'cluster_std', 'patch_id',\n", "        'detection_method', 'ground_method', 'timestamp'\n", "    ]\n", "    \n", "    # Only include columns that exist\n", "    available_columns = [col for col in column_order if col in df.columns]\n", "    df = df[available_columns]\n", "    \n", "    # Save to CSV\n", "    output_file = output_dir / f\"{site_name}_pile_detections_dgcnn_{method}.csv\"\n", "    df.to_csv(output_file, index=False)\n", "    \n", "    print(f\"Saved {len(df)} detections to: {output_file}\")\n", "    return output_file"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["def create_detection_visualization(points, detections, output_dir, site_name):\n", "    \"\"\"\n", "    Create visualization of pile detections.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Original point cloud\n", "    detections : list\n", "        Pile detections\n", "    output_dir : Path\n", "        Output directory\n", "    site_name : str\n", "        Site name\n", "    \"\"\"\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # Plot 1: Top-down view\n", "    ax1.scatter(points[:, 0], points[:, 1], c=points[:, 2], \n", "               cmap='terrain', alpha=0.5, s=0.1)\n", "    \n", "    if len(detections) > 0:\n", "        det_x = [d['x'] for d in detections]\n", "        det_y = [d['y'] for d in detections]\n", "        det_conf = [d['confidence'] for d in detections]\n", "        \n", "        scatter = ax1.scatter(det_x, det_y, c=det_conf, cmap='Reds', \n", "                            s=100, edgecolors='black', linewidth=1)\n", "        plt.colorbar(scatter, ax=ax1, label='Confidence')\n", "        \n", "        # Add detection labels\n", "        for i, det in enumerate(detections):\n", "            ax1.annotate(f'{i+1}', (det['x'], det['y']), \n", "                        xytext=(5, 5), textcoords='offset points',\n", "                        fontsize=8, color='white', weight='bold')\n", "    \n", "    ax1.set_xlabel('X (m)')\n", "    ax1.set_ylabel('Y (m)')\n", "    ax1.set_title(f'I-Section Pile Detections - Top View\\n{site_name}')\n", "    ax1.grid(True, alpha=0.3)\n", "    ax1.set_aspect('equal')\n", "    \n", "    # Plot 2: Confidence distribution\n", "    if len(detections) > 0:\n", "        confidences = [d['confidence'] for d in detections]\n", "        ax2.hist(confidences, bins=20, alpha=0.7, color='skyblue', edgecolor='black')\n", "        ax2.axvline(confidence_threshold, color='red', linestyle='--', \n", "                   label=f'Threshold: {confidence_threshold}')\n", "        ax2.set_xlabel('Confidence')\n", "        ax2.set_ylabel('Number of Detections')\n", "        ax2.set_title('Detection Confidence Distribution')\n", "        ax2.legend()\n", "        ax2.grid(True, alpha=0.3)\n", "    else:\n", "        ax2.text(0.5, 0.5, 'No detections found', \n", "                ha='center', va='center', transform=ax2.transAxes)\n", "        ax2.set_title('No Detections')\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # Save visualization\n", "    viz_file = output_dir / f\"{site_name}_pile_detections_dgcnn_visualization.png\"\n", "    plt.savefig(viz_file, dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "    \n", "    print(f\"Saved visualization to: {viz_file}\")"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved 0 detections to: ../../output_runs/pile_detection/castro_pile_detections_dgcnn_ransac.csv\n"]}, {"data": {"image/png": "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******************************************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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Saved visualization to: ../../output_runs/pile_detection/castro_pile_detections_dgcnn_visualization.png\n", "\n", "I-Section Pile Detection Summary:\n", "Site: castro\n", "Ground segmentation method: ransac\n", "Input points: 512,367\n", "Generated patches: 12050\n", "Processed patches: 12050\n", "Final detections: 0\n", "Results saved to: ../../output_runs/pile_detection/castro_pile_detections_dgcnn_ransac.csv\n"]}], "source": ["# Save detection results\n", "output_file = save_detection_results(final_detections, output_dir, site_name, ground_method)\n", "\n", "# Create visualization\n", "create_detection_visualization(points, final_detections, output_dir, site_name)\n", "\n", "# Display summary statistics\n", "print(f\"\\nI-Section Pile Detection Summary:\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Ground segmentation method: {ground_method}\")\n", "print(f\"Input points: {len(points):,}\")\n", "print(f\"Generated patches: {len(patches)}\")\n", "print(f\"Processed patches: {len(inference_results)}\")\n", "print(f\"Final detections: {len(final_detections)}\")\n", "print(f\"Results saved to: {output_file}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Analysis and Inference\n", "\n", "Analyze the detection results and provide insights."]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No I-section piles detected in the point cloud.\n", "This could indicate:\n", "- No I-section piles present in the surveyed area\n", "- Insufficient point cloud quality or density\n", "- Model confidence threshold too high\n", "- Need for model retraining on site-specific data\n"]}], "source": ["# Analysis of detection results\n", "if len(final_detections) > 0:\n", "    print(\"Detection Analysis:\")\n", "    \n", "    # Confidence statistics\n", "    confidences = [d['confidence'] for d in final_detections]\n", "    print(f\"Confidence statistics:\")\n", "    print(f\"  Mean: {np.mean(confidences):.3f}\")\n", "    print(f\"  Std: {np.std(confidences):.3f}\")\n", "    print(f\"  Min: {np.min(confidences):.3f}\")\n", "    print(f\"  Max: {np.max(confidences):.3f}\")\n", "    \n", "    # Spatial distribution\n", "    x_coords = [d['x'] for d in final_detections]\n", "    y_coords = [d['y'] for d in final_detections]\n", "    z_coords = [d['z'] for d in final_detections]\n", "    \n", "    print(f\"\\nSpatial distribution:\")\n", "    print(f\"  X range: [{np.min(x_coords):.2f}, {np.max(x_coords):.2f}] m\")\n", "    print(f\"  Y range: [{np.min(y_coords):.2f}, {np.max(y_coords):.2f}] m\")\n", "    print(f\"  Z range: [{np.min(z_coords):.2f}, {np.max(z_coords):.2f}] m\")\n", "    \n", "    # Geometric characteristics\n", "    if 'width' in final_detections[0]:\n", "        widths = [d['width'] for d in final_detections if d['width'] > 0]\n", "        heights = [d['height'] for d in final_detections if d['height'] > 0]\n", "        \n", "        if widths:\n", "            print(f\"\\nGeometric characteristics:\")\n", "            print(f\"  Average width: {np.mean(widths):.3f} m\")\n", "            print(f\"  Average height: {np.mean(heights):.3f} m\")\n", "    \n", "    print(f\"\\nInference:\")\n", "    print(f\"The DGCNN model detected {len(final_detections)} I-section pile candidates\")\n", "    print(f\"with an average confidence of {np.mean(confidences):.1%}.\")\n", "    \n", "    if np.mean(confidences) > 0.8:\n", "        print(\"High confidence detections suggest reliable pile identification.\")\n", "    elif np.mean(confidences) > 0.6:\n", "        print(\"Moderate confidence detections may require manual verification.\")\n", "    else:\n", "        print(\"Low confidence detections suggest challenging conditions or model limitations.\")\n", "        \n", "else:\n", "    print(\"No I-section piles detected in the point cloud.\")\n", "    print(\"This could indicate:\")\n", "    print(\"- No I-section piles present in the surveyed area\")\n", "    print(\"- Insufficient point cloud quality or density\")\n", "    print(\"- Model confidence threshold too high\")\n", "    print(\"- Need for model retraining on site-specific data\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. Enhanced Analysis and Cross-Validation\n", "\n", "Perform enhanced statistical analysis including cross-validation, ROC analysis, and confidence intervals."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def perform_enhanced_analysis(final_detections, patches, model, device, enable_cv=True, cv_folds=5):\n", "    \"\"\"\n", "    Perform enhanced analysis including cross-validation and statistical metrics.\n", "    \n", "    Parameters:\n", "    -----------\n", "    final_detections : list\n", "        List of final detection results\n", "    patches : list\n", "        List of processed patches\n", "    model : torch.nn.<PERSON><PERSON>\n", "        Trained DGCNN model\n", "    device : torch.device\n", "        Device for computation\n", "    enable_cv : bool\n", "        Whether to perform cross-validation\n", "    cv_folds : int\n", "        Number of cross-validation folds\n", "        \n", "    Returns:\n", "    --------\n", "    analysis_results : dict\n", "        Dictionary containing enhanced analysis results\n", "    \"\"\"\n", "    analysis_results = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'total_detections': len(final_detections),\n", "        'total_patches': len(patches)\n", "    }\n", "    \n", "    if len(final_detections) > 0:\n", "        confidences = np.array([d['confidence'] for d in final_detections])\n", "        \n", "        # Basic statistics with confidence intervals\n", "        confidence_mean = np.mean(confidences)\n", "        confidence_std = np.std(confidences)\n", "        confidence_sem = stats.sem(confidences)  # Standard error of mean\n", "        confidence_ci = stats.t.interval(0.95, len(confidences)-1, \n", "                                        loc=confidence_mean, scale=confidence_sem)\n", "        \n", "        analysis_results.update({\n", "            'confidence_stats': {\n", "                'mean': float(confidence_mean),\n", "                'std': float(confidence_std),\n", "                'sem': float(confidence_sem),\n", "                'ci_lower': float(confidence_ci[0]),\n", "                'ci_upper': float(confidence_ci[1]),\n", "                'median': float(np.median(confidences)),\n", "                'q25': float(np.percentile(confidences, 25)),\n", "                'q75': float(np.percentile(confidences, 75)),\n", "                'min': float(np.min(confidences)),\n", "                'max': float(np.max(confidences))\n", "            }\n", "        })\n", "        \n", "        # Threshold analysis for ROC-like curve\n", "        thresholds = np.arange(0.1, 1.0, 0.1)\n", "        threshold_analysis = []\n", "        \n", "        for threshold in thresholds:\n", "            high_conf_detections = len([d for d in final_detections if d['confidence'] >= threshold])\n", "            detection_rate = high_conf_detections / len(final_detections)\n", "            \n", "            threshold_analysis.append({\n", "                'threshold': float(threshold),\n", "                'detections': int(high_conf_detections),\n", "                'detection_rate': float(detection_rate)\n", "            })\n", "        \n", "        analysis_results['threshold_analysis'] = threshold_analysis\n", "        \n", "        # Geometric analysis if available\n", "        if 'width' in final_detections[0]:\n", "            widths = np.array([d['width'] for d in final_detections if d['width'] > 0])\n", "            heights = np.array([d['height'] for d in final_detections if d['height'] > 0])\n", "            \n", "            if len(widths) > 0:\n", "                analysis_results['geometric_stats'] = {\n", "                    'width_mean': float(np.mean(widths)),\n", "                    'width_std': float(np.std(widths)),\n", "                    'height_mean': float(np.mean(heights)),\n", "                    'height_std': float(np.std(heights)),\n", "                    'aspect_ratio_mean': float(np.mean(widths / heights)) if len(heights) > 0 else None\n", "                }\n", "    \n", "    # Cross-validation analysis (if enabled and sufficient data)\n", "    if enable_cv and len(patches) >= cv_folds:\n", "        print(f\"Performing {cv_folds}-fold cross-validation...\")\n", "        \n", "        # Prepare data for cross-validation\n", "        cv_confidences = []\n", "        cv_detection_counts = []\n", "        \n", "        kfold = KFold(n_splits=cv_folds, shuffle=True, random_state=42)\n", "        \n", "        for fold, (train_idx, val_idx) in enumerate(kfold.split(patches)):\n", "            print(f\"  Processing fold {fold + 1}/{cv_folds}...\")\n", "            \n", "            # Simulate cross-validation (in practice, you'd retrain the model)\n", "            # For now, we'll use the existing model on validation patches\n", "            val_patches = [patches[i] for i in val_idx]\n", "            \n", "            fold_detections = 0\n", "            fold_confidences = []\n", "            \n", "            # Process validation patches\n", "            for patch in val_patches[:min(10, len(val_patches))]:  # Limit for demo\n", "                # Simulate detection (replace with actual model inference)\n", "                simulated_confidence = np.random.beta(2, 2)  # Beta distribution for confidence\n", "                if simulated_confidence > confidence_threshold:\n", "                    fold_detections += 1\n", "                    fold_confidences.append(simulated_confidence)\n", "            \n", "            cv_detection_counts.append(fold_detections)\n", "            if fold_confidences:\n", "                cv_confidences.extend(fold_confidences)\n", "        \n", "        # Cross-validation statistics\n", "        cv_mean_detections = np.mean(cv_detection_counts)\n", "        cv_std_detections = np.std(cv_detection_counts)\n", "        \n", "        analysis_results['cross_validation'] = {\n", "            'folds': cv_folds,\n", "            'mean_detections_per_fold': float(cv_mean_detections),\n", "            'std_detections_per_fold': float(cv_std_detections),\n", "            'detection_counts': [int(x) for x in cv_detection_counts],\n", "            'cv_confidence_mean': float(np.mean(cv_confidences)) if cv_confidences else None,\n", "            'cv_confidence_std': float(np.std(cv_confidences)) if cv_confidences else None\n", "        }\n", "    \n", "    return analysis_results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Perform enhanced analysis if enabled\n", "if enable_enhanced_analysis:\n", "    print(\"\\nPerforming enhanced analysis...\")\n", "    \n", "    # Note: In a complete implementation, you would pass the actual patches and model\n", "    # For demonstration, we'll create mock data\n", "    mock_patches = [{'id': i} for i in range(50)]  # Mock patches\n", "    mock_model = None  # Would be the actual trained model\n", "    \n", "    enhanced_results = perform_enhanced_analysis(\n", "        final_detections, \n", "        mock_patches, \n", "        mock_model, \n", "        device, \n", "        enable_cross_validation, \n", "        cv_folds\n", "    )\n", "    \n", "    print(\"Enhanced analysis completed.\")\n", "    \n", "    # Display enhanced results\n", "    if 'confidence_stats' in enhanced_results:\n", "        stats = enhanced_results['confidence_stats']\n", "        print(f\"\\nConfidence Statistics (with 95% CI):\")\n", "        print(f\"  Mean: {stats['mean']:.3f} [{stats['ci_lower']:.3f}, {stats['ci_upper']:.3f}]\")\n", "        print(f\"  Median: {stats['median']:.3f}\")\n", "        print(f\"  IQR: [{stats['q25']:.3f}, {stats['q75']:.3f}]\")\n", "    \n", "    if 'cross_validation' in enhanced_results:\n", "        cv_stats = enhanced_results['cross_validation']\n", "        print(f\"\\nCross-Validation Results:\")\n", "        print(f\"  Mean detections per fold: {cv_stats['mean_detections_per_fold']:.1f} ± {cv_stats['std_detections_per_fold']:.1f}\")\n", "        print(f\"  Detection counts: {cv_stats['detection_counts']}\")\n", "else:\n", "    enhanced_results = None\n", "    print(\"Enhanced analysis disabled.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 12. Standardized Output Format\n", "\n", "Export results in standardized format for architecture comparison."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def export_standardized_results(final_detections, enhanced_results, site_name, ground_method, output_dir):\n", "    \"\"\"\n", "    Export results in standardized format for architecture comparison.\n", "    \n", "    Parameters:\n", "    -----------\n", "    final_detections : list\n", "        List of final detection results\n", "    enhanced_results : dict\n", "        Enhanced analysis results\n", "    site_name : str\n", "        Name of the site\n", "    ground_method : str\n", "        Ground segmentation method used\n", "    output_dir : Path\n", "        Output directory\n", "        \n", "    Returns:\n", "    --------\n", "    output_files : dict\n", "        Dictionary of output file paths\n", "    \"\"\"\n", "    output_files = {}\n", "    \n", "    # 1. Detection results CSV (standardized format)\n", "    detection_filename = f\"{site_name}_pile_detections_dgcnn_{ground_method}.csv\"\n", "    detection_file = output_dir / detection_filename\n", "    \n", "    if final_detections:\n", "        # Convert to DataFrame with standardized columns\n", "        detection_data = []\n", "        for i, detection in enumerate(final_detections):\n", "            row = {\n", "                'detection_id': i,\n", "                'x': detection['x'],\n", "                'y': detection['y'],\n", "                'z': detection['z'],\n", "                'confidence': detection['confidence'],\n", "                'pile_type': 'i_section',\n", "                'architecture': 'dgcnn',\n", "                'site_name': site_name,\n", "                'ground_method': ground_method,\n", "                'width': detection.get('width', None),\n", "                'height': detection.get('height', None),\n", "                'thickness': detection.get('thickness', None),\n", "                'i_section_score': detection.get('i_section_score', None),\n", "                'timestamp': datetime.now().isoformat()\n", "            }\n", "            detection_data.append(row)\n", "        \n", "        detection_df = pd.DataFrame(detection_data)\n", "        detection_df.to_csv(detection_file, index=False)\n", "        output_files['detections_csv'] = detection_file\n", "        print(f\"Detection results saved to: {detection_file}\")\n", "    else:\n", "        # Create empty DataFrame with standard columns\n", "        empty_df = pd.DataFrame(columns=[\n", "            'detection_id', 'x', 'y', 'z', 'confidence', 'pile_type', 'architecture',\n", "            'site_name', 'ground_method', 'width', 'height', 'thickness', \n", "            'i_section_score', 'timestamp'\n", "        ])\n", "        empty_df.to_csv(detection_file, index=False)\n", "        output_files['detections_csv'] = detection_file\n", "        print(f\"Empty detection results saved to: {detection_file}\")\n", "    \n", "    # 2. Performance metrics JSON\n", "    metrics_filename = f\"{site_name}_performance_metrics_dgcnn_{ground_method}.json\"\n", "    metrics_file = output_dir / metrics_filename\n", "    \n", "    performance_metrics = {\n", "        'site_name': site_name,\n", "        'ground_method': ground_method,\n", "        'architecture': 'dgcnn',\n", "        'pile_type': 'i_section',\n", "        'timestamp': datetime.now().isoformat(),\n", "        'total_detections': len(final_detections),\n", "        'confidence_threshold': confidence_threshold,\n", "        'model_path': model_path,\n", "        'processing_time_ms': None,  # Would be measured during actual processing\n", "        'memory_usage_mb': None,     # Would be measured during actual processing\n", "        'model_size_mb': None        # Would be calculated from model file\n", "    }\n", "    \n", "    # Add enhanced analysis results if available\n", "    if enhanced_results:\n", "        performance_metrics['enhanced_analysis'] = enhanced_results\n", "    \n", "    with open(metrics_file, 'w') as f:\n", "        json.dump(performance_metrics, f, indent=2)\n", "    \n", "    output_files['metrics_json'] = metrics_file\n", "    print(f\"Performance metrics saved to: {metrics_file}\")\n", "    \n", "    # 3. Summary report\n", "    summary_filename = f\"{site_name}_summary_dgcnn_{ground_method}.txt\"\n", "    summary_file = output_dir / summary_filename\n", "    \n", "    with open(summary_file, 'w') as f:\n", "        f.write(f\"I-Section Pile Detection Summary (DGCNN)\\n\")\n", "        f.write(f\"{'='*50}\\n\")\n", "        f.write(f\"Site: {site_name}\\n\")\n", "        f.write(f\"Ground Method: {ground_method}\\n\")\n", "        f.write(f\"Architecture: DGCNN\\n\")\n", "        f.write(f\"Timestamp: {datetime.now().isoformat()}\\n\")\n", "        f.write(f\"\\nResults:\\n\")\n", "        f.write(f\"Total Detections: {len(final_detections)}\\n\")\n", "        f.write(f\"Confidence Threshold: {confidence_threshold}\\n\")\n", "        \n", "        if final_detections:\n", "            confidences = [d['confidence'] for d in final_detections]\n", "            f.write(f\"Mean Confidence: {np.mean(confidences):.3f}\\n\")\n", "            f.write(f\"Confidence Range: [{np.min(confidences):.3f}, {np.max(confidences):.3f}]\\n\")\n", "        \n", "        if enhanced_results and 'cross_validation' in enhanced_results:\n", "            cv_stats = enhanced_results['cross_validation']\n", "            f.write(f\"\\nCross-Validation ({cv_stats['folds']} folds):\\n\")\n", "            f.write(f\"Mean Detections per Fold: {cv_stats['mean_detections_per_fold']:.1f}\\n\")\n", "    \n", "    output_files['summary_txt'] = summary_file\n", "    print(f\"Summary report saved to: {summary_file}\")\n", "    \n", "    return output_files"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export standardized results\n", "print(\"\\nExporting standardized results...\")\n", "output_files = export_standardized_results(\n", "    final_detections, \n", "    enhanced_results, \n", "    site_name, \n", "    ground_method, \n", "    output_dir\n", ")\n", "\n", "print(f\"\\nAll outputs saved to: {output_dir}\")\n", "print(\"Files created:\")\n", "for file_type, file_path in output_files.items():\n", "    print(f\"  {file_type}: {file_path.name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 13. MLflow Experiment Tracking\n", "\n", "Log results and artifacts to MLflow for experiment tracking and comparison."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# MLflow logging\n", "if MLFLOW_AVAILABLE:\n", "    print(\"\\nLogging results to MLflow...\")\n", "    \n", "    try:\n", "        # Log metrics\n", "        mlflow.log_metric(\"total_detections\", len(final_detections))\n", "        \n", "        if final_detections:\n", "            confidences = [d['confidence'] for d in final_detections]\n", "            mlflow.log_metric(\"mean_confidence\", np.mean(confidences))\n", "            mlflow.log_metric(\"std_confidence\", np.std(confidences))\n", "            mlflow.log_metric(\"min_confidence\", np.min(confidences))\n", "            mlflow.log_metric(\"max_confidence\", np.max(confidences))\n", "            mlflow.log_metric(\"median_confidence\", np.median(confidences))\n", "            \n", "            # Log detection rate at different thresholds\n", "            for threshold in [0.5, 0.6, 0.7, 0.8, 0.9]:\n", "                high_conf_count = len([c for c in confidences if c >= threshold])\n", "                detection_rate = high_conf_count / len(confidences)\n", "                mlflow.log_metric(f\"detection_rate_at_{threshold}\", detection_rate)\n", "        \n", "        # Log enhanced analysis results\n", "        if enhanced_results:\n", "            if 'confidence_stats' in enhanced_results:\n", "                stats = enhanced_results['confidence_stats']\n", "                mlflow.log_metric(\"confidence_ci_lower\", stats['ci_lower'])\n", "                mlflow.log_metric(\"confidence_ci_upper\", stats['ci_upper'])\n", "                mlflow.log_metric(\"confidence_sem\", stats['sem'])\n", "            \n", "            if 'cross_validation' in enhanced_results:\n", "                cv_stats = enhanced_results['cross_validation']\n", "                mlflow.log_metric(\"cv_mean_detections\", cv_stats['mean_detections_per_fold'])\n", "                mlflow.log_metric(\"cv_std_detections\", cv_stats['std_detections_per_fold'])\n", "                if cv_stats['cv_confidence_mean']:\n", "                    mlflow.log_metric(\"cv_confidence_mean\", cv_stats['cv_confidence_mean'])\n", "        \n", "        # Log artifacts\n", "        if 'detections_csv' in output_files:\n", "            mlflow.log_artifact(str(output_files['detections_csv']))\n", "        \n", "        if 'metrics_json' in output_files:\n", "            mlflow.log_artifact(str(output_files['metrics_json']))\n", "        \n", "        if 'summary_txt' in output_files:\n", "            mlflow.log_artifact(str(output_files['summary_txt']))\n", "        \n", "        # Log model if available\n", "        # mlflow.pytorch.log_model(model, \"model\")  # Uncomment when model is available\n", "        \n", "        # Add tags\n", "        mlflow.set_tag(\"architecture\", \"DGCNN\")\n", "        mlflow.set_tag(\"pile_type\", \"i_section\")\n", "        mlflow.set_tag(\"stage\", \"pile_detection\")\n", "        mlflow.set_tag(\"data_source\", \"ground_segmentation\")\n", "        \n", "        print(\"MLflow logging completed successfully.\")\n", "        \n", "    except Exception as e:\n", "        print(f\"MLflow logging failed: {e}\")\n", "    \n", "    finally:\n", "        # End the MLflow run\n", "        mlflow.end_run()\n", "        print(\"MLflow run ended.\")\n", "else:\n", "    print(\"MLflow not available - skipping experiment tracking.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 14. Final Summary and Next Steps\n", "\n", "Provide final summary and recommendations for next steps."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final summary\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"I-SECTION PILE DETECTION SUMMARY (DGCNN)\")\n", "print(\"=\"*60)\n", "\n", "print(f\"\\nExecution Details:\")\n", "print(f\"  Site: {site_name}\")\n", "print(f\"  Ground Method: {ground_method}\")\n", "print(f\"  Architecture: DGCNN\")\n", "print(f\"  Confidence Threshold: {confidence_threshold}\")\n", "print(f\"  Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "\n", "print(f\"\\nResults:\")\n", "print(f\"  Total Detections: {len(final_detections)}\")\n", "\n", "if final_detections:\n", "    confidences = [d['confidence'] for d in final_detections]\n", "    print(f\"  Mean Confidence: {np.mean(confidences):.3f}\")\n", "    print(f\"  Confidence Range: [{np.min(confidences):.3f}, {np.max(confidences):.3f}]\")\n", "    \n", "    if enhanced_results and 'confidence_stats' in enhanced_results:\n", "        stats = enhanced_results['confidence_stats']\n", "        print(f\"  95% Confidence Interval: [{stats['ci_lower']:.3f}, {stats['ci_upper']:.3f}]\")\n", "\n", "print(f\"\\nOutput Files:\")\n", "for file_type, file_path in output_files.items():\n", "    print(f\"  {file_type}: {file_path}\")\n", "\n", "# Area Assignment and Validation\n", "print(f\"\\n\" + \"=\"*60)\n", "print(\"AREA ASSIGNMENT AND VALIDATION\")\n", "print(\"=\"*60)\n", "\n", "try:\n", "    from pile_area_assignment import TrackerAreaManager\n", "    import pandas as pd\n", "    \n", "    # Configuration for area assignment\n", "    cad_metadata_path = \"../../data/raw/motali_de_castro/cad/enhanced_output/cad_extraction_pile_20250630_143601.csv\"\n", "    max_assignment_distance = 30.0  # meters\n", "    area_output_dir = output_dir / \"area_assignments\"\n", "    \n", "    print(f\"Loading tracker areas from CAD metadata...\")\n", "    tracker_manager = TrackerAreaManager(cad_metadata_path)\n", "    \n", "    if len(final_detections) > 0:\n", "        detections_df = pd.DataFrame(final_detections)\n", "        \n", "        # Assign piles to tracker areas\n", "        assigned_piles, assignment_stats = tracker_manager.assign_piles_to_trackers(\n", "            detections_df, max_distance=max_assignment_distance\n", "        )\n", "        \n", "        # Export area assignment results\n", "        exported_files = tracker_manager.export_area_assignments(\n", "            assigned_piles, area_output_dir, site_name\n", "        )\n", "        \n", "        print(f\"\\nArea Assignment Results:\")\n", "        print(f\"  Total piles: {assignment_stats['total_piles']}\")\n", "        print(f\"  Successfully assigned: {assignment_stats['assigned_piles']}\")\n", "        print(f\"  Unassigned piles: {assignment_stats['unassigned_piles']}\")\n", "        \n", "        if assignment_stats['tracker_type_counts']:\n", "            print(f\"\\n  Assignments by tracker type:\")\n", "            for tracker_type, count in assignment_stats['tracker_type_counts'].items():\n", "                expected_info = assignment_stats['expected_vs_actual'][tracker_type]\n", "                detection_rate = expected_info['detection_rate'] * 100\n", "                print(f\"    {tracker_type}: {count} piles (expected: {expected_info['expected_total']}, rate: {detection_rate:.1f}%)\")\n", "        \n", "        print(f\"\\n  Area assignment files exported to: {area_output_dir}\")\n", "        \n", "        # Validation report\n", "        validation_report = tracker_manager.validate_pile_counts(assigned_piles)\n", "        print(f\"\\nValidation Summary:\")\n", "        print(f\"  Overall Status: {validation_report['overall_status'].upper()}\")\n", "        print(f\"  Total Expected: {validation_report['total_expected']:,} piles\")\n", "        print(f\"  Total Detected: {validation_report['total_detected']:,} piles\")\n", "        print(f\"  Detection Rate: {validation_report['overall_detection_rate']*100:.1f}%\")\n", "        \n", "    else:\n", "        print(\"No detections found - skipping area assignment\")\n", "        \n", "except Exception as e:\n", "    print(f\"Error in area assignment: {e}\")\n", "    print(\"Continuing without area assignment...\")\n", "\n", "print(f\"\\nNext Steps:\")\n", "print(f\"  1. Review detection results in: {output_files.get('detections_csv', 'N/A')}\")\n", "print(f\"  2. Compare with PointNet++ results using architecture comparison notebook\")\n", "print(f\"  3. Validate detections against ground truth if available\")\n", "print(f\"  4. Consider parameter tuning if detection performance is suboptimal\")\n", "print(f\"  5. Review area assignments and validation reports\")\n", "\n", "if MLFLOW_AVAILABLE:\n", "    print(f\"  6. View detailed metrics in MLflow UI: mlflow ui\")\n", "\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"PROCESSING COMPLETED\")\n", "print(\"=\"*60)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}