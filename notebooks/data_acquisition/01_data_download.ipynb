{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Project Data Downloader (Bare Minimum)\n", "Downloads point cloud, CAD, and ortho data using AWS S3 CLI or `wget` for HTTP links.\n", "\n", "## Research Context\n", "\n", "**Thesis Title**: Monitoring Structural Integrity Using Machine Learning and Remotely Sensed Imagery\n", "\n", "**Research Objective**: Comparative analysis of traditional vs machine learning methods for solar foundation quality assessment\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025\n", "**Project**: As-Built Foundation Analysis\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["import subprocess\n", "from pathlib import Path\n", "\n", "# Setup base path\n", "base_path = Path(\"../../data/raw\")\n", "base_path.mkdir(parents=True, exist_ok=True)\n", "\n", "def run(cmd):\n", "    cmd = cmd.strip()\n", "\n", "    if cmd.startswith(\"aws s3 cp --recursive\"):\n", "        # Format: aws s3 cp --recursive <src> <dest>\n", "        parts = cmd.split()\n", "        if len(parts) < 5:\n", "            raise ValueError(\"Invalid S3 command. Expecting: aws s3 cp --recursive <src> <dest>\")\n", "        dest_path = Path(parts[-1]).expanduser()\n", "        dest_path.mkdir(parents=True, exist_ok=True)\n", "\n", "        if any(dest_path.iterdir()):\n", "            print(f\"Skipping S3 download, folder already populated: {dest_path}\")\n", "            return\n", "\n", "    elif cmd.startswith(\"wget\"):\n", "        # Format: wget <url> -O <dest>\n", "        parts = cmd.split()\n", "        if \"-O\" not in parts:\n", "            raise ValueError(\"wget command must include '-O <dest_path>'\")\n", "        dest_index = parts.index(\"-O\") + 1\n", "        if dest_index >= len(parts):\n", "            raise ValueError(\"Missing destination path after '-O'\")\n", "        dest_path = Path(parts[dest_index]).expanduser()\n", "        dest_path.parent.mkdir(parents=True, exist_ok=True)\n", "\n", "        if dest_path.exists():\n", "            print(f\"Skipping wget download, file already exists: {dest_path}\")\n", "            return\n", "\n", "    else:\n", "        raise ValueError(\"Only 'aws s3 cp --recursive' and 'wget -O <dest>' supported\")\n", "\n", "    # Run the command\n", "    print(f\"Downloading to: {dest_path}\")\n", "    subprocess.run(cmd, shell=True, check=True)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Skipping download, folder already populated: ../../data/raw/motali_de_castro/pointcloud\n", "Skipping download, folder already populated: ../../data/raw/motali_de_castro/cad\n", "Skipping download, folder already populated: ../../data/raw/motali_de_castro/ortho\n"]}], "source": ["# 1. <PERSON><PERSON><PERSON> - ENEL\n", "proj = base_path / \"motali_de_castro\"\n", "proj.mkdir(exist_ok=True)\n", "run(\"aws s3 cp --recursive s3://preetam-filezilla-test/Castro/Pointcloud/ {}/pointcloud/\".format(proj))\n", "run(\"aws s3 cp --recursive s3://ftp-enel/montalto_di_castro-italy/2025-01-30/CAD/ {}/cad/\".format(proj))\n", "run(\"aws s3 cp --recursive s3://preetam-filezilla-test/Castro/Ortho/ {}/ortho/\".format(proj))\n", "\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Skipping S3 download, folder already populated: ../../data/raw/mudjar_enel/cad\n"]}], "source": ["# 2. <PERSON><PERSON><PERSON> - ENEL\n", "proj = base_path / \"mudjar_enel\"\n", "proj.mkdir(exist_ok=True)\n", "#run(\"wget -O {}/pointcloud.las 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las'\".format(proj))\n", "run(\"aws s3 cp --recursive s3://ftp-enel/mudejar-spain/ {}/cad/\".format(proj))\n", "#run(\"wget -O {}/ortho.tif 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las'\".format(proj))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Skipping S3 download, folder already populated: ../../data/raw/piani_di_giorgio/cad\n"]}], "source": ["# 3. <PERSON><PERSON> - ENEL\n", "proj = base_path / \"piani_di_giorgio\"\n", "proj.mkdir(exist_ok=True)\n", "#run(\"wget -O {}/pointcloud.las 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Paini_Di_Di_<PERSON>/Flight12/<PERSON>_Fly12_pointcloud.las'\".format(proj))\n", "run(\"aws s3 cp --recursive s3://ftp-enel/pian_di_di_giorgio-italy/2024-05-15/ {}/cad/\".format(proj))\n", "#run(\"wget -O {}/ortho.tif 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Paini_Di_Di_Giorgio/Flight12/<PERSON>_Fly12_Ortho.tif'\".format(proj))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Skipping S3 download, folder already populated: ../../data/raw/sunstreams_mccarthy/pointcloud\n", "Skipping S3 download, folder already populated: ../../data/raw/sunstreams_mccarthy/cad\n", "Skipping S3 download, folder already populated: ../../data/raw/sunstreams_mccarthy/ortho\n"]}], "source": ["# 4. Sunstreams Project - McCarthy\n", "proj = base_path / \"sunstreams_mccarthy\"\n", "proj.mkdir(exist_ok=True)\n", "run(\"aws s3 cp --recursive s3://preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/ {}/pointcloud/\".format(proj))\n", "run(\"aws s3 cp --recursive s3://ftp-mccarthy/CAD Files/ {}/cad/\".format(proj))\n", "run(\"aws s3 cp --recursive s3://preetam-filezilla-test/McCarthy_Fly2/RGB_Ortho/ {}/ortho/\".format(proj))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Skipping S3 download, folder already populated: ../../data/raw/althea_rpcs/pointcloud\n", "Skipping S3 download, folder already populated: ../../data/raw/althea_rpcs/cad\n", "Skipping S3 download, folder already populated: ../../data/raw/althea_rpcs/ortho\n"]}], "source": ["# 5. <PERSON><PERSON> - RPC<PERSON>\n", "proj = base_path / \"althea_rpcs\"\n", "proj.mkdir(exist_ok=True)\n", "run(\"aws s3 cp --recursive s3://preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/ {}/pointcloud/\".format(proj))\n", "run(\"aws s3 cp --recursive s3://ftp-rpcs/Althea/CAD Files/ {}/cad/\".format(proj))\n", "run(\"aws s3 cp --recursive s3://preetam-filezilla-test/RCPS/Updated_031024/Ortho/ {}/ortho/\".format(proj))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Skipping S3 download, folder already populated: ../../data/raw/nortan_res/cad\n"]}], "source": ["# 6. <PERSON><PERSON> - R<PERSON> Renewables\n", "proj = base_path / \"nortan_res\"\n", "proj.mkdir(exist_ok=True)\n", "#run(\"aws s3 cp --recursive 's3://ftp-upload-images/Data files to GIS Team/CPM & CQM/2025/RES/Block11/Pointcloud/' {}/pointcloud/\".format(proj))\n", "run(\"aws s3 cp --recursive s3://preetam-filezilla-test/RES_Renewable/BLOCK_11/CAD/ {}/cad/\".format(proj))\n", "#run(\"wget -O {}/ortho.tif 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2025/RES/Block11/RSE_Block11_ortho.tif'\".format(proj))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current\n", "                                 Dload  Upload   Total   Spent    Left  Speed\n", "100  4734  100  4734    0     0   8419      0 --:--:-- --:--:-- --:--:--  8622Password:\n"]}], "source": ["# Install rclone if not already available\n", "!curl https://rclone.org/install.sh | sudo bash"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2K\u001b[1GTransferred:   \t          0 B / 0 B, -, 0 B/s, ETA -\n", "Elapsed time:         0.4s\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t          0 B / 0 B, -, 0 B/s, ETA -\n", "Elapsed time:         0.9s\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t          0 B / 0 B, -, 0 B/s, ETA -\n", "Elapsed time:         1.4s\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t          0 B / 0 B, -, 0 B/s, ETA -\n", "Checks:                 1 / 1, 100%\n", "Elapsed time:         1.9s\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t          0 B / 232.689 MiB, 0%, 0 B/s, ETA -\n", "Checks:                 3 / 3, 100%\n", "Transferred:            0 / 6, 0%\n", "Elapsed time:         2.4s\n", "Transferring:\n", " *     cad/gre.eec.d.00.it.p.13316.00.066.05.pdf:  0% /13.684Mi, 0/s, -\n", " *       cad/enel-6319_pian_di_giorgio-wdt-1.pdf:  0% /4.387Mi, 0/s, -\n", " * cad/gre.eec.d.00.it.p.…t_plan_of_pv_field.pdf:  0% /13.684Mi, 0/s, -\n", " *     cad/gre.eec.d.00.it.p.13316.00.067.00.pdf:  0% /5.620Mi, 0/s, -\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t    2.492 MiB / 232.689 MiB, 1%, 0 B/s, ETA -\n", "Checks:                 3 / 3, 100%\n", "Transferred:            0 / 6, 0%\n", "Elapsed time:         2.9s\n", "Transferring:\n", " *     cad/gre.eec.d.00.it.p.13316.00.066.05.pdf:  0% /13.684Mi, 0/s, -\n", " *       cad/enel-6319_pian_di_giorgio-wdt-1.pdf: 11% /4.387Mi, 0/s, -\n", " * cad/gre.eec.d.00.it.p.…t_plan_of_pv_field.pdf:  0% /13.684Mi, 0/s, -\n", " *     cad/gre.eec.d.00.it.p.13316.00.067.00.pdf: 35% /5.620Mi, 0/s, -\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t    5.395 MiB / 232.689 MiB, 2%, 3.259 MiB/s, ETA 1m9s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            0 / 6, 0%\n", "Elapsed time:         3.4s\n", "Transferring:\n", " *     cad/gre.eec.d.00.it.p.13316.00.066.05.pdf:  0% /13.684Mi, 0/s, -\n", " *       cad/enel-6319_pian_di_giorgio-wdt-1.pdf: 22% /4.387Mi, 1019.980Ki/s, 3s\n", " * cad/gre.eec.d.00.it.p.…t_plan_of_pv_field.pdf:  2% /13.684Mi, 0/s, -\n", " *     cad/gre.eec.d.00.it.p.13316.00.067.00.pdf: 71% /5.620Mi, 2.994Mi/s, 0s\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t   15.484 MiB / 232.689 MiB, 7%, 3.259 MiB/s, ETA 1m6s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            0 / 6, 0%\n", "Elapsed time:         3.9s\n", "Transferring:\n", " *     cad/gre.eec.d.00.it.p.13316.00.066.05.pdf: 18% /13.684Mi, 0/s, -\n", " *       cad/enel-6319_pian_di_giorgio-wdt-1.pdf: 45% /4.387Mi, 1019.980Ki/s, 2s\n", " * cad/gre.eec.d.00.it.p.…t_plan_of_pv_field.pdf: 43% /13.684Mi, 0/s, -\n", " *     cad/gre.eec.d.00.it.p.13316.00.067.00.pdf: 88% /5.620Mi, 2.994Mi/s, 0s\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t   24.639 MiB / 232.689 MiB, 11%, 8.141 MiB/s, ETA 25s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            0 / 6, 0%\n", "Elapsed time:         4.4s\n", "Transferring:\n", " *     cad/gre.eec.d.00.it.p.13316.00.066.05.pdf: 51% /13.684Mi, 2.264Mi/s, 2s\n", " *       cad/enel-6319_pian_di_giorgio-wdt-1.pdf: 68% /4.387Mi, 1021.990Ki/s, 1s\n", " * cad/gre.eec.d.00.it.p.…t_plan_of_pv_field.pdf: 65% /13.684Mi, 4.000Mi/s, 1s\n", " *     cad/gre.eec.d.00.it.p.13316.00.067.00.pdf:100% /5.620Mi, 2.809Mi/s, 0s\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t   34.123 MiB / 232.689 MiB, 15%, 8.141 MiB/s, ETA 24s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            0 / 6, 0%\n", "Elapsed time:         4.9s\n", "Transferring:\n", " *     cad/gre.eec.d.00.it.p.13316.00.066.05.pdf: 83% /13.684Mi, 2.264Mi/s, 0s\n", " *       cad/enel-6319_pian_di_giorgio-wdt-1.pdf:100% /4.387Mi, 1021.990Ki/s, 0s\n", " * cad/gre.eec.d.00.it.p.…t_plan_of_pv_field.pdf: 92% /13.684Mi, 4.000Mi/s, 0s\n", " *     cad/gre.eec.d.00.it.p.13316.00.067.00.pdf:100% /5.620Mi, 2.809Mi/s, 0s\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t   37.374 MiB / 232.689 MiB, 16%, 11.630 MiB/s, ETA 16s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            0 / 6, 0%\n", "Elapsed time:         5.4s\n", "Transferring:\n", " *     cad/gre.eec.d.00.it.p.13316.00.066.05.pdf:100% /13.684Mi, 4.561Mi/s, 0s\n", " *       cad/enel-6319_pian_di_giorgio-wdt-1.pdf:100% /4.387Mi, 1.462Mi/s, 0s\n", " * cad/gre.eec.d.00.it.p.…t_plan_of_pv_field.pdf:100% /13.684Mi, 4.561Mi/s, 0s\n", " *     cad/gre.eec.d.00.it.p.13316.00.067.00.pdf:100% /5.620Mi, 1.873Mi/s, 0s\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t   37.374 MiB / 232.689 MiB, 16%, 11.630 MiB/s, ETA 16s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            1 / 6, 17%\n", "Elapsed time:         5.9s\n", "Transferring:\n", " *     cad/gre.eec.d.00.it.p.13316.00.066.05.pdf:100% /13.684Mi, 4.561Mi/s, 0s\n", " *       cad/enel-6319_pian_di_giorgio-wdt-1.pdf:100% /4.387Mi, 1.462Mi/s, 0s\n", " * cad/gre.eec.d.00.it.p.…t_plan_of_pv_field.pdf:100% /13.684Mi, 4.561Mi/s, 0s\n", " * cad/gre.eec.d.00.it.p.…rk_and_cable_paths.pdf:  0% /44.467Mi, 0/s, -\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t   37.374 MiB / 232.689 MiB, 16%, 9.535 MiB/s, ETA 20s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            1 / 6, 17%\n", "Elapsed time:         6.4s\n", "Transferring:\n", " *     cad/gre.eec.d.00.it.p.13316.00.066.05.pdf:100% /13.684Mi, 3.421Mi/s, 0s\n", " *       cad/enel-6319_pian_di_giorgio-wdt-1.pdf:100% /4.387Mi, 1.097Mi/s, 0s\n", " * cad/gre.eec.d.00.it.p.…t_plan_of_pv_field.pdf:100% /13.684Mi, 3.421Mi/s, 0s\n", " * cad/gre.eec.d.00.it.p.…rk_and_cable_paths.pdf:  0% /44.467Mi, 0/s, -\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t   37.374 MiB / 232.689 MiB, 16%, 9.535 MiB/s, ETA 20s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            2 / 6, 33%\n", "Elapsed time:         6.9s\n", "Transferring:\n", " *     cad/gre.eec.d.00.it.p.13316.00.066.05.pdf:100% /13.684Mi, 3.421Mi/s, 0s\n", " *       cad/enel-6319_pian_di_giorgio-wdt-1.pdf:100% /4.387Mi, 1.097Mi/s, 0s\n", " * cad/gre.eec.d.00.it.p.…rk_and_cable_paths.pdf:  0% /44.467Mi, 0/s, -\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf:  0% /150.848Mi, 0/s, -\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t   39.776 MiB / 232.689 MiB, 17%, 7.628 MiB/s, ETA 25s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            3 / 6, 50%\n", "Elapsed time:         7.4s\n", "Transferring:\n", " *       cad/enel-6319_pian_di_giorgio-wdt-1.pdf:100% /4.387Mi, 898.354Ki/s, 0s\n", " * cad/gre.eec.d.00.it.p.…rk_and_cable_paths.pdf:  5% /44.467Mi, 0/s, -\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf:  0% /150.848Mi, 0/s, -\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t   44.307 MiB / 232.689 MiB, 19%, 7.628 MiB/s, ETA 24s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            3 / 6, 50%\n", "Elapsed time:         7.9s\n", "Transferring:\n", " *       cad/enel-6319_pian_di_giorgio-wdt-1.pdf:100% /4.387Mi, 898.354Ki/s, 0s\n", " * cad/gre.eec.d.00.it.p.…rk_and_cable_paths.pdf: 15% /44.467Mi, 2.014Mi/s, 18\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf:  0% /150.848Mi, 0/s, -\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t   52.053 MiB / 232.689 MiB, 22%, 7.523 MiB/s, ETA 24s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            4 / 6, 67%\n", "Elapsed time:         8.4s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…rk_and_cable_paths.pdf: 24% /44.467Mi, 2.014Mi/s, 16\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf:  2% /150.848Mi, 0/s, -\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t   60.714 MiB / 232.689 MiB, 26%, 7.523 MiB/s, ETA 22s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            4 / 6, 67%\n", "Elapsed time:         8.9s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…rk_and_cable_paths.pdf: 35% /44.467Mi, 4.603Mi/s, 6s\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf:  4% /150.848Mi, 2.639Mi/s, 5\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t   67.022 MiB / 232.689 MiB, 29%, 8.783 MiB/s, ETA 18s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            4 / 6, 67%\n", "Elapsed time:         9.4s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…rk_and_cable_paths.pdf: 45% /44.467Mi, 4.603Mi/s, 5s\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf:  6% /150.848Mi, 2.639Mi/s, 5\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t   74.745 MiB / 232.689 MiB, 32%, 8.783 MiB/s, ETA 17s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            4 / 6, 67%\n", "Elapsed time:         9.9s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…rk_and_cable_paths.pdf: 53% /44.467Mi, 5.460Mi/s, 3s\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf:  8% /150.848Mi, 3.780Mi/s, 3\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t   82.116 MiB / 232.689 MiB, 35%, 9.439 MiB/s, ETA 15s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            4 / 6, 67%\n", "Elapsed time:        10.4s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…rk_and_cable_paths.pdf: 62% /44.467Mi, 5.460Mi/s, 3s\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 11% /150.848Mi, 3.780Mi/s, 3\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t   91.432 MiB / 232.689 MiB, 39%, 9.439 MiB/s, ETA 14s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            4 / 6, 67%\n", "Elapsed time:        10.9s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…rk_and_cable_paths.pdf: 71% /44.467Mi, 5.880Mi/s, 2s\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 14% /150.848Mi, 4.905Mi/s, 2\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t   98.960 MiB / 232.689 MiB, 43%, 10.244 MiB/s, ETA 13s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            4 / 6, 67%\n", "Elapsed time:        11.4s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…rk_and_cable_paths.pdf: 80% /44.467Mi, 5.880Mi/s, 1s\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 17% /150.848Mi, 4.905Mi/s, 2\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  108.089 MiB / 232.689 MiB, 46%, 10.244 MiB/s, ETA 12s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            4 / 6, 67%\n", "Elapsed time:        11.9s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…rk_and_cable_paths.pdf: 89% /44.467Mi, 6.369Mi/s, 0s\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 20% /150.848Mi, 5.699Mi/s, 2\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  115.711 MiB / 232.689 MiB, 50%, 10.892 MiB/s, ETA 10s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            4 / 6, 67%\n", "Elapsed time:        12.4s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…rk_and_cable_paths.pdf:100% /44.467Mi, 6.369Mi/s, 0s\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 22% /150.848Mi, 5.699Mi/s, 2\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  121.840 MiB / 232.689 MiB, 52%, 10.892 MiB/s, ETA 10s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            4 / 6, 67%\n", "Elapsed time:        12.9s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…rk_and_cable_paths.pdf:100% /44.467Mi, 6.352Mi/s, 0s\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 26% /150.848Mi, 6.187Mi/s, 1\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  128.930 MiB / 232.689 MiB, 55%, 11.146 MiB/s, ETA 9s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            4 / 6, 67%\n", "Elapsed time:        13.4s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…rk_and_cable_paths.pdf:100% /44.467Mi, 6.352Mi/s, 0s\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 31% /150.848Mi, 6.187Mi/s, 1\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  134.118 MiB / 232.689 MiB, 58%, 11.146 MiB/s, ETA 8s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            4 / 6, 67%\n", "Elapsed time:        13.9s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…rk_and_cable_paths.pdf:100% /44.467Mi, 5.558Mi/s, 0s\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 34% /150.848Mi, 6.857Mi/s, 1\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  137.840 MiB / 232.689 MiB, 59%, 11.240 MiB/s, ETA 8s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            5 / 6, 83%\n", "Elapsed time:        14.4s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 37% /150.848Mi, 6.857Mi/s, 1\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  143.618 MiB / 232.689 MiB, 62%, 11.240 MiB/s, ETA 7s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            5 / 6, 83%\n", "Elapsed time:        14.9s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 40% /150.848Mi, 7.094Mi/s, 1\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  147.493 MiB / 232.689 MiB, 63%, 11.109 MiB/s, ETA 7s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            5 / 6, 83%\n", "Elapsed time:        15.4s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 43% /150.848Mi, 7.094Mi/s, 1\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  153.840 MiB / 232.689 MiB, 66%, 11.109 MiB/s, ETA 7s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            5 / 6, 83%\n", "Elapsed time:        15.9s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 47% /150.848Mi, 7.819Mi/s, 1\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  160.274 MiB / 232.689 MiB, 69%, 11.043 MiB/s, ETA 6s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            5 / 6, 83%\n", "Elapsed time:        16.4s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 51% /150.848Mi, 7.819Mi/s, 9\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  162.309 MiB / 232.689 MiB, 70%, 11.043 MiB/s, ETA 6s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            5 / 6, 83%\n", "Elapsed time:        16.9s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 53% /150.848Mi, 8.000Mi/s, 8\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  169.840 MiB / 232.689 MiB, 73%, 10.872 MiB/s, ETA 5s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            5 / 6, 83%\n", "Elapsed time:        17.4s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 58% /150.848Mi, 8.000Mi/s, 7\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  176.430 MiB / 232.689 MiB, 76%, 10.872 MiB/s, ETA 5s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            5 / 6, 83%\n", "Elapsed time:        17.9s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 62% /150.848Mi, 8.164Mi/s, 6\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  181.149 MiB / 232.689 MiB, 78%, 11.081 MiB/s, ETA 4s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            5 / 6, 83%\n", "Elapsed time:        18.4s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 65% /150.848Mi, 8.164Mi/s, 6\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  185.840 MiB / 232.689 MiB, 80%, 11.081 MiB/s, ETA 4s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            5 / 6, 83%\n", "Elapsed time:        18.9s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 68% /150.848Mi, 8.667Mi/s, 5\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  191.524 MiB / 232.689 MiB, 82%, 10.970 MiB/s, ETA 3s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            5 / 6, 83%\n", "Elapsed time:        19.4s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 72% /150.848Mi, 8.667Mi/s, 4\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  195.493 MiB / 232.689 MiB, 84%, 10.970 MiB/s, ETA 3s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            5 / 6, 83%\n", "Elapsed time:        19.9s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 75% /150.848Mi, 8.615Mi/s, 4\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  201.840 MiB / 232.689 MiB, 87%, 10.888 MiB/s, ETA 2s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            5 / 6, 83%\n", "Elapsed time:        20.4s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 79% /150.848Mi, 8.615Mi/s, 3\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  209.555 MiB / 232.689 MiB, 90%, 10.888 MiB/s, ETA 2s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            5 / 6, 83%\n", "Elapsed time:        20.9s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 84% /150.848Mi, 8.814Mi/s, 2\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  210.653 MiB / 232.689 MiB, 91%, 11.090 MiB/s, ETA 1s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            5 / 6, 83%\n", "Elapsed time:        21.4s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 85% /150.848Mi, 8.814Mi/s, 2\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  217.840 MiB / 232.689 MiB, 94%, 11.090 MiB/s, ETA 1s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            5 / 6, 83%\n", "Elapsed time:        21.9s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 90% /150.848Mi, 8.925Mi/s, 1\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  225.840 MiB / 232.689 MiB, 97%, 10.911 MiB/s, ETA 0s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            5 / 6, 83%\n", "Elapsed time:        22.4s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 95% /150.848Mi, 8.925Mi/s, 0\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  229.805 MiB / 232.689 MiB, 99%, 10.911 MiB/s, ETA 0s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            5 / 6, 83%\n", "Elapsed time:        22.9s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf: 98% /150.848Mi, 9Mi/s, 0s\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[1GTransferred:   \t  232.689 MiB / 232.689 MiB, 100%, 10.981 MiB/s, ETA 0s\n", "Checks:                 3 / 3, 100%\n", "Transferred:            5 / 6, 83%\n", "Elapsed time:        23.4s\n", "Transferring:\n", " * cad/gre.eec.d.00.it.p.…wing_of_earthworks.pdf:100% /150.848Mi, 9Mi/s, 0s"]}, {"ename": "OSError", "evalue": "[Errno 5] Input/output error", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/utils/_process_posix.py:130\u001b[39m, in \u001b[36mProcessHandler.system\u001b[39m\u001b[34m(self, cmd)\u001b[39m\n\u001b[32m    127\u001b[39m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m    128\u001b[39m     \u001b[38;5;66;03m# res is the index of the pattern that caused the match, so we\u001b[39;00m\n\u001b[32m    129\u001b[39m     \u001b[38;5;66;03m# know whether we've finished (if we matched EOF) or not\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m130\u001b[39m     res_idx = \u001b[43mchild\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexpect_list\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpatterns\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mread_timeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    131\u001b[39m     \u001b[38;5;28mprint\u001b[39m(child.before[out_size:].decode(enc, \u001b[33m'\u001b[39m\u001b[33mreplace\u001b[39m\u001b[33m'\u001b[39m), end=\u001b[33m'\u001b[39m\u001b[33m'\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/pexpect/spawnbase.py:383\u001b[39m, in \u001b[36mSpawnBase.expect_list\u001b[39m\u001b[34m(self, pattern_list, timeout, searchwindowsize, async_, **kw)\u001b[39m\n\u001b[32m    382\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m383\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mexp\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexpect_loop\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/pexpect/expect.py:169\u001b[39m, in \u001b[36mExpecter.expect_loop\u001b[39m\u001b[34m(self, timeout)\u001b[39m\n\u001b[32m    168\u001b[39m \u001b[38;5;66;03m# Still have time left, so read more data\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m169\u001b[39m incoming = \u001b[43mspawn\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread_nonblocking\u001b[49m\u001b[43m(\u001b[49m\u001b[43mspawn\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmaxread\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    170\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.spawn.delayafterread \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/pexpect/pty_spawn.py:500\u001b[39m, in \u001b[36mspawn.read_nonblocking\u001b[39m\u001b[34m(self, size, timeout)\u001b[39m\n\u001b[32m    497\u001b[39m \u001b[38;5;66;03m# Because of the select(0) check above, we know that no data\u001b[39;00m\n\u001b[32m    498\u001b[39m \u001b[38;5;66;03m# is available right now. But if a non-zero timeout is given\u001b[39;00m\n\u001b[32m    499\u001b[39m \u001b[38;5;66;03m# (possibly timeout=None), we call select() with a timeout.\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m500\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m (timeout != \u001b[32m0\u001b[39m) \u001b[38;5;129;01mand\u001b[39;00m \u001b[43mselect\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[32m    501\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28msuper\u001b[39m(spawn, \u001b[38;5;28mself\u001b[39m).read_nonblocking(size)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/pexpect/pty_spawn.py:450\u001b[39m, in \u001b[36mspawn.read_nonblocking.<locals>.select\u001b[39m\u001b[34m(timeout)\u001b[39m\n\u001b[32m    449\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mselect\u001b[39m(timeout):\n\u001b[32m--> \u001b[39m\u001b[32m450\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mselect_ignore_interrupts\u001b[49m\u001b[43m(\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mchild_fd\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m[\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m[\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m[\u001b[32m0\u001b[39m]\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/pexpect/utils.py:143\u001b[39m, in \u001b[36mselect_ignore_interrupts\u001b[39m\u001b[34m(iwtd, owtd, ewtd, timeout)\u001b[39m\n\u001b[32m    142\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m143\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m select.select(iwtd, owtd, ewtd, timeout)\n\u001b[32m    144\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mInterruptedError\u001b[39;00m:\n", "\u001b[31mKeyboardInterrupt\u001b[39m: ", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON>\u001b[39m                                   <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# Sync all project folders and subfolders to Drive\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;66;03m#! rclone sync \"../../data/raw/\" \"gdrive:asbuilt-foundation/\" --progress\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m \u001b[43mget_ipython\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43msystem\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43m rclone sync \u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m../../data/raw/piani_di_giorgio\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m \u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mgdrive:asbuilt-foundation/piani_di_giorgio\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m --progress\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/zmqshell.py:657\u001b[39m, in \u001b[36mZMQInteractiveShell.system_piped\u001b[39m\u001b[34m(self, cmd)\u001b[39m\n\u001b[32m    655\u001b[39m         \u001b[38;5;28mself\u001b[39m.user_ns[\u001b[33m\"\u001b[39m\u001b[33m_exit_code\u001b[39m\u001b[33m\"\u001b[39m] = system(cmd)\n\u001b[32m    656\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m657\u001b[39m     \u001b[38;5;28mself\u001b[39m.user_ns[\u001b[33m\"\u001b[39m\u001b[33m_exit_code\u001b[39m\u001b[33m\"\u001b[39m] = \u001b[43msystem\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mvar_expand\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcmd\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdepth\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m1\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/utils/_process_posix.py:141\u001b[39m, in \u001b[36mProcessHandler.system\u001b[39m\u001b[34m(self, cmd)\u001b[39m\n\u001b[32m    136\u001b[39m         out_size = \u001b[38;5;28mlen\u001b[39m(child.before)\n\u001b[32m    137\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mKeyboardInterrupt\u001b[39;00m:\n\u001b[32m    138\u001b[39m     \u001b[38;5;66;03m# We need to send ^C to the process.  The ascii code for '^C' is 3\u001b[39;00m\n\u001b[32m    139\u001b[39m     \u001b[38;5;66;03m# (the character is known as ETX for 'End of Text', see\u001b[39;00m\n\u001b[32m    140\u001b[39m     \u001b[38;5;66;03m# curses.ascii.ETX).\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m141\u001b[39m     \u001b[43mchild\u001b[49m\u001b[43m.\u001b[49m\u001b[43msendline\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mchr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[32;43m3\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    142\u001b[39m     \u001b[38;5;66;03m# Read and print any more output the program might produce on its\u001b[39;00m\n\u001b[32m    143\u001b[39m     \u001b[38;5;66;03m# way out.\u001b[39;00m\n\u001b[32m    144\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/pexpect/pty_spawn.py:578\u001b[39m, in \u001b[36mspawn.sendline\u001b[39m\u001b[34m(self, s)\u001b[39m\n\u001b[32m    572\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m'''Wraps send(), sending string ``s`` to child process, with\u001b[39;00m\n\u001b[32m    573\u001b[39m \u001b[33;03m``os.linesep`` automatically appended. Returns number of bytes\u001b[39;00m\n\u001b[32m    574\u001b[39m \u001b[33;03mwritten.  Only a limited number of bytes may be sent for each\u001b[39;00m\n\u001b[32m    575\u001b[39m \u001b[33;03mline in the default terminal mode, see docstring of :meth:`send`.\u001b[39;00m\n\u001b[32m    576\u001b[39m \u001b[33;03m'''\u001b[39;00m\n\u001b[32m    577\u001b[39m s = \u001b[38;5;28mself\u001b[39m._coerce_send_string(s)\n\u001b[32m--> \u001b[39m\u001b[32m578\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43ms\u001b[49m\u001b[43m \u001b[49m\u001b[43m+\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mlinesep\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/pexpect/pty_spawn.py:569\u001b[39m, in \u001b[36mspawn.send\u001b[39m\u001b[34m(self, s)\u001b[39m\n\u001b[32m    566\u001b[39m \u001b[38;5;28mself\u001b[39m._log(s, \u001b[33m'\u001b[39m\u001b[33msend\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m    568\u001b[39m b = \u001b[38;5;28mself\u001b[39m._encoder.encode(s, final=\u001b[38;5;28;01mFalse\u001b[39;00m)\n\u001b[32m--> \u001b[39m\u001b[32m569\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mos\u001b[49m\u001b[43m.\u001b[49m\u001b[43mwrite\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mchild_fd\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mb\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mOSError\u001b[39m: [Errno 5] Input/output error"]}], "source": ["# Sync all project folders and subfolders to Drive\n", "#! rclone sync \"../../data/raw/\" \"gdrive:asbuilt-foundation/\" --progress\n", "! rclone sync \"../../data/raw/piani_di_giorgio\" \"gdrive:asbuilt-foundation/piani_di_giorgio\" --progress\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sync everything back from Drive to local\n", "!rclone sync \"gdrive:asbuilt-foundation/\" \"../../data/raw/\" --progress"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}