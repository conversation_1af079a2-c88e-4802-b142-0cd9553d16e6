# Papermill parameters
project_type = "motali_de_castro"
site_name = "main_site"
cad_data_path = "../../../data/raw/motali_de_castro/cad"
output_dir = "../../../output_runs/cad_metadata"
target_files = ["GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf", "GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg"]
coordinate_system = "EPSG:32633"  # UTM Zone 33N for Italy
classification_rules_file = None  # Optional custom classification rules

import os
import sys
import logging
import json
import csv
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict

import pandas as pd
import numpy as np
import ezdxf
from ezdxf import recover
import mlflow

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

logger.info("Enhanced CAD Metadata Extraction - Starting...")
logger.info(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
logger.info(f"Project: {project_type}/{site_name}")

# Setup paths and directories
cad_path = Path(cad_data_path)
output_path = Path(output_dir)
output_path.mkdir(parents=True, exist_ok=True)

# Create timestamped output directory
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
run_output_dir = output_path / f"{project_type}_{site_name}_{timestamp}"
run_output_dir.mkdir(parents=True, exist_ok=True)

logger.info(f"CAD data path: {cad_path.resolve()}")
logger.info(f"Output directory: {run_output_dir.resolve()}")

# Verify CAD directory exists
if not cad_path.exists():
    raise FileNotFoundError(f"CAD directory not found: {cad_path}")

logger.info(f"Setup completed - Output directory: {run_output_dir}")

class CADEntityClassifier:
    """Enhanced classification system for CAD entities."""
    
    def __init__(self):
        self.classification_rules = {
            'pile': {
                'layer_keywords': ['pile', 'palo', 'foundation', 'fondazione'],
                'block_keywords': ['pile', 'palo', 'p_', 'foundation'],
                'text_keywords': ['pile', 'palo', 'p-', 'foundation'],
                'entity_types': ['CIRCLE', 'INSERT', 'POINT']
            },
            'panel': {
                'layer_keywords': ['panel', 'pv', 'solar', 'modulo', 'pannello'],
                'block_keywords': ['panel', 'pv', 'solar', 'modulo'],
                'text_keywords': ['panel', 'pv', 'solar'],
                'entity_types': ['INSERT', 'LWPOLYLINE', 'POLYLINE']
            },
            'road': {
                'layer_keywords': ['road', 'strada', 'strade', 'access', 'accesso'],
                'block_keywords': ['road', 'strada'],
                'text_keywords': ['road', 'strada'],
                'entity_types': ['LWPOLYLINE', 'POLYLINE', 'LINE', 'ARC']
            },
            'trench': {
                'layer_keywords': ['trench', 'trincea', 'cable', 'cavo', 'cavidotto'],
                'block_keywords': ['trench', 'cable'],
                'text_keywords': ['trench', 'cable', 'cavo'],
                'entity_types': ['LWPOLYLINE', 'POLYLINE', 'LINE']
            },
            'foundation': {
                'layer_keywords': ['foundation', 'fondazione', 'base', 'cabin', 'cabina'],
                'block_keywords': ['foundation', 'cabin', 'base'],
                'text_keywords': ['foundation', 'cabin', 'base'],
                'entity_types': ['LWPOLYLINE', 'POLYLINE', 'INSERT']
            },
            'electrical': {
                'layer_keywords': ['electrical', 'elettrico', 'power', 'energia'],
                'block_keywords': ['electrical', 'power'],
                'text_keywords': ['electrical', 'power', 'kw', 'v'],
                'entity_types': ['LINE', 'LWPOLYLINE', 'INSERT']
            },
            'annotation': {
                'layer_keywords': ['text', 'label', 'annotation', 'quota', 'dimension'],
                'block_keywords': ['text', 'label'],
                'text_keywords': [],
                'entity_types': ['TEXT', 'MTEXT', 'DIMENSION']
            }
        }
    
    def classify_entity(self, entity, layer_name: str = "", block_name: str = "", text_content: str = "") -> str:
        """Classify CAD entity based on multiple criteria."""
        entity_type = entity.dxftype()
        layer_name = layer_name.lower()
        block_name = block_name.lower()
        text_content = text_content.lower()
        
        scores = defaultdict(int)
        
        for category, rules in self.classification_rules.items():
            # Check entity type match
            if entity_type in rules['entity_types']:
                scores[category] += 2
            
            # Check layer keywords
            for keyword in rules['layer_keywords']:
                if keyword in layer_name:
                    scores[category] += 3
            
            # Check block keywords
            for keyword in rules['block_keywords']:
                if keyword in block_name:
                    scores[category] += 3
            
            # Check text keywords
            for keyword in rules['text_keywords']:
                if keyword in text_content:
                    scores[category] += 2
        
        # Return highest scoring category or 'unknown'
        if scores:
            return max(scores.items(), key=lambda x: x[1])[0]
        return 'unknown'

def extract_comprehensive_entity_data(entity, classifier: CADEntityClassifier) -> Dict[str, Any]:
    """Extract comprehensive metadata from CAD entity."""
    try:
        entity_type = entity.dxftype()
        handle = getattr(entity.dxf, 'handle', 'unknown')
        layer_name = getattr(entity.dxf, 'layer', '')
        
        # Base entity data
        entity_data = {
            'entity_id': handle,
            'entity_type': entity_type,
            'layer_name': layer_name,
            'color': getattr(entity.dxf, 'color', None),
            'linetype': getattr(entity.dxf, 'linetype', None),
            'lineweight': getattr(entity.dxf, 'lineweight', None)
        }
        
        # Extract geometry and coordinates based on entity type
        if entity_type == 'CIRCLE':
            center = entity.dxf.center
            entity_data.update({
                'x_coord': center[0],
                'y_coord': center[1],
                'z_coord': center[2] if len(center) > 2 else 0.0,
                'radius': entity.dxf.radius,
                'geometry_type': 'circle'
            })
            
        elif entity_type == 'LINE':
            start = entity.dxf.start
            end = entity.dxf.end
            entity_data.update({
                'x_coord': (start[0] + end[0]) / 2,  # Midpoint
                'y_coord': (start[1] + end[1]) / 2,
                'z_coord': (start[2] + end[2]) / 2 if len(start) > 2 else 0.0,
                'start_x': start[0],
                'start_y': start[1],
                'start_z': start[2] if len(start) > 2 else 0.0,
                'end_x': end[0],
                'end_y': end[1],
                'end_z': end[2] if len(end) > 2 else 0.0,
                'length': np.linalg.norm(np.array(end) - np.array(start)),
                'geometry_type': 'line'
            })
            
        elif entity_type == 'INSERT':
            insert_point = entity.dxf.insert
            entity_data.update({
                'x_coord': insert_point[0],
                'y_coord': insert_point[1],
                'z_coord': insert_point[2] if len(insert_point) > 2 else 0.0,
                'block_name': entity.dxf.name,
                'rotation': getattr(entity.dxf, 'rotation', 0.0),
                'scale_x': getattr(entity.dxf, 'xscale', 1.0),
                'scale_y': getattr(entity.dxf, 'yscale', 1.0),
                'scale_z': getattr(entity.dxf, 'zscale', 1.0),
                'geometry_type': 'insert'
            })
            
        elif entity_type in ['TEXT', 'MTEXT']:
            insert_point = entity.dxf.insert
            text_content = entity.dxf.text if entity_type == 'TEXT' else entity.text
            entity_data.update({
                'x_coord': insert_point[0],
                'y_coord': insert_point[1],
                'z_coord': insert_point[2] if len(insert_point) > 2 else 0.0,
                'text_content': text_content,
                'text_height': getattr(entity.dxf, 'height', None),
                'text_rotation': getattr(entity.dxf, 'rotation', 0.0),
                'geometry_type': 'text'
            })
            
        elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
            # Get polyline points
            if hasattr(entity, 'get_points'):
                points = list(entity.get_points())
            else:
                points = []
            
            if points:
                # Calculate centroid
                points_array = np.array(points)
                centroid = np.mean(points_array, axis=0)
                entity_data.update({
                    'x_coord': centroid[0],
                    'y_coord': centroid[1],
                    'z_coord': centroid[2] if len(centroid) > 2 else 0.0,
                    'point_count': len(points),
                    'is_closed': getattr(entity.dxf, 'flags', 0) & 1,
                    'geometry_type': 'polyline'
                })
            else:
                entity_data.update({
                    'x_coord': 0.0,
                    'y_coord': 0.0,
                    'z_coord': 0.0,
                    'geometry_type': 'polyline'
                })
        
        else:
            # Default handling for other entity types
            entity_data.update({
                'x_coord': 0.0,
                'y_coord': 0.0,
                'z_coord': 0.0,
                'geometry_type': 'other'
            })
        
        # Classify entity
        block_name = entity_data.get('block_name', '')
        text_content = entity_data.get('text_content', '')
        entity_data['classification'] = classifier.classify_entity(
            entity, layer_name, block_name, text_content
        )
        
        # Add extraction metadata
        entity_data['extraction_timestamp'] = datetime.now().isoformat()
        
        return entity_data
        
    except Exception as e:
        logger.warning(f"Error extracting entity data: {e}")
        return {
            'entity_id': 'error',
            'entity_type': getattr(entity, 'dxftype', lambda: 'unknown')(),
            'error': str(e),
            'extraction_timestamp': datetime.now().isoformat()
        }

def process_dxf_file(file_path: Path, classifier: CADEntityClassifier) -> Tuple[List[Dict], Dict]:
    """Process a single DXF file and extract all entity metadata."""
    logger.info(f"Processing DXF file: {file_path.name}")
    
    entities_data = []
    file_stats = {
        'file_name': file_path.name,
        'file_path': str(file_path),
        'file_size_mb': file_path.stat().st_size / (1024 * 1024),
        'processing_timestamp': datetime.now().isoformat(),
        'entity_counts': defaultdict(int),
        'classification_counts': defaultdict(int),
        'layer_counts': defaultdict(int),
        'coordinate_bounds': {'min_x': float('inf'), 'max_x': float('-inf'),
                             'min_y': float('inf'), 'max_y': float('-inf')},
        'errors': []
    }
    
    try:
        # Try to read the file, use recovery if needed
        try:
            doc = ezdxf.readfile(file_path)
        except ezdxf.DXFStructureError:
            logger.warning(f"DXF structure error, attempting recovery: {file_path.name}")
            doc, auditor = recover.readfile(file_path)
            if auditor.has_errors:
                file_stats['errors'].extend([str(error) for error in auditor.errors])
        
        # Process modelspace entities
        msp = doc.modelspace()
        logger.info(f"Processing {len(msp)} entities in modelspace")
        
        for entity in msp:
            entity_data = extract_comprehensive_entity_data(entity, classifier)
            entity_data['source_file'] = file_path.name
            entity_data['source_space'] = 'modelspace'
            entities_data.append(entity_data)
            
            # Update statistics
            file_stats['entity_counts'][entity_data.get('entity_type', 'unknown')] += 1
            file_stats['classification_counts'][entity_data.get('classification', 'unknown')] += 1
            file_stats['layer_counts'][entity_data.get('layer_name', 'unknown')] += 1
            
            # Update coordinate bounds
            x_coord = entity_data.get('x_coord')
            y_coord = entity_data.get('y_coord')
            if x_coord is not None and y_coord is not None:
                file_stats['coordinate_bounds']['min_x'] = min(file_stats['coordinate_bounds']['min_x'], x_coord)
                file_stats['coordinate_bounds']['max_x'] = max(file_stats['coordinate_bounds']['max_x'], x_coord)
                file_stats['coordinate_bounds']['min_y'] = min(file_stats['coordinate_bounds']['min_y'], y_coord)
                file_stats['coordinate_bounds']['max_y'] = max(file_stats['coordinate_bounds']['max_y'], y_coord)
        
        # Process block definitions
        logger.info(f"Processing {len(doc.blocks)} block definitions")
        for block in doc.blocks:
            if block.name.startswith('*'):  # Skip anonymous blocks
                continue
                
            for entity in block:
                entity_data = extract_comprehensive_entity_data(entity, classifier)
                entity_data['source_file'] = file_path.name
                entity_data['source_space'] = f'block:{block.name}'
                entities_data.append(entity_data)
                
                # Update statistics
                file_stats['entity_counts'][entity_data.get('entity_type', 'unknown')] += 1
                file_stats['classification_counts'][entity_data.get('classification', 'unknown')] += 1
        
        file_stats['total_entities'] = len(entities_data)
        file_stats['processing_status'] = 'success'
        
        logger.info(f"Successfully processed {file_path.name}: {len(entities_data)} entities")
        
    except Exception as e:
        error_msg = f"Error processing {file_path.name}: {str(e)}"
        logger.error(error_msg)
        file_stats['errors'].append(error_msg)
        file_stats['processing_status'] = 'error'
    
    return entities_data, file_stats

# Initialize classifier
classifier = CADEntityClassifier()

# Initialize MLflow tracking
mlflow.set_experiment(f"cad_metadata_extraction_{project_type}")

with mlflow.start_run(run_name=f"{site_name}_cad_extraction_{timestamp}"):
    # Log parameters
    mlflow.log_param("project_type", project_type)
    mlflow.log_param("site_name", site_name)
    mlflow.log_param("coordinate_system", coordinate_system)
    mlflow.log_param("target_files", ",".join(target_files))
    
    # Discover CAD files
    print("\n=== File Discovery ===")
    discovered_files = []
    
    if target_files:
        # Process specific target files
        for target_file in target_files:
            file_path = cad_path / target_file
            if file_path.exists() and file_path.suffix.lower() in ['.dxf', '.dwg']:
                discovered_files.append(file_path)
                print(f"Found target file: {target_file}")
            else:
                print(f"Target file not found or invalid: {target_file}")
    else:
        # Discover all DXF files in directory
        for file_path in cad_path.rglob("*.dxf"):
            discovered_files.append(file_path)
            print(f"Discovered DXF file: {file_path.name}")
    
    print(f"\nTotal files to process: {len(discovered_files)}")
    mlflow.log_metric("files_discovered", len(discovered_files))
    
    # Process each file
    all_entities = []
    all_file_stats = []
    processing_summary = {
        'total_files': len(discovered_files),
        'successful_files': 0,
        'failed_files': 0,
        'total_entities': 0,
        'classification_summary': defaultdict(int),
        'entity_type_summary': defaultdict(int)
    }
    
    print("\n=== Processing Files ===")
    for file_path in discovered_files:
        if file_path.suffix.lower() == '.dxf':
            entities_data, file_stats = process_dxf_file(file_path, classifier)
            
            all_entities.extend(entities_data)
            all_file_stats.append(file_stats)
            
            if file_stats['processing_status'] == 'success':
                processing_summary['successful_files'] += 1
                processing_summary['total_entities'] += len(entities_data)
                
                # Update summaries
                for classification, count in file_stats['classification_counts'].items():
                    processing_summary['classification_summary'][classification] += count
                for entity_type, count in file_stats['entity_counts'].items():
                    processing_summary['entity_type_summary'][entity_type] += count
            else:
                processing_summary['failed_files'] += 1
        else:
            print(f"Skipping non-DXF file: {file_path.name} (DWG processing requires conversion)")
    
    print(f"\n=== Processing Summary ===")
    print(f"Total files processed: {processing_summary['total_files']}")
    print(f"Successful: {processing_summary['successful_files']}")
    print(f"Failed: {processing_summary['failed_files']}")
    print(f"Total entities extracted: {processing_summary['total_entities']}")
    
    # Log metrics to MLflow
    mlflow.log_metric("files_processed", processing_summary['total_files'])
    mlflow.log_metric("files_successful", processing_summary['successful_files'])
    mlflow.log_metric("files_failed", processing_summary['failed_files'])
    mlflow.log_metric("total_entities", processing_summary['total_entities'])

# Create comprehensive DataFrame
if all_entities:
        print("\n=== Generating Structured Output ===")
        
        # Convert to DataFrame
        entities_df = pd.DataFrame(all_entities)
        
        # Save main entities CSV
        entities_csv_path = run_output_dir / f"{project_type}_{site_name}_cad_entities.csv"
        entities_df.to_csv(entities_csv_path, index=False)
        print(f"Entities CSV saved: {entities_csv_path.name}")
        
        # Save classification-specific CSVs
        for classification in processing_summary['classification_summary'].keys():
            if classification != 'unknown':
                classified_df = entities_df[entities_df['classification'] == classification]
                if not classified_df.empty:
                    classified_csv_path = run_output_dir / f"{project_type}_{site_name}_cad_{classification}.csv"
                    classified_df.to_csv(classified_csv_path, index=False)
                    print(f"{classification.title()} entities CSV saved: {classified_csv_path.name} ({len(classified_df)} entities)")
        
        # Save file statistics
        file_stats_df = pd.DataFrame(all_file_stats)
        file_stats_csv_path = run_output_dir / f"{project_type}_{site_name}_cad_file_stats.csv"
        file_stats_df.to_csv(file_stats_csv_path, index=False)
        print(f"File statistics CSV saved: {file_stats_csv_path.name}")
        
        # Save processing summary as JSON
        summary_json_path = run_output_dir / f"{project_type}_{site_name}_cad_summary.json"
        with open(summary_json_path, 'w') as f:
            # Convert defaultdict to regular dict for JSON serialization
            summary_for_json = {
                'processing_summary': dict(processing_summary),
                'classification_summary': dict(processing_summary['classification_summary']),
                'entity_type_summary': dict(processing_summary['entity_type_summary']),
                'coordinate_system': coordinate_system,
                'extraction_timestamp': datetime.now().isoformat()
            }
            json.dump(summary_for_json, f, indent=2)
        print(f"Processing summary JSON saved: {summary_json_path.name}")
        
        # Log artifacts to MLflow
        mlflow.log_artifact(str(entities_csv_path))
        mlflow.log_artifact(str(file_stats_csv_path))
        mlflow.log_artifact(str(summary_json_path))
        
        # Log classification metrics
        for classification, count in processing_summary['classification_summary'].items():
            mlflow.log_metric(f"entities_{classification}", count)
    
    else:
        print("\nNo entities extracted - check file processing errors")
        mlflow.log_metric("extraction_success", 0)

# Display detailed analysis if entities were extracted
if all_entities:
    print("\n=== Detailed Analysis ===")
    
    # Classification breakdown
    print("\nEntity Classification Summary:")
    for classification, count in sorted(processing_summary['classification_summary'].items()):
        percentage = (count / processing_summary['total_entities']) * 100
        print(f"  {classification.title()}: {count} entities ({percentage:.1f}%)")
    
    # Entity type breakdown
    print("\nEntity Type Summary:")
    for entity_type, count in sorted(processing_summary['entity_type_summary'].items()):
        percentage = (count / processing_summary['total_entities']) * 100
        print(f"  {entity_type}: {count} entities ({percentage:.1f}%)")
    
    # Coordinate bounds analysis
    print("\nCoordinate Bounds Analysis:")
    entities_with_coords = entities_df.dropna(subset=['x_coord', 'y_coord'])
    if not entities_with_coords.empty:
        min_x = entities_with_coords['x_coord'].min()
        max_x = entities_with_coords['x_coord'].max()
        min_y = entities_with_coords['y_coord'].min()
        max_y = entities_with_coords['y_coord'].max()
        
        print(f"  X range: {min_x:.2f} to {max_x:.2f} (span: {max_x - min_x:.2f})")
        print(f"  Y range: {min_y:.2f} to {max_y:.2f} (span: {max_y - min_y:.2f})")
        print(f"  Coordinate system: {coordinate_system}")
        
        # Log coordinate bounds to MLflow
        mlflow.log_metric("coord_min_x", min_x)
        mlflow.log_metric("coord_max_x", max_x)
        mlflow.log_metric("coord_min_y", min_y)
        mlflow.log_metric("coord_max_y", max_y)
        mlflow.log_metric("coord_span_x", max_x - min_x)
        mlflow.log_metric("coord_span_y", max_y - min_y)
    
    # Layer analysis
    print("\nLayer Analysis:")
    layer_counts = entities_df['layer_name'].value_counts().head(10)
    for layer, count in layer_counts.items():
        print(f"  {layer}: {count} entities")
    
    # Quality metrics
    print("\nQuality Metrics:")
    entities_with_coords_count = len(entities_with_coords)
    coord_completeness = (entities_with_coords_count / len(entities_df)) * 100
    print(f"  Coordinate completeness: {coord_completeness:.1f}% ({entities_with_coords_count}/{len(entities_df)})")
    
    classified_entities = len(entities_df[entities_df['classification'] != 'unknown'])
    classification_rate = (classified_entities / len(entities_df)) * 100
    print(f"  Classification success rate: {classification_rate:.1f}% ({classified_entities}/{len(entities_df)})")
    
    # Log quality metrics
    mlflow.log_metric("coordinate_completeness_pct", coord_completeness)
    mlflow.log_metric("classification_success_pct", classification_rate)
    
    print("\n=== Extraction Results for Alignment and Labeling ===")
    
    # Pile entities for alignment
    pile_entities = entities_df[entities_df['classification'] == 'pile']
    if not pile_entities.empty:
        print(f"\nPile Entities for Alignment: {len(pile_entities)} found")
        print("Sample pile coordinates:")
        for idx, row in pile_entities.head(5).iterrows():
            print(f"  Pile {row['entity_id']}: ({row['x_coord']:.2f}, {row['y_coord']:.2f}, {row['z_coord']:.2f})")
    
    # Foundation entities
    foundation_entities = entities_df[entities_df['classification'] == 'foundation']
    if not foundation_entities.empty:
        print(f"\nFoundation Entities: {len(foundation_entities)} found")
    
    # Panel entities
    panel_entities = entities_df[entities_df['classification'] == 'panel']
    if not panel_entities.empty:
        print(f"\nPanel Entities: {len(panel_entities)} found")
    
    # Text annotations for labeling
    text_entities = entities_df[entities_df['geometry_type'] == 'text']
    if not text_entities.empty:
        print(f"\nText Annotations for Labeling: {len(text_entities)} found")
        print("Sample text annotations:")
        for idx, row in text_entities.head(3).iterrows():
            text_content = row.get('text_content', 'N/A')
            print(f"  '{text_content}' at ({row['x_coord']:.2f}, {row['y_coord']:.2f})")

print("\n=== CAD Metadata Extraction Complete ===")
print(f"Output directory: {run_output_dir}")
print(f"Total entities extracted: {processing_summary['total_entities']}")
print(f"Files processed successfully: {processing_summary['successful_files']}/{processing_summary['total_files']}")

if processing_summary['total_entities'] > 0:
    print("\nReady for downstream workflows:")
    print("- Point cloud alignment using extracted coordinates")
    print("- Automated labeling using classified entities")
    print("- Coordinate system validation and transformation")
else:
    print("\nNo entities extracted - review file processing errors and classification rules")

def convert_to_element_metadata(cad_entities_df: pd.DataFrame, source_file: str) -> List[Dict[str, Any]]:
    """Convert CAD entities to ElementMetadata schema format."""
    
    element_metadata_list = []
    
    for idx, row in cad_entities_df.iterrows():
        # Create base element metadata following the schema
        element_metadata = {
            'element_id': str(row.get('entity_id', f'cad_{idx}')),
            'element_name': row.get('block_name', row.get('text_content', f"{row.get('entity_type', 'unknown')}_{idx}")),
            'element_type': map_classification_to_element_type(row.get('classification', 'unknown')),
            'source_file': source_file,
            'source_type': 'CAD',
            
            # Coordinates
            'x_local': float(row.get('x_coord', 0.0)),
            'y_local': float(row.get('y_coord', 0.0)),
            'z_local': float(row.get('z_coord', 0.0)),
            
            # Transformation parameters
            'rotation': float(row.get('rotation', 0.0)) if pd.notna(row.get('rotation')) else None,
            'scale_x': float(row.get('scale_x', 1.0)) if pd.notna(row.get('scale_x')) else None,
            'scale_y': float(row.get('scale_y', 1.0)) if pd.notna(row.get('scale_y')) else None,
            'scale_z': float(row.get('scale_z', 1.0)) if pd.notna(row.get('scale_z')) else None,
            
            # CAD-specific attributes
            'layer_name': row.get('layer_name', ''),
            'entity_type': row.get('entity_type', ''),
            'geometry_type': row.get('geometry_type', ''),
            'classification': row.get('classification', 'unknown'),
            
            # Additional geometric properties
            'radius': float(row.get('radius')) if pd.notna(row.get('radius')) else None,
            'length': float(row.get('length')) if pd.notna(row.get('length')) else None,
            'point_count': int(row.get('point_count')) if pd.notna(row.get('point_count')) else None,
            'is_closed': bool(row.get('is_closed')) if pd.notna(row.get('is_closed')) else None,
            
            # Text content for annotations
            'text_content': row.get('text_content', '') if pd.notna(row.get('text_content')) else None,
            'text_height': float(row.get('text_height')) if pd.notna(row.get('text_height')) else None,
            
            # Metadata
            'extraction_timestamp': row.get('extraction_timestamp', datetime.now().isoformat()),
            'source_space': row.get('source_space', 'modelspace'),
            'coordinate_system': coordinate_system,
            'confidence_score': calculate_confidence_score(row),
            
            # Quality flags
            'has_coordinates': bool(pd.notna(row.get('x_coord')) and pd.notna(row.get('y_coord'))),
            'is_classified': row.get('classification', 'unknown') != 'unknown',
            'is_geometric': row.get('geometry_type', '') in ['circle', 'line', 'polyline', 'insert']
        }
        
        element_metadata_list.append(element_metadata)
    
    return element_metadata_list


def map_classification_to_element_type(classification: str) -> str:
    """Map CAD classification to standard element types."""
    mapping = {
        'pile': 'pile',
        'foundation': 'foundation',
        'panel': 'solar_panel',
        'building': 'building',
        'road': 'infrastructure',
        'trench': 'infrastructure',
        'electrical': 'electrical',
        'annotation': 'annotation',
        'unknown': 'other'
    }
    return mapping.get(classification.lower(), 'other')


def calculate_confidence_score(row: pd.Series) -> float:
    """Calculate confidence score based on data completeness and classification."""
    score = 0.0
    
    # Base score for having coordinates
    if pd.notna(row.get('x_coord')) and pd.notna(row.get('y_coord')):
        score += 0.3
    
    # Score for classification
    if row.get('classification', 'unknown') != 'unknown':
        score += 0.3
    
    # Score for having geometric properties
    if row.get('geometry_type', '') in ['circle', 'line', 'polyline', 'insert']:
        score += 0.2
    
    # Score for having layer information
    if row.get('layer_name', ''):
        score += 0.1
    
    # Score for having additional properties
    if pd.notna(row.get('radius')) or pd.notna(row.get('length')) or pd.notna(row.get('text_content')):
        score += 0.1
    
    return min(score, 1.0)

def create_alignment_ready_dataset(pile_entities: pd.DataFrame, foundation_entities: pd.DataFrame) -> Dict[str, Any]:
    """Create alignment-ready dataset with pile and foundation coordinates."""
    
    alignment_data = {
        'metadata': {
            'dataset_type': 'cad_alignment_points',
            'coordinate_system': coordinate_system,
            'creation_timestamp': datetime.now().isoformat(),
            'total_points': len(pile_entities) + len(foundation_entities)
        },
        'pile_points': [],
        'foundation_points': [],
        'coordinate_bounds': {
            'min_x': None,
            'max_x': None,
            'min_y': None,
            'max_y': None
        }
    }
    
    # Process pile entities
    for idx, row in pile_entities.iterrows():
        if pd.notna(row.get('x_coord')) and pd.notna(row.get('y_coord')):
            pile_point = {
                'id': str(row.get('entity_id', f'pile_{idx}')),
                'x': float(row['x_coord']),
                'y': float(row['y_coord']),
                'z': float(row.get('z_coord', 0.0)),
                'type': 'pile',
                'subtype': row.get('block_name', 'unknown'),
                'layer': row.get('layer_name', ''),
                'confidence': calculate_confidence_score(row)
            }
            alignment_data['pile_points'].append(pile_point)
    
    # Process foundation entities
    for idx, row in foundation_entities.iterrows():
        if pd.notna(row.get('x_coord')) and pd.notna(row.get('y_coord')):
            foundation_point = {
                'id': str(row.get('entity_id', f'foundation_{idx}')),
                'x': float(row['x_coord']),
                'y': float(row['y_coord']),
                'z': float(row.get('z_coord', 0.0)),
                'type': 'foundation',
                'subtype': row.get('block_name', 'unknown'),
                'layer': row.get('layer_name', ''),
                'confidence': calculate_confidence_score(row)
            }
            alignment_data['foundation_points'].append(foundation_point)
    
    # Calculate coordinate bounds
    all_points = alignment_data['pile_points'] + alignment_data['foundation_points']
    if all_points:
        x_coords = [p['x'] for p in all_points]
        y_coords = [p['y'] for p in all_points]
        alignment_data['coordinate_bounds'] = {
            'min_x': min(x_coords),
            'max_x': max(x_coords),
            'min_y': min(y_coords),
            'max_y': max(y_coords)
        }
    
    return alignment_data

# Generate schema-compliant outputs if entities were extracted
if all_entities:
    print("\n=== Generating Schema-Compliant Outputs ===")
    
    # Convert to element metadata schema
    source_file = entities_df['source_file'].iloc[0] if not entities_df.empty else 'unknown'
    element_metadata_list = convert_to_element_metadata(entities_df, source_file)
    
    # Save element metadata
    element_metadata_path = run_output_dir / f"{project_type}_{site_name}_element_metadata.json"
    with open(element_metadata_path, 'w') as f:
        json.dump(element_metadata_list, f, indent=2, default=str)
    print(f"Element metadata saved: {element_metadata_path.name} ({len(element_metadata_list)} records)")
    
    # Create alignment-ready dataset
    pile_entities = entities_df[entities_df['classification'] == 'pile']
    foundation_entities = entities_df[entities_df['classification'] == 'foundation']
    
    alignment_data = create_alignment_ready_dataset(pile_entities, foundation_entities)
    alignment_path = run_output_dir / f"{project_type}_{site_name}_alignment_points.json"
    logger.info(f"Alignment dataset saved: {alignment_path.name}")
    
    with open(alignment_path, 'w') as f:
        json.dump(alignment_data, f, indent=2, default=str)
    logger.info(f"Alignment dataset saved: {alignment_path.name}")
    logger.info(f"  - Pile points: {len(alignment_data['pile_points'])}")
    logger.info(f"  - Foundation points: {len(alignment_data['foundation_points'])}")
    
    # Create labeling dataset
    labeling_data = {
        'metadata': {
            'dataset_type': 'cad_labeling_data',
            'coordinate_system': coordinate_system,
            'creation_timestamp': datetime.now().isoformat(),
            'total_entities': len(entities_df)
        },
        'classified_entities': {},
        'text_annotations': [],
        'classification_summary': {}
    }
    
    # Group entities by classification
    for classification in entities_df['classification'].unique():
        if classification != 'unknown':
            classified_entities = entities_df[entities_df['classification'] == classification]
            
            entity_list = []
            for idx, row in classified_entities.iterrows():
                if pd.notna(row.get('x_coord')) and pd.notna(row.get('y_coord')):
                    entity = {
                        'id': str(row.get('entity_id', f'{classification}_{idx}')),
                        'x': float(row['x_coord']),
                        'y': float(row['y_coord']),
                        'z': float(row.get('z_coord', 0.0)),
                        'entity_type': row.get('entity_type', ''),
                        'layer': row.get('layer_name', ''),
                        'confidence': calculate_confidence_score(row)
                    }
                    entity_list.append(entity)
            
            labeling_data['classified_entities'][classification] = entity_list
            labeling_data['classification_summary'][classification] = len(entity_list)
    
    # Extract text annotations
    text_entities = entities_df[entities_df['geometry_type'] == 'text']
    for idx, row in text_entities.iterrows():
        if pd.notna(row.get('text_content')) and pd.notna(row.get('x_coord')):
            annotation = {
                'id': str(row.get('entity_id', f'text_{idx}')),
                'x': float(row['x_coord']),
                'y': float(row['y_coord']),
                'z': float(row.get('z_coord', 0.0)),
                'text': str(row['text_content']),
                'height': float(row.get('text_height', 0)) if pd.notna(row.get('text_height')) else None,
                'rotation': float(row.get('text_rotation', 0)) if pd.notna(row.get('text_rotation')) else None,
                'layer': row.get('layer_name', ''),
                'classification': row.get('classification', 'annotation')
            }
            labeling_data['text_annotations'].append(annotation)
    
    # Save labeling dataset
    labeling_path = run_output_dir / f"{project_type}_{site_name}_labeling_data.json"
    with open(labeling_path, 'w') as f:
        json.dump(labeling_data, f, indent=2, default=str)
    logger.info(f"Labeling dataset saved: {labeling_path.name}")
    logger.info(f"  - Text annotations: {len(labeling_data['text_annotations'])}")
    logger.info(f"  - Classified entity types: {len(labeling_data['classified_entities'])}")
    
    # Log schema outputs to MLflow
    mlflow.log_artifact(str(element_metadata_path))
    mlflow.log_artifact(str(alignment_path))
    mlflow.log_artifact(str(labeling_path))
    
    # Log schema metrics
    mlflow.log_metric("element_metadata_records", len(element_metadata_list))
    mlflow.log_metric("alignment_points_total", len(alignment_data['pile_points']) + len(alignment_data['foundation_points']))
    mlflow.log_metric("alignment_pile_points", len(alignment_data['pile_points']))
    mlflow.log_metric("alignment_foundation_points", len(alignment_data['foundation_points']))
    mlflow.log_metric("text_annotations", len(labeling_data['text_annotations']))

# Final summary
print("\n" + "="*60)
print("ENHANCED CAD METADATA EXTRACTION COMPLETE")
print("="*60)

if processing_summary['total_entities'] > 0:
    print(f"\n✅ Successfully processed {processing_summary['successful_files']}/{processing_summary['total_files']} files")
    print(f"✅ Extracted {processing_summary['total_entities']} entities with {(len(entities_df[entities_df['classification'] != 'unknown']) / len(entities_df) * 100):.1f}% classification rate")
    
    if 'alignment_data' in locals():
        total_alignment_points = len(alignment_data['pile_points']) + len(alignment_data['foundation_points'])
        print(f"✅ Generated {total_alignment_points} alignment points for point cloud registration")
    
    if 'labeling_data' in locals():
        print(f"✅ Generated {len(labeling_data['text_annotations'])} text annotations for labeling")
    
    print(f"\n📁 Output directory: {run_output_dir}")
    print(f"📊 Coordinate system: {coordinate_system}")
    print(f"🕒 Processing timestamp: {timestamp}")
    
    print("\n🎯 Next Steps:")
    print("1. Use alignment points for point cloud to CAD registration")
    print("2. Apply classified entities for automated point cloud labeling")
    print("3. Validate coordinate system consistency with survey data")
    print("4. Integrate with existing preprocessing pipeline")
    
else:
    print("\n❌ No entities extracted - check input files and processing errors")
    print("Review file statistics and error logs for troubleshooting")

print("\n" + "="*60)

!ls ../../../output_runs/cad_metadata/motali_de_castro_main_site_20250630_154333