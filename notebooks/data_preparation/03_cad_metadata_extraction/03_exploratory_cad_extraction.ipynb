# Papermill parameters
project_type = "motali_de_castro"
site_name = "main_site"
cad_data_path = "../../../data/raw/motali_de_castro/cad"
output_dir = "../../../output_runs/cad_metadata"
target_files = ["GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf", "GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg"]
coordinate_system = "EPSG:32633"  # UTM Zone 33N for Italy
classification_rules_file = None  # Optional custom classification rules

import os
import sys
import logging
import json
import csv
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict


import pandas as pd
import numpy as np
import ezdxf
from ezdxf import recover
import mlflow

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

print("Enhanced CAD Metadata Extraction - Starting...")
print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print(f"Project: {project_type}/{site_name}")

# Setup paths and directories
cad_path = Path(cad_data_path)
output_path = Path(output_dir)
output_path.mkdir(parents=True, exist_ok=True)

# Create timestamped output directory
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
run_output_dir = output_path / f"{project_type}_{site_name}_{timestamp}"
run_output_dir.mkdir(parents=True, exist_ok=True)

print(f"CAD data path: {cad_path.resolve()}")
print(f"Output directory: {run_output_dir.resolve()}")

# Verify CAD directory exists
if not cad_path.exists():
    raise FileNotFoundError(f"CAD directory not found: {cad_path}")

print(f"Setup completed - Output directory: {run_output_dir}")

# Define classification rules for CAD entities
# Each category has keywords for layers, blocks, text, and preferred entity types

classification_rules = {
    'pile': {
        'layer_keywords': ['pile', 'palo', 'pali'],
        'block_keywords': ['pile', 'palo', 'p_'],
        'text_keywords': ['pile', 'palo', 'p-'],
        'entity_types': ['CIRCLE', 'INSERT', 'POINT'],
        'priority': 3  # High priority for alignment
    },
    'panel': {
        'layer_keywords': ['panel', 'pannello', 'pv', 'solar', 'solare', 'modulo', 'moduli'],
        'block_keywords': ['panel', 'pannello', 'pv', 'solar', 'modulo'],
        'text_keywords': ['panel', 'pv', 'solar', 'modulo'],
        'entity_types': ['INSERT', 'LWPOLYLINE', 'POLYLINE'],
        'priority': 2
    },
    'road': {
        'layer_keywords': ['road', 'strada', 'strade', 'access', 'accesso', 'viabilita'],
        'block_keywords': ['road', 'strada', 'access'],
        'text_keywords': ['road', 'strada', 'access'],
        'entity_types': ['LWPOLYLINE', 'POLYLINE', 'LINE', 'ARC'],
        'priority': 2
    },
    'trench': {
        'layer_keywords': ['trench', 'trincea', 'cable', 'cavo', 'cavidotto', 'cavidotti'],
        'block_keywords': ['trench', 'cable', 'cavo', 'cavidotto'],
        'text_keywords': ['trench', 'cable', 'cavo', 'cavidotto'],
        'entity_types': ['LWPOLYLINE', 'POLYLINE', 'LINE'],
        'priority': 2
    },
    'foundation': {
        'layer_keywords': ['foundation', 'fondazione', 'base', 'cabin', 'cabina', 'cabine'],
        'block_keywords': ['foundation', 'cabin', 'cabina', 'base'],
        'text_keywords': ['foundation', 'cabin', 'cabina', 'base'],
        'entity_types': ['LWPOLYLINE', 'POLYLINE', 'INSERT'],
        'priority': 3  # High priority for alignment
    },
    'electrical': {
        'layer_keywords': ['electrical', 'elettrico', 'elettrica', 'power', 'energia', 'electric'],
        'block_keywords': ['electrical', 'elettrico', 'power', 'electric'],
        'text_keywords': ['electrical', 'power', 'kw', 'v', 'elettrico'],
        'entity_types': ['LINE', 'LWPOLYLINE', 'INSERT'],
        'priority': 1
    },
    'building': {
        'layer_keywords': ['building', 'edificio', 'fabbricato', 'manufatto', 'costruzione'],
        'block_keywords': ['building', 'edificio', 'fabbricato'],
        'text_keywords': ['building', 'edificio'],
        'entity_types': ['LWPOLYLINE', 'POLYLINE', 'INSERT', 'HATCH'],
        'priority': 2
    },
    'annotation': {
        'layer_keywords': ['text', 'label', 'annotation', 'quota', 'dimension', 'dim'],
        'block_keywords': ['text', 'label', 'annotation'],
        'text_keywords': [],
        'entity_types': ['TEXT', 'MTEXT', 'DIMENSION'],
        'priority': 1
    }
}

print(f"Defined classification rules for {len(classification_rules)} categories:")
for category, rules in classification_rules.items():
    print(f"  {category}: {len(rules['layer_keywords'])} layer keywords, priority {rules['priority']}")

# Define entity classification function with priority scoring
def classify_entity(entity, layer_name="", block_name="", text_content=""):
    """Classify CAD entity based on multiple criteria with priority scoring."""
    entity_type = entity.dxftype()
    layer_name = layer_name.lower()
    block_name = block_name.lower()
    text_content = text_content.lower()
    
    scores = defaultdict(int)
    
    for category, rules in classification_rules.items():
        base_score = rules.get('priority', 1)
        
        # Check entity type match
        if entity_type in rules['entity_types']:
            scores[category] += base_score * 2
        
        # Check layer keywords with higher weight
        for keyword in rules['layer_keywords']:
            if keyword in layer_name:
                scores[category] += base_score * 4
        
        # Check block keywords
        for keyword in rules['block_keywords']:
            if keyword in block_name:
                scores[category] += base_score * 3
        
        # Check text keywords
        for keyword in rules['text_keywords']:
            if keyword in text_content:
                scores[category] += base_score * 2
    
    # Return highest scoring category or 'unknown'
    if scores:
        return max(scores.items(), key=lambda x: x[1])[0]
    return 'unknown'

print("Entity classification function defined")
print("Supports multi-criteria classification with priority scoring")

def extract_circle_data(entity):
    """Extract data from CIRCLE entities."""
    center = entity.dxf.center
    return {
        'x_coord': center[0],
        'y_coord': center[1],
        'z_coord': center[2] if len(center) > 2 else 0.0,
        'radius': entity.dxf.radius,
        'geometry_type': 'circle'
    }

def extract_line_data(entity):
    """Extract data from LINE entities."""
    start = entity.dxf.start
    end = entity.dxf.end
    return {
        'x_coord': (start[0] + end[0]) / 2,  # Midpoint
        'y_coord': (start[1] + end[1]) / 2,
        'z_coord': (start[2] + end[2]) / 2 if len(start) > 2 else 0.0,
        'start_x': start[0],
        'start_y': start[1],
        'start_z': start[2] if len(start) > 2 else 0.0,
        'end_x': end[0],
        'end_y': end[1],
        'end_z': end[2] if len(end) > 2 else 0.0,
        'length': np.linalg.norm(np.array(end) - np.array(start)),
        'geometry_type': 'line'
    }

def extract_insert_data(entity):
    """Extract data from INSERT (block reference) entities."""
    insert_point = entity.dxf.insert
    return {
        'x_coord': insert_point[0],
        'y_coord': insert_point[1],
        'z_coord': insert_point[2] if len(insert_point) > 2 else 0.0,
        'block_name': entity.dxf.name,
        'rotation': getattr(entity.dxf, 'rotation', 0.0),
        'scale_x': getattr(entity.dxf, 'xscale', 1.0),
        'scale_y': getattr(entity.dxf, 'yscale', 1.0),
        'scale_z': getattr(entity.dxf, 'zscale', 1.0),
        'geometry_type': 'insert'
    }

def extract_text_data(entity):
    """Extract data from TEXT and MTEXT entities."""
    insert_point = entity.dxf.insert
    text_content = entity.dxf.text if entity.dxftype() == 'TEXT' else entity.text
    return {
        'x_coord': insert_point[0],
        'y_coord': insert_point[1],
        'z_coord': insert_point[2] if len(insert_point) > 2 else 0.0,
        'text_content': text_content,
        'text_height': getattr(entity.dxf, 'height', None),
        'text_rotation': getattr(entity.dxf, 'rotation', 0.0),
        'geometry_type': 'text'
    }

print("Entity-specific extraction functions defined")
print("Supports CIRCLE, LINE, INSERT, TEXT, and MTEXT entities")

def extract_polyline_data(entity):
    """Extract data from LWPOLYLINE and POLYLINE entities."""
    # Get polyline points
    if hasattr(entity, 'get_points'):
        points = list(entity.get_points())
    else:
        points = []
    
    if points:
        # Calculate centroid
        points_array = np.array(points)
        centroid = np.mean(points_array, axis=0)
        return {
            'x_coord': centroid[0],
            'y_coord': centroid[1],
            'z_coord': centroid[2] if len(centroid) > 2 else 0.0,
            'point_count': len(points),
            'is_closed': getattr(entity.dxf, 'flags', 0) & 1,
            'geometry_type': 'polyline'
        }
    else:
        return {
            'x_coord': 0.0,
            'y_coord': 0.0,
            'z_coord': 0.0,
            'geometry_type': 'polyline'
        }

def extract_default_data(entity):
    """Extract default data for other entity types."""
    return {
        'x_coord': 0.0,
        'y_coord': 0.0,
        'z_coord': 0.0,
        'geometry_type': 'other'
    }

print("Additional extraction functions defined")
print("Supports POLYLINE entities and default handling")

def extract_entity_metadata(entity):
    """Extract comprehensive metadata from any CAD entity."""
    try:
        entity_type = entity.dxftype()
        handle = getattr(entity.dxf, 'handle', 'unknown')
        layer_name = getattr(entity.dxf, 'layer', '')
        
        # Base entity data
        entity_data = {
            'entity_id': handle,
            'entity_type': entity_type,
            'layer_name': layer_name,
            'color': getattr(entity.dxf, 'color', None),
            'linetype': getattr(entity.dxf, 'linetype', None),
            'lineweight': getattr(entity.dxf, 'lineweight', None)
        }
        
        # Extract geometry based on entity type
        if entity_type == 'CIRCLE':
            entity_data.update(extract_circle_data(entity))
        elif entity_type == 'LINE':
            entity_data.update(extract_line_data(entity))
        elif entity_type == 'INSERT':
            entity_data.update(extract_insert_data(entity))
        elif entity_type in ['TEXT', 'MTEXT']:
            entity_data.update(extract_text_data(entity))
        elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
            entity_data.update(extract_polyline_data(entity))
        else:
            entity_data.update(extract_default_data(entity))
        
        # Classify entity
        block_name = entity_data.get('block_name', '')
        text_content = entity_data.get('text_content', '')
        entity_data['classification'] = classify_entity(
            entity, layer_name, block_name, text_content
        )
        
        # Add extraction metadata
        entity_data['extraction_timestamp'] = datetime.now().isoformat()
        
        return entity_data
        
    except Exception as e:
        logger.warning(f"Error extracting entity data: {e}")
        return {
            'entity_id': 'error',
            'entity_type': getattr(entity, 'dxftype', lambda: 'unknown')(),
            'error': str(e),
            'extraction_timestamp': datetime.now().isoformat()
        }

print("Main entity processing function defined")
print("Handles all entity types with comprehensive metadata extraction")

# Initialize MLflow tracking
mlflow.set_experiment(f"cad_metadata_extraction_{project_type}")

# Start MLflow run
mlflow.start_run(run_name=f"{site_name}_cad_extraction_{timestamp}")

# Log parameters
mlflow.log_param("project_type", project_type)
mlflow.log_param("site_name", site_name)
mlflow.log_param("coordinate_system", coordinate_system)
mlflow.log_param("target_files", ",".join(target_files))

print("MLflow experiment tracking initialized")
print(f"Experiment: cad_metadata_extraction_{project_type}")
print(f"Run name: {site_name}_cad_extraction_{timestamp}")

# Discover CAD files
print("File Discovery")
print("=" * 50)

discovered_files = []

if target_files:
    # Process specific target files
    for target_file in target_files:
        file_path = cad_path / target_file
        if file_path.exists() and file_path.suffix.lower() in ['.dxf', '.dwg']:
            discovered_files.append(file_path)
            print(f"Found target file: {target_file}")
        else:
            print(f"Target file not found or invalid: {target_file}")
else:
    # Discover all DXF files in directory
    for file_path in cad_path.rglob("*.dxf"):
        discovered_files.append(file_path)
        print(f"Discovered DXF file: {file_path.name}")

print(f"\nTotal files to process: {len(discovered_files)}")
mlflow.log_metric("files_discovered", len(discovered_files))

# Display file information
for file_path in discovered_files:
    file_size_mb = file_path.stat().st_size / (1024 * 1024)
    print(f"  {file_path.name}: {file_size_mb:.2f} MB")

# Process each DXF file
all_entities = []
all_file_stats = []
processing_summary = {
    'total_files': len(discovered_files),
    'successful_files': 0,
    'failed_files': 0,
    'total_entities': 0,
    'classification_summary': defaultdict(int),
    'entity_type_summary': defaultdict(int)
}

print("Processing Files")
print("=" * 50)

for file_path in discovered_files:
    if file_path.suffix.lower() == '.dxf':
        print(f"\nProcessing: {file_path.name}")
        
        # Initialize file statistics
        file_stats = {
            'file_name': file_path.name,
            'file_path': str(file_path),
            'file_size_mb': file_path.stat().st_size / (1024 * 1024),
            'processing_timestamp': datetime.now().isoformat(),
            'entity_counts': defaultdict(int),
            'classification_counts': defaultdict(int),
            'layer_counts': defaultdict(int),
            'coordinate_bounds': {'min_x': float('inf'), 'max_x': float('-inf'),
                                 'min_y': float('inf'), 'max_y': float('-inf')},
            'errors': []
        }
        
        try:
            # Try to read the file, use recovery if needed
            try:
                doc = ezdxf.readfile(file_path)
                print(f"  Successfully loaded DXF file")
            except ezdxf.DXFStructureError:
                print(f"  DXF structure error, attempting recovery...")
                doc, auditor = recover.readfile(file_path)
                if auditor.has_errors:
                    file_stats['errors'].extend([str(error) for error in auditor.errors])
                    print(f"  Recovery completed with {len(auditor.errors)} errors")
                else:
                    print(f"  Recovery successful")
            
            # Get basic file information
            print(f"  DXF version: {doc.dxfversion}")
            print(f"  Number of layers: {len(doc.layers)}")
            print(f"  Number of blocks: {len(doc.blocks)}")
            
        except Exception as e:
            error_msg = f"Error loading {file_path.name}: {str(e)}"
            print(f"  {error_msg}")
            file_stats['errors'].append(error_msg)
            file_stats['processing_status'] = 'error'
            all_file_stats.append(file_stats)
            processing_summary['failed_files'] += 1
            continue
    else:
        print(f"Skipping non-DXF file: {file_path.name} (DWG processing requires conversion)")

# Continue processing the loaded DXF file
if 'doc' in locals():
    # Process modelspace entities
    msp = doc.modelspace()
    print(f"\nProcessing {len(msp)} entities in modelspace")
    
    entities_data = []
    
    for i, entity in enumerate(msp):
        if i % 1000 == 0 and i > 0:
            print(f"  Processed {i} entities...")
        
        entity_data = extract_entity_metadata(entity)
        entity_data['source_file'] = file_path.name
        entity_data['source_space'] = 'modelspace'
        entities_data.append(entity_data)
        
        # Update statistics
        file_stats['entity_counts'][entity_data.get('entity_type', 'unknown')] += 1
        file_stats['classification_counts'][entity_data.get('classification', 'unknown')] += 1
        file_stats['layer_counts'][entity_data.get('layer_name', 'unknown')] += 1
        
        # Update coordinate bounds
        x_coord = entity_data.get('x_coord')
        y_coord = entity_data.get('y_coord')
        if x_coord is not None and y_coord is not None:
            file_stats['coordinate_bounds']['min_x'] = min(file_stats['coordinate_bounds']['min_x'], x_coord)
            file_stats['coordinate_bounds']['max_x'] = max(file_stats['coordinate_bounds']['max_x'], x_coord)
            file_stats['coordinate_bounds']['min_y'] = min(file_stats['coordinate_bounds']['min_y'], y_coord)
            file_stats['coordinate_bounds']['max_y'] = max(file_stats['coordinate_bounds']['max_y'], y_coord)
    
    print(f"  Completed modelspace processing: {len(entities_data)} entities")
    
    # Add to overall collection
    all_entities.extend(entities_data)
    
    # Update processing summary
    processing_summary['total_entities'] += len(entities_data)
    for classification, count in file_stats['classification_counts'].items():
        processing_summary['classification_summary'][classification] += count
    for entity_type, count in file_stats['entity_counts'].items():
        processing_summary['entity_type_summary'][entity_type] += count
    
    file_stats['total_entities'] = len(entities_data)
    file_stats['processing_status'] = 'success'
    all_file_stats.append(file_stats)
    processing_summary['successful_files'] += 1
    
    print(f"\nModelspace extraction completed successfully")
else:
    print("No DXF file loaded for processing")

# Process block definitions if DXF file was loaded
if 'doc' in locals():
    print(f"\nProcessing {len(doc.blocks)} block definitions")
    
    block_entities_count = 0
    
    for block in doc.blocks:
        if block.name.startswith('*'):  # Skip anonymous blocks
            continue
        
        print(f"  Processing block: {block.name}")
        block_entity_count = 0
        
        for entity in block:
            entity_data = extract_entity_metadata(entity)
            entity_data['source_file'] = file_path.name
            entity_data['source_space'] = f'block:{block.name}'
            entities_data.append(entity_data)
            
            # Update statistics
            file_stats['entity_counts'][entity_data.get('entity_type', 'unknown')] += 1
            file_stats['classification_counts'][entity_data.get('classification', 'unknown')] += 1
            
            block_entity_count += 1
            block_entities_count += 1
        
        if block_entity_count > 0:
            print(f"    {block_entity_count} entities in block {block.name}")
    
    print(f"\nBlock processing completed: {block_entities_count} additional entities")
    
    # Update totals
    all_entities.extend(entities_data[len(all_entities):])
    processing_summary['total_entities'] = len(all_entities)
    
    # Update classification and entity type summaries
    for classification, count in file_stats['classification_counts'].items():
        processing_summary['classification_summary'][classification] = count
    for entity_type, count in file_stats['entity_counts'].items():
        processing_summary['entity_type_summary'][entity_type] = count
else:
    print("No DXF file loaded for block processing")

# Display processing summary
print("Processing Summary")
print("=" * 50)
print(f"Total files processed: {processing_summary['total_files']}")
print(f"Successful: {processing_summary['successful_files']}")
print(f"Failed: {processing_summary['failed_files']}")
print(f"Total entities extracted: {processing_summary['total_entities']}")

# Log metrics to MLflow
mlflow.log_metric("files_processed", processing_summary['total_files'])
mlflow.log_metric("files_successful", processing_summary['successful_files'])
mlflow.log_metric("files_failed", processing_summary['failed_files'])
mlflow.log_metric("total_entities", processing_summary['total_entities'])

if processing_summary['total_entities'] > 0:
    print("\nEntity Type Distribution:")
    for entity_type, count in sorted(processing_summary['entity_type_summary'].items()):
        percentage = (count / processing_summary['total_entities']) * 100
        print(f"  {entity_type}: {count} ({percentage:.1f}%)")
    
    print("\nClassification Distribution:")
    for classification, count in sorted(processing_summary['classification_summary'].items()):
        percentage = (count / processing_summary['total_entities']) * 100
        print(f"  {classification}: {count} ({percentage:.1f}%)")
        
        # Log classification metrics
        mlflow.log_metric(f"entities_{classification}", count)

# Create comprehensive DataFrame if entities were extracted
if all_entities:
    print("\nCreating DataFrame for analysis")
    
    # Convert to DataFrame
    entities_df = pd.DataFrame(all_entities)
    
    print(f"DataFrame created with {len(entities_df)} rows and {len(entities_df.columns)} columns")
    print(f"Columns: {list(entities_df.columns)}")
    
    # Display basic statistics
    print("\nDataFrame Info:")
    print(f"  Shape: {entities_df.shape}")
    print(f"  Memory usage: {entities_df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
    
    # Check for missing coordinates
    coords_missing = entities_df[['x_coord', 'y_coord']].isnull().any(axis=1).sum()
    print(f"  Entities with missing coordinates: {coords_missing}")
    
    # Display sample data
    print("\nSample of extracted data:")
    display_columns = ['entity_type', 'classification', 'layer_name', 'x_coord', 'y_coord', 'geometry_type']
    available_columns = [col for col in display_columns if col in entities_df.columns]
    print(entities_df[available_columns].head(10).to_string())
    
else:
    print("\nNo entities extracted - cannot create DataFrame")
    entities_df = pd.DataFrame()

# Analyze coordinates if data is available
if not entities_df.empty and 'x_coord' in entities_df.columns:
    print("\nCoordinate Analysis")
    print("=" * 50)
    
    # Filter entities with valid coordinates
    entities_with_coords = entities_df.dropna(subset=['x_coord', 'y_coord'])
    
    if not entities_with_coords.empty:
        min_x = entities_with_coords['x_coord'].min()
        max_x = entities_with_coords['x_coord'].max()
        min_y = entities_with_coords['y_coord'].min()
        max_y = entities_with_coords['y_coord'].max()
        
        print(f"Coordinate bounds:")
        print(f"  X range: {min_x:.2f} to {max_x:.2f} (span: {max_x - min_x:.2f} m)")
        print(f"  Y range: {min_y:.2f} to {max_y:.2f} (span: {max_y - min_y:.2f} m)")
        print(f"  Coordinate system: {coordinate_system}")
        
        # Check if coordinates are in expected UTM range for Italy
        utm_33n_x_min, utm_33n_x_max = 166021, 833978
        utm_33n_y_min, utm_33n_y_max = 0, 9329005
        
        x_in_range = (min_x >= utm_33n_x_min) and (max_x <= utm_33n_x_max)
        y_in_range = (min_y >= utm_33n_y_min) and (max_y <= utm_33n_y_max)
        
        print(f"\nUTM Zone 33N validation:")
        print(f"  X coordinates in valid range: {'Yes' if x_in_range else 'No'}")
        print(f"  Y coordinates in valid range: {'Yes' if y_in_range else 'No'}")
        
        # Log coordinate bounds to MLflow
        mlflow.log_metric("coord_min_x", min_x)
        mlflow.log_metric("coord_max_x", max_x)
        mlflow.log_metric("coord_min_y", min_y)
        mlflow.log_metric("coord_max_y", max_y)
        mlflow.log_metric("coord_span_x", max_x - min_x)
        mlflow.log_metric("coord_span_y", max_y - min_y)
        
        # Calculate coordinate completeness
        coord_completeness = len(entities_with_coords) / len(entities_df)
        print(f"\nCoordinate completeness: {coord_completeness:.1%} ({len(entities_with_coords)}/{len(entities_df)})")
        mlflow.log_metric("coordinate_completeness_pct", coord_completeness * 100)
    
    else:
        print("No entities with valid coordinates found")
else:
    print("No coordinate data available for analysis")

# Generate structured output if entities were extracted
if not entities_df.empty:
    print("\nGenerating Structured Output")
    print("=" * 50)
    
    # Save main entities CSV
    entities_csv_path = run_output_dir / f"{project_type}_{site_name}_cad_entities.csv"
    entities_df.to_csv(entities_csv_path, index=False)
    print(f"Main entities CSV saved: {entities_csv_path.name}")
    
    # Save classification-specific CSVs
    classification_files = {}
    for classification in processing_summary['classification_summary'].keys():
        if classification != 'unknown':
            classified_df = entities_df[entities_df['classification'] == classification]
            if not classified_df.empty:
                classified_csv_path = run_output_dir / f"{project_type}_{site_name}_cad_{classification}.csv"
                classified_df.to_csv(classified_csv_path, index=False)
                classification_files[classification] = str(classified_csv_path)
                print(f"{classification.title()} entities CSV saved: {classified_csv_path.name} ({len(classified_df)} entities)")
    
    # Save file statistics
    if all_file_stats:
        file_stats_df = pd.DataFrame(all_file_stats)
        file_stats_csv_path = run_output_dir / f"{project_type}_{site_name}_cad_file_stats.csv"
        file_stats_df.to_csv(file_stats_csv_path, index=False)
        print(f"File statistics CSV saved: {file_stats_csv_path.name}")
    
    # Save processing summary as JSON
    summary_json_path = run_output_dir / f"{project_type}_{site_name}_cad_summary.json"
    summary_for_json = {
        'processing_summary': dict(processing_summary),
        'classification_summary': dict(processing_summary['classification_summary']),
        'entity_type_summary': dict(processing_summary['entity_type_summary']),
        'coordinate_system': coordinate_system,
        'extraction_timestamp': datetime.now().isoformat()
    }
    
    with open(summary_json_path, 'w') as f:
        json.dump(summary_for_json, f, indent=2)
    print(f"Processing summary JSON saved: {summary_json_path.name}")
    
    # Log artifacts to MLflow
    mlflow.log_artifact(str(entities_csv_path))
    mlflow.log_artifact(str(summary_json_path))
    if 'file_stats_csv_path' in locals():
        mlflow.log_artifact(str(file_stats_csv_path))
    
    for classification, file_path in classification_files.items():
        mlflow.log_artifact(file_path)
    
    print(f"\nAll output files logged to MLflow")
    
else:
    print("\nNo entities extracted - skipping output generation")
    mlflow.log_metric("extraction_success", 0)

def calculate_confidence_score(row):
    """Calculate confidence score based on data completeness and classification."""
    score = 0.0
    
    # Base score for having coordinates
    if pd.notna(row.get('x_coord')) and pd.notna(row.get('y_coord')):
        score += 0.3
    
    # Score for classification
    if row.get('classification', 'unknown') != 'unknown':
        score += 0.3
    
    # Score for having geometric properties
    if row.get('geometry_type', '') in ['circle', 'line', 'polyline', 'insert']:
        score += 0.2
    
    # Score for having layer information
    if row.get('layer_name', ''):
        score += 0.1
    
    # Score for having additional properties
    if pd.notna(row.get('radius')) or pd.notna(row.get('length')) or pd.notna(row.get('text_content')):
        score += 0.1
    
    return min(score, 1.0)

def map_classification_to_element_type(classification):
    """Map CAD classification to standard element types."""
    mapping = {
        'pile': 'pile',
        'foundation': 'foundation',
        'panel': 'solar_panel',
        'building': 'building',
        'road': 'infrastructure',
        'trench': 'infrastructure',
        'electrical': 'electrical',
        'annotation': 'annotation',
        'unknown': 'other'
    }
    return mapping.get(classification.lower(), 'other')

print("Schema conversion helper functions defined")

# Generate schema-compliant outputs if entities were extracted
if not entities_df.empty:
    print("\nGenerating Schema-Compliant Datasets")
    print("=" * 50)
    
    # Create alignment-ready dataset
    pile_entities = entities_df[entities_df['classification'] == 'pile']
    foundation_entities = entities_df[entities_df['classification'] == 'foundation']
    
    alignment_data = {
        'metadata': {
            'dataset_type': 'cad_alignment_points',
            'coordinate_system': coordinate_system,
            'creation_timestamp': datetime.now().isoformat(),
            'total_points': len(pile_entities) + len(foundation_entities)
        },
        'pile_points': [],
        'foundation_points': [],
        'coordinate_bounds': {
            'min_x': None,
            'max_x': None,
            'min_y': None,
            'max_y': None
        }
    }
    
    # Process pile entities
    for idx, row in pile_entities.iterrows():
        if pd.notna(row.get('x_coord')) and pd.notna(row.get('y_coord')):
            pile_point = {
                'id': str(row.get('entity_id', f'pile_{idx}')),
                'x': float(row['x_coord']),
                'y': float(row['y_coord']),
                'z': float(row.get('z_coord', 0.0)),
                'type': 'pile',
                'subtype': row.get('block_name', 'unknown'),
                'layer': row.get('layer_name', ''),
                'confidence': calculate_confidence_score(row)
            }
            alignment_data['pile_points'].append(pile_point)
    
    # Process foundation entities
    for idx, row in foundation_entities.iterrows():
        if pd.notna(row.get('x_coord')) and pd.notna(row.get('y_coord')):
            foundation_point = {
                'id': str(row.get('entity_id', f'foundation_{idx}')),
                'x': float(row['x_coord']),
                'y': float(row['y_coord']),
                'z': float(row.get('z_coord', 0.0)),
                'type': 'foundation',
                'subtype': row.get('block_name', 'unknown'),
                'layer': row.get('layer_name', ''),
                'confidence': calculate_confidence_score(row)
            }
            alignment_data['foundation_points'].append(foundation_point)
    
    # Calculate coordinate bounds
    all_points = alignment_data['pile_points'] + alignment_data['foundation_points']
    if all_points:
        x_coords = [p['x'] for p in all_points]
        y_coords = [p['y'] for p in all_points]
        alignment_data['coordinate_bounds'] = {
            'min_x': min(x_coords),
            'max_x': max(x_coords),
            'min_y': min(y_coords),
            'max_y': max(y_coords)
        }
    
    # Save alignment dataset
    alignment_path = run_output_dir / f"{project_type}_{site_name}_alignment_points.json"
    with open(alignment_path, 'w') as f:
        json.dump(alignment_data, f, indent=2, default=str)
    print(f"Alignment dataset saved: {alignment_path.name}")
    print(f"  - Pile points: {len(alignment_data['pile_points'])}")
    print(f"  - Foundation points: {len(alignment_data['foundation_points'])}")
    
    # Log alignment metrics
    mlflow.log_metric("alignment_points_total", len(all_points))
    mlflow.log_metric("alignment_pile_points", len(alignment_data['pile_points']))
    mlflow.log_metric("alignment_foundation_points", len(alignment_data['foundation_points']))
    mlflow.log_artifact(str(alignment_path))
    
else:
    print("\nNo entities available for schema conversion")

# Create labeling dataset if entities were extracted
if not entities_df.empty:
    print("\nCreating Labeling Dataset")
    print("=" * 30)
    
    labeling_data = {
        'metadata': {
            'dataset_type': 'cad_labeling_data',
            'coordinate_system': coordinate_system,
            'creation_timestamp': datetime.now().isoformat(),
            'total_entities': len(entities_df)
        },
        'classified_entities': {},
        'text_annotations': [],
        'classification_summary': {}
    }
    
    # Group entities by classification
    for classification in entities_df['classification'].unique():
        if classification != 'unknown':
            classified_entities = entities_df[entities_df['classification'] == classification]
            
            entity_list = []
            for idx, row in classified_entities.iterrows():
                if pd.notna(row.get('x_coord')) and pd.notna(row.get('y_coord')):
                    entity = {
                        'id': str(row.get('entity_id', f'{classification}_{idx}')),
                        'x': float(row['x_coord']),
                        'y': float(row['y_coord']),
                        'z': float(row.get('z_coord', 0.0)),
                        'entity_type': row.get('entity_type', ''),
                        'layer': row.get('layer_name', ''),
                        'confidence': calculate_confidence_score(row)
                    }
                    entity_list.append(entity)
            
            labeling_data['classified_entities'][classification] = entity_list
            labeling_data['classification_summary'][classification] = len(entity_list)
            print(f"  {classification}: {len(entity_list)} entities")
    
    # Extract text annotations
    text_entities = entities_df[entities_df['geometry_type'] == 'text']
    for idx, row in text_entities.iterrows():
        if pd.notna(row.get('text_content')) and pd.notna(row.get('x_coord')):
            annotation = {
                'id': str(row.get('entity_id', f'text_{idx}')),
                'x': float(row['x_coord']),
                'y': float(row['y_coord']),
                'z': float(row.get('z_coord', 0.0)),
                'text': str(row['text_content']),
                'height': float(row.get('text_height', 0)) if pd.notna(row.get('text_height')) else None,
                'rotation': float(row.get('text_rotation', 0)) if pd.notna(row.get('text_rotation')) else None,
                'layer': row.get('layer_name', ''),
                'classification': row.get('classification', 'annotation')
            }
            labeling_data['text_annotations'].append(annotation)
    
    # Save labeling dataset
    labeling_path = run_output_dir / f"{project_type}_{site_name}_labeling_data.json"
    with open(labeling_path, 'w') as f:
        json.dump(labeling_data, f, indent=2, default=str)
    print(f"\nLabeling dataset saved: {labeling_path.name}")
    print(f"  - Text annotations: {len(labeling_data['text_annotations'])}")
    print(f"  - Classified entity types: {len(labeling_data['classified_entities'])}")
    
    # Log labeling metrics
    mlflow.log_metric("text_annotations", len(labeling_data['text_annotations']))
    mlflow.log_artifact(str(labeling_path))
    
else:
    print("\nNo entities available for labeling dataset creation")

# Display final analysis if entities were extracted
if not entities_df.empty:
    print("\nFinal Analysis and Summary")
    print("=" * 60)
    
    # Classification quality metrics
    classified_entities = len(entities_df[entities_df['classification'] != 'unknown'])
    classification_rate = (classified_entities / len(entities_df)) * 100
    print(f"Classification success rate: {classification_rate:.1f}% ({classified_entities}/{len(entities_df)})")
    
    # Coordinate quality metrics
    entities_with_coords = entities_df.dropna(subset=['x_coord', 'y_coord'])
    coord_completeness = (len(entities_with_coords) / len(entities_df)) * 100
    print(f"Coordinate completeness: {coord_completeness:.1f}% ({len(entities_with_coords)}/{len(entities_df)})")
    
    # Layer analysis
    print(f"\nLayer Analysis:")
    layer_counts = entities_df['layer_name'].value_counts().head(10)
    for layer, count in layer_counts.items():
        print(f"  {layer}: {count} entities")
    
    # Entities for alignment and labeling
    print(f"\nReadiness for Downstream Workflows:")
    
    if 'alignment_data' in locals():
        total_alignment_points = len(alignment_data['pile_points']) + len(alignment_data['foundation_points'])
        print(f"  Alignment points available: {total_alignment_points}")
        print(f"    - Pile points: {len(alignment_data['pile_points'])}")
        print(f"    - Foundation points: {len(alignment_data['foundation_points'])}")
        
        if total_alignment_points >= 4:
            print(f"    Status: Ready for point cloud alignment")
        else:
            print(f"    Status: Insufficient points for reliable alignment")
    
    if 'labeling_data' in locals():
        print(f"  Text annotations available: {len(labeling_data['text_annotations'])}")
        print(f"  Classified entity categories: {len(labeling_data['classified_entities'])}")
        print(f"    Status: Ready for automated labeling workflows")
    
    # Log final quality metrics
    mlflow.log_metric("classification_success_pct", classification_rate)
    mlflow.log_metric("coordinate_completeness_pct", coord_completeness)
    
    print(f"\nOutput Directory: {run_output_dir}")
    print(f"Coordinate System: {coordinate_system}")
    print(f"Processing Timestamp: {timestamp}")
    
else:
    print("\nNo entities extracted - check input files and processing errors")

# Final completion summary
print("\n" + "=" * 60)
print("EXPLORATORY CAD METADATA EXTRACTION COMPLETE")
print("=" * 60)

if processing_summary['total_entities'] > 0:
    print(f"\nSuccessfully processed {processing_summary['successful_files']}/{processing_summary['total_files']} files")
    print(f"Extracted {processing_summary['total_entities']} entities with {classification_rate:.1f}% classification rate")
    
    if 'alignment_data' in locals():
        total_alignment_points = len(alignment_data['pile_points']) + len(alignment_data['foundation_points'])
        print(f"Generated {total_alignment_points} alignment points for point cloud registration")
    
    if 'labeling_data' in locals():
        print(f"Generated {len(labeling_data['text_annotations'])} text annotations for labeling")
    
    print(f"\nOutput directory: {run_output_dir}")
    print(f"Coordinate system: {coordinate_system}")
    
    print(f"\nNext Steps:")
    print("1. Use alignment points for point cloud to CAD registration")
    print("2. Apply classified entities for automated point cloud labeling")
    print("3. Validate coordinate system consistency with survey data")
    print("4. Integrate with existing preprocessing pipeline")
    
else:
    print(f"\nNo entities extracted - check input files and processing errors")
    print("Review file statistics and error logs for troubleshooting")

# End MLflow run
mlflow.end_run()
print(f"\nMLflow run completed and logged")

print("\n" + "=" * 60)