#!/usr/bin/env python3
"""
Simple CAD Extraction Accuracy Analysis

This script provides a comprehensive analysis of CAD metadata extraction accuracy
by comparing reference data (castro_cad.csv) with extracted data and ground truth.
"""

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
import json
from collections import defaultdict

def main():
    print("CAD Extraction Accuracy Analysis - Montalto di Castro")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # File paths
    reference_file = "../../../data/reference/castro_cad.csv"
    ground_truth_file = "../../../data/reference/montalto_di_castro_ground_truth.csv"
    extracted_file = "../../../data/raw/motali_de_castro/cad/enhanced_output/cad_extraction_pile_20250630_143601.csv"
    output_dir = "../../../output_runs/validation"
    
    # Load data
    print("\n1. LOADING DATA")
    print("-" * 30)
    
    reference_df = pd.read_csv(reference_file)
    print(f"Reference data loaded: {len(reference_df)} entities")
    
    extracted_df = pd.read_csv(extracted_file)
    print(f"Extracted data loaded: {len(extracted_df)} entities")
    
    ground_truth_df = pd.read_csv(ground_truth_file)
    print(f"Ground truth loaded: {len(ground_truth_df)} specifications")
    
    # Create ground truth lookup
    ground_truth = {}
    for _, row in ground_truth_df.iterrows():
        key = row['Features'].lower().replace(' ', '_')
        ground_truth[key] = {
            'planned': row['Planned'],
            'unit': row['Unit'],
            'category': row['Categories'],
            'notes': row.get('Notes', '')
        }
    
    # Analyze reference data patterns
    print("\n2. REFERENCE DATA ANALYSIS")
    print("-" * 30)
    
    reference_patterns = defaultdict(int)
    for name in reference_df['Name']:
        if 'TRJ-52' in name:
            if 'INT' in name:
                reference_patterns['TRJ-52-INT'] += 1
            elif 'EXT' in name:
                reference_patterns['TRJ-52-EXT'] += 1
            elif 'EDGE' in name:
                reference_patterns['TRJ-52-EDGE'] += 1
        elif 'TRJ-26' in name:
            if 'INT' in name:
                reference_patterns['TRJ-26-INT'] += 1
            elif 'EXT' in name:
                reference_patterns['TRJ-26-EXT'] += 1
    
    print("Reference tracker patterns:")
    for pattern, count in sorted(reference_patterns.items()):
        print(f"  {pattern}: {count}")
    
    total_ref_trackers = sum(reference_patterns.values())
    print(f"Total reference tracker entities: {total_ref_trackers}")
    
    # Analyze extracted data patterns
    print("\n3. EXTRACTED DATA ANALYSIS")
    print("-" * 30)
    
    tracker_layers = extracted_df[extracted_df['layer_name'].str.contains('Tracker', na=False)]
    tracker_patterns = tracker_layers['layer_name'].value_counts()
    
    print("Extracted tracker patterns:")
    for pattern, count in tracker_patterns.items():
        print(f"  {pattern}: {count}")
    
    extracted_trackers = len(tracker_layers)
    print(f"Total extracted tracker entities: {extracted_trackers}")
    
    pile_layers = extracted_df[extracted_df['layer_name'].str.contains('PILE', na=False)]
    pile_patterns = pile_layers['layer_name'].value_counts()
    
    print(f"\nExtracted pile patterns:")
    for pattern, count in pile_patterns.items():
        print(f"  {pattern}: {count}")
    
    extracted_piles = len(pile_layers)
    print(f"Total extracted pile entities: {extracted_piles}")
    
    # Coordinate analysis
    print("\n4. COORDINATE SYSTEM ANALYSIS")
    print("-" * 30)
    
    ref_x_range = (reference_df['X'].min(), reference_df['X'].max())
    ref_y_range = (reference_df['Y'].min(), reference_df['Y'].max())
    
    print(f"Reference coordinates:")
    print(f"  X range: {ref_x_range[0]:.2f} to {ref_x_range[1]:.2f}")
    print(f"  Y range: {ref_y_range[0]:.2f} to {ref_y_range[1]:.2f}")
    
    ext_x_range = (extracted_df['x_coord'].min(), extracted_df['x_coord'].max())
    ext_y_range = (extracted_df['y_coord'].min(), extracted_df['y_coord'].max())
    
    print(f"\nExtracted coordinates:")
    print(f"  X range: {ext_x_range[0]:.2f} to {ext_x_range[1]:.2f}")
    print(f"  Y range: {ext_y_range[0]:.2f} to {ext_y_range[1]:.2f}")
    
    # Check coordinate overlap
    x_overlap = (max(ref_x_range[0], ext_x_range[0]), min(ref_x_range[1], ext_x_range[1]))
    y_overlap = (max(ref_y_range[0], ext_y_range[0]), min(ref_y_range[1], ext_y_range[1]))
    
    has_overlap = x_overlap[0] <= x_overlap[1] and y_overlap[0] <= y_overlap[1]
    print(f"\nCoordinate overlap: {'✅ Yes' if has_overlap else '❌ No'}")
    
    # Accuracy assessment
    print("\n5. ACCURACY ASSESSMENT")
    print("-" * 30)
    
    expected_trackers = ground_truth['tracker']['planned']
    expected_piles = ground_truth['piling']['planned']
    expected_modules = ground_truth['modules']['planned']
    
    # Calculate module support piles from trackers
    estimated_modules = 0
    for layer, count in tracker_patterns.items():
        if '1x52' in layer:
            estimated_modules += count * 52
        elif '1x26' in layer:
            estimated_modules += count * 26
    
    print(f"Tracker Detection:")
    print(f"  Expected: {expected_trackers}")
    print(f"  Reference: {total_ref_trackers}")
    print(f"  Extracted: {extracted_trackers}")
    print(f"  Reference accuracy: {(total_ref_trackers/expected_trackers)*100:.1f}%")
    print(f"  Extraction accuracy: {(extracted_trackers/expected_trackers)*100:.1f}%")
    
    print(f"\nFoundation Pile Detection:")
    print(f"  Expected: {expected_piles}")
    print(f"  Extracted: {extracted_piles}")
    print(f"  Extraction accuracy: {(extracted_piles/expected_piles)*100:.1f}%")
    
    print(f"\nModule Support Pile Estimation:")
    print(f"  Expected: {expected_modules}")
    print(f"  Estimated: {estimated_modules}")
    print(f"  Estimation accuracy: {(estimated_modules/expected_modules)*100:.1f}%")
    
    # Create comparison table
    comparison_data = {
        'Infrastructure Type': ['Trackers', 'Foundation Piles', 'Module Support Piles'],
        'Ground Truth': [expected_trackers, expected_piles, expected_modules],
        'Reference (castro_cad.csv)': [total_ref_trackers, 0, 0],
        'Extracted (Automated)': [extracted_trackers, extracted_piles, estimated_modules],
        'Reference Accuracy (%)': [
            round((total_ref_trackers/expected_trackers)*100, 1),
            0.0,
            0.0
        ],
        'Extraction Accuracy (%)': [
            round((extracted_trackers/expected_trackers)*100, 1),
            round((extracted_piles/expected_piles)*100, 1),
            round((estimated_modules/expected_modules)*100, 1)
        ]
    }
    
    comparison_df = pd.DataFrame(comparison_data)
    
    print("\n6. DETAILED COMPARISON")
    print("-" * 30)
    print(comparison_df.to_string(index=False))
    
    # Overall accuracy
    accuracy_scores = comparison_df['Extraction Accuracy (%)']
    overall_accuracy = accuracy_scores.mean()
    
    print(f"\n7. OVERALL ASSESSMENT")
    print("-" * 30)
    print(f"Overall Accuracy Score: {overall_accuracy:.1f}%")
    
    if overall_accuracy >= 80:
        status = "✅ Excellent"
    elif overall_accuracy >= 60:
        status = "✅ Good"
    elif overall_accuracy >= 40:
        status = "⚠️ Needs Improvement"
    else:
        status = "❌ Critical Issues"
    
    print(f"Status: {status}")
    
    # Critical gaps
    print(f"\n8. CRITICAL GAPS")
    print("-" * 30)
    
    gaps = []
    for idx, row in comparison_df.iterrows():
        if row['Extraction Accuracy (%)'] < 50:
            gaps.append({
                'type': row['Infrastructure Type'],
                'accuracy': row['Extraction Accuracy (%)'],
                'missing': row['Ground Truth'] - row['Extracted (Automated)']
            })
    
    for gap in gaps:
        print(f"❌ {gap['type']}: {gap['accuracy']:.1f}% accuracy, missing {gap['missing']:.0f} entities")
    
    # Recommendations
    print(f"\n9. RECOMMENDATIONS")
    print("-" * 30)
    
    recommendations = [
        "1. Foundation pile detection needs major improvement (11.1% accuracy)",
        "2. Implement coordinate system transformation (EPSG:32633)",
        "3. Add detection for roads, fencing, and electrical infrastructure",
        "4. Enhance LINE entity processing for foundation piles",
        "5. Validate CAD layer naming conventions",
        "6. Implement spatial proximity validation with reference data"
    ]
    
    for rec in recommendations:
        print(f"  {rec}")
    
    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Save comparison CSV
    comparison_file = output_path / f"cad_extraction_accuracy_comparison_{timestamp}.csv"
    comparison_df.to_csv(comparison_file, index=False)
    
    # Save detailed report
    analysis_report = {
        'metadata': {
            'timestamp': timestamp,
            'project': 'motali_de_castro',
            'analysis_type': 'cad_extraction_accuracy'
        },
        'summary_statistics': {
            'overall_accuracy': float(overall_accuracy),
            'reference_entities': len(reference_df),
            'extracted_entities': len(extracted_df),
            'coordinate_overlap': has_overlap
        },
        'accuracy_by_type': comparison_df.to_dict('records'),
        'critical_gaps': gaps,
        'recommendations': recommendations
    }
    
    report_file = output_path / f"cad_extraction_accuracy_report_{timestamp}.json"
    with open(report_file, 'w') as f:
        json.dump(analysis_report, f, indent=2, default=str)
    
    print(f"\n10. RESULTS SAVED")
    print("-" * 30)
    print(f"Comparison CSV: {comparison_file.name}")
    print(f"Detailed report: {report_file.name}")
    print(f"Output directory: {output_path}")
    
    return analysis_report

if __name__ == "__main__":
    main()
