# Papermill parameters - these will be injected by Papermill
site_name = "Castro"  # Site name for output file naming
project_type = "ENEL"  # Options: "ENEL", "USA"

target_files = ["GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf", "GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg"]
search_path = "../../../data/raw"
output_dir = "../../../output_runs/cad_metadata"

## Setup and Imports

# Import libraries

import pandas as pd
import numpy as np
from pathlib import Path
import ezdxf
import json
from datetime import datetime
from collections import defaultdict
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

logger.info(f"Starting Solar CAD Metadata Extraction")
logger.info(f"Target files: {target_files}")
logger.info(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Find target files
logger.info("=== FINDING CAD FILES ===")
print("\nSearching for target files...")
for target_file in target_files:
    logger.info(f"\nLooking for: {target_file}")
    result = !find {search_path} -name "{target_file}" 2>/dev/null
    if result:
        file_path = Path(result[0])
        size_mb = file_path.stat().st_size / 1024 / 1024
        logger.info(f"  Found: {file_path} ({size_mb:.1f} MB)")
    else:
        logger.info(f"  Not found: {target_file}")



print("\n=== QUICK CAD ANALYSIS ===")

# Check if ogrinfo is available
ogrinfo_check = !which ogrinfo
if ogrinfo_check:
    print("ogrinfo available")
    
    # Analyze DXF files only (ogrinfo works best with DXF)
    for file_path in cad_files:
        if file_path.suffix.lower() == '.dxf':
            print(f"\nAnalyzing {file_path.name}:")
            !ogrinfo -so "{file_path}"
        else:
            print(f"\nSkipping {file_path.name} (DWG - use DXF for ogrinfo)")
else:
    print("ogrinfo not available - install with: brew install gdal")

def extract_coordinates_simple(file_path):
    """Simple coordinate extraction focused on trackers and modules."""
    try:
        doc = ezdxf.readfile(file_path)
        msp = doc.modelspace()
        
        trackers = []  # INSERT entities (high value for alignment)
        modules = []   # POLYLINE entities (medium value for alignment)
        
        for entity in msp:
            layer = getattr(entity.dxf, 'layer', '').upper()
            entity_type = entity.dxftype()
            
            # Extract tracker coordinates (INSERT entities)
            if entity_type == 'INSERT' and any(keyword in layer for keyword in ['TRACKER', 'CVT']):
                if hasattr(entity.dxf, 'insert'):
                    pt = entity.dxf.insert
                    # Skip zero coordinates
                    if not (pt.x == 0.0 and pt.y == 0.0):
                        trackers.append({
                            'x': pt.x, 'y': pt.y, 'z': pt.z,
                            'layer': layer,
                            'block_name': getattr(entity.dxf, 'name', ''),
                            'type': 'tracker'
                        })
            
            # Extract module boundaries (POLYLINE entities)
            elif entity_type in ['LWPOLYLINE', 'POLYLINE'] and any(keyword in layer for keyword in ['PVCASE', 'PV', 'MODULE']):
                try:
                    bbox = entity.bbox()
                    if bbox:
                        center_x = (bbox[0].x + bbox[1].x) / 2
                        center_y = (bbox[0].y + bbox[1].y) / 2
                        # Skip zero coordinates
                        if not (center_x == 0.0 and center_y == 0.0):
                            modules.append({
                                'x': center_x, 'y': center_y, 'z': (bbox[0].z + bbox[1].z) / 2,
                                'layer': layer,
                                'x_min': bbox[0].x, 'y_min': bbox[0].y,
                                'x_max': bbox[1].x, 'y_max': bbox[1].y,
                                'type': 'module'
                            })
                except:
                    pass
        
        return trackers, modules
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return [], []

print("Simple coordinate extraction function ready")

logger.info("\n=== PROCESSING CAD FILES ===")

# Load DXF CAD File
if cad_files:
    dxf_file_path = cad_files[0]  # or select specific one if multiple
    logger.info(f"\nLoading DXF file: {dxf_file_path}")
    
    doc = ezdxf.readfile(dxf_file_path)
    modelspace = doc.modelspace()
else:
    raise FileNotFoundError("No CAD files found. Please check the search path and file names.")


# Entity Type Exploration
all_entities = list(modelspace)
entity_types = [e.dxftype() for e in all_entities]
pd.Series(entity_types).value_counts()

# Entity Type Frequency Table
entity_type_series = pd.Series(entity_types)
entity_counts = entity_type_series.value_counts().reset_index()
entity_counts.columns = ['Entity Type', 'Count']
entity_counts['Percentage'] = (entity_counts['Count'] / entity_counts['Count'].sum()) * 100
entity_counts['Percentage'] = entity_counts['Percentage'].map("{:.2f}%".format)
entity_counts

# Visualize Entity Type Distribution
import seaborn as sns
import matplotlib.pyplot as plt

# Bar Plot of Entity Types
plt.figure(figsize=(10, 5))
sns.barplot(data=entity_counts, x='Count', y='Entity Type', palette='viridis')
plt.title("Distribution of Entity Types in CAD")
plt.xlabel("Number of Entities")
plt.ylabel("Entity Type")
plt.tight_layout()
plt.show()


from collections import defaultdict

layer_map = defaultdict(set)

for e in modelspace:
    layer = getattr(e.dxf, "layer", "unknown")
    entity_type = e.dxftype()
    layer_map[entity_type].add(layer)

# See what layers are used in INSERTs, LWPOLYLINEs, etc.
for etype in ["INSERT", "LWPOLYLINE", "LINE", "MTEXT"]:
    print(f"\nEntity: {etype}")
    print(layer_map[etype])

# Defined classification rules
classification_rules = {
    'pile': {
        'layer_keywords': ['pile', 'palo', 'pali'],
        'block_keywords': ['pile', 'palo', 'p_'],
        'text_keywords': ['pile', 'palo', 'p-'],
        'entity_types': ['CIRCLE', 'INSERT', 'POINT'],
        'priority': 3
    },
    'panel': {
        'layer_keywords': ['panel', 'pannello', 'pv', 'solar', 'solare', 'modulo', 'moduli'],
        'block_keywords': ['panel', 'pannello', 'pv', 'solar', 'modulo'],
        'text_keywords': ['panel', 'pv', 'solar', 'modulo'],
        'entity_types': ['INSERT', 'LWPOLYLINE', 'POLYLINE'],
        'priority': 2
    },
    'road': {
        'layer_keywords': ['road', 'strada', 'strade', 'access', 'accesso', 'viabilita'],
        'block_keywords': ['road', 'strada', 'access'],
        'text_keywords': ['road', 'strada', 'access'],
        'entity_types': ['LWPOLYLINE', 'POLYLINE', 'LINE', 'ARC'],
        'priority': 2
    },
    'trench': {
        'layer_keywords': ['trench', 'trincea', 'cable', 'cavo', 'cavidotto', 'cavidotti'],
        'block_keywords': ['trench', 'cable', 'cavo', 'cavidotto'],
        'text_keywords': ['trench', 'cable', 'cavo', 'cavidotto'],
        'entity_types': ['LWPOLYLINE', 'POLYLINE', 'LINE'],
        'priority': 2
    },
    'foundation': {
        'layer_keywords': ['foundation', 'fondazione', 'base', 'cabin', 'cabina', 'cabine'],
        'block_keywords': ['foundation', 'cabin', 'cabina', 'base'],
        'text_keywords': ['foundation', 'cabin', 'cabina', 'base'],
        'entity_types': ['LWPOLYLINE', 'POLYLINE', 'INSERT'],
        'priority': 3
    },
    'electrical': {
        'layer_keywords': ['electrical', 'elettrico', 'elettrica', 'power', 'energia', 'electric'],
        'block_keywords': ['electrical', 'elettrico', 'power', 'electric'],
        'text_keywords': ['electrical', 'power', 'kw', 'v', 'elettrico'],
        'entity_types': ['LINE', 'LWPOLYLINE', 'INSERT'],
        'priority': 1
    },
    'building': {
        'layer_keywords': ['building', 'edificio', 'fabbricato', 'manufatto', 'costruzione'],
        'block_keywords': ['building', 'edificio', 'fabbricato'],
        'text_keywords': ['building', 'edificio'],
        'entity_types': ['LWPOLYLINE', 'POLYLINE', 'INSERT', 'HATCH'],
        'priority': 2
    },
    'annotation': {
        'layer_keywords': ['text', 'label', 'annotation', 'quota', 'dimension', 'dim'],
        'block_keywords': ['text', 'label', 'annotation'],
        'text_keywords': [],
        'entity_types': ['TEXT', 'MTEXT', 'DIMENSION'],
        'priority': 1
    }
}


# Classify entities
def classify_entity_dxf(entity, rules):
    etype = entity.dxftype()
    layer = getattr(entity.dxf, "layer", "").lower()
    block_name = getattr(entity.dxf, "name", "").lower() if hasattr(entity.dxf, "name") else ""
    text = ""
    if etype in ["TEXT", "MTEXT"]:
        text = entity.text.lower() if hasattr(entity, "text") else ""

    for tag, r in rules.items():
        if etype not in r['entity_types']:
            continue
        if any(k in layer for k in r['layer_keywords']):
            return tag
        if any(k in block_name for k in r['block_keywords']):
            return tag
        if any(k in text for k in r['text_keywords']):
            return tag
    return 'unclassified'

tagged_entities = defaultdict(list)

for e in modelspace:
    tag = classify_entity_dxf(e, classification_rules)
    tagged_entities[tag].append(e)

# Summary printout
for tag, group in tagged_entities.items():
    logger.info(f"{tag}: {len(group)} entities")

rows = []
for tag, ents in tagged_entities.items():
    for e in ents:
        rows.append({
            "Entity Type": e.dxftype(),
            "Layer": getattr(e.dxf, "layer", ""),
            "Tag": tag
        })

df = pd.DataFrame(rows)
df.to_csv("entity_classification.csv", index=False)
df.head()


# Extract centroids for specific tags
from shapely.geometry import Polygon, Point
import numpy as np

def get_centroid(entity):
    if entity.dxftype() == 'INSERT':
        return Point(entity.dxf.insert.x, entity.dxf.insert.y)
    elif entity.dxftype() in ['LWPOLYLINE', 'POLYLINE']:
        points = [tuple(p[:2]) for p in entity.get_points()]
        if len(points) >= 3:
            return Polygon(points).centroid
    return None

tracker_coords = []
module_coords = []

for tracker in tagged_entities.get('panel', []):
    pt = get_centroid(tracker)
    if pt:
        module_coords.append({'x': pt.x, 'y': pt.y})

for tracker in tagged_entities.get('pile', []):
    pt = get_centroid(tracker)
    if pt:
        tracker_coords.append({'x': pt.x, 'y': pt.y})


print("\n=== EXTRACTION SUMMARY ===")
print(f"Trackers extracted: {len(tracker_coords)}")
print(f"Modules extracted: {len(module_coords)}")

if tracker_coords:
    coords = np.array([[t['x'], t['y']] for t in tracker_coords])
    print("\nTracker coordinate ranges:")
    print(f"  X: {coords[:, 0].min():.1f} to {coords[:, 0].max():.1f}")
    print(f"  Y: {coords[:, 1].min():.1f} to {coords[:, 1].max():.1f}")
else:
    print("No tracker coordinates found.")


print("\n=== PROCESSING CAD FILES ===")

all_trackers = []
all_modules = []

for file_path in cad_files:
    if file_path.suffix.lower() == '.dxf':  # Process DXF files only
        print(f"\nProcessing: {file_path.name}")
        
        trackers, modules = extract_coordinates_simple(file_path)
        
        # Add source file info
        for tracker in trackers:
            tracker['source_file'] = file_path.name
        for module in modules:
            module['source_file'] = file_path.name
        
        all_trackers.extend(trackers)
        all_modules.extend(modules)
        
        print(f"  Trackers extracted: {len(trackers)}")
        print(f"  Modules extracted: {len(modules)}")
    else:
        print(f"\nSkipping: {file_path.name} (DWG format - convert to DXF first)")

print(f"\n=== EXTRACTION SUMMARY ===")
print(f"Total trackers: {len(all_trackers)}")
print(f"Total modules: {len(all_modules)}")

# Quick coordinate analysis
if all_trackers:
    tracker_coords = np.array([[t['x'], t['y']] for t in all_trackers])
    print(f"\nTracker coordinate ranges:")
    print(f"  X: {tracker_coords[:, 0].min():.1f} to {tracker_coords[:, 0].max():.1f}")
    print(f"  Y: {tracker_coords[:, 1].min():.1f} to {tracker_coords[:, 1].max():.1f}")

if all_modules:
    module_coords = np.array([[m['x'], m['y']] for m in all_modules])
    print(f"\nModule coordinate ranges:")
    print(f"  X: {module_coords[:, 0].min():.1f} to {module_coords[:, 0].max():.1f}")
    print(f"  Y: {module_coords[:, 1].min():.1f} to {module_coords[:, 1].max():.1f}")

if all_trackers or all_modules:
    # Create output directory
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_path = Path(output_dir)
    run_output_dir = output_path / f"simple_solar_extraction_{timestamp}"
    run_output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"\n=== SAVING OUTPUTS ===")
    print(f"Output directory: {run_output_dir}")
    
    # Save tracker coordinates (primary alignment points)
    if all_trackers:
        trackers_df = pd.DataFrame(all_trackers)
        tracker_file = run_output_dir / f"tracker_coordinates_{timestamp}.csv"
        trackers_df.to_csv(tracker_file, index=False)
        print(f"✅ Tracker coordinates: {tracker_file.name} ({len(all_trackers)} points)")
    
    # Save module boundaries (secondary alignment)
    if all_modules:
        modules_df = pd.DataFrame(all_modules)
        module_file = run_output_dir / f"module_boundaries_{timestamp}.csv"
        modules_df.to_csv(module_file, index=False)
        print(f"✅ Module boundaries: {module_file.name} ({len(all_modules)} areas)")
    
    # Save simple summary
    summary = {
        'extraction_timestamp': timestamp,
        'target_files': target_files,
        'files_processed': [f.name for f in cad_files if f.suffix.lower() == '.dxf'],
        'results': {
            'tracker_count': len(all_trackers),
            'module_count': len(all_modules)
        },
        'coordinate_ranges': {
            'tracker_x_range': [float(tracker_coords[:, 0].min()), float(tracker_coords[:, 0].max())] if all_trackers else None,
            'tracker_y_range': [float(tracker_coords[:, 1].min()), float(tracker_coords[:, 1].max())] if all_trackers else None,
            'module_x_range': [float(module_coords[:, 0].min()), float(module_coords[:, 0].max())] if all_modules else None,
            'module_y_range': [float(module_coords[:, 1].min()), float(module_coords[:, 1].max())] if all_modules else None
        },
        'usage': {
            'primary_alignment': 'Use tracker_coordinates.csv for point cloud registration',
            'area_validation': 'Use module_boundaries.csv for spatial validation'
        }
    }
    
    summary_file = run_output_dir / f"extraction_summary_{timestamp}.json"
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    print(f"✅ Summary: {summary_file.name}")
    
    print(f"\n🎯 Ready for alignment workflows!")
    print(f"📁 Output directory: {run_output_dir}")
    
else:
    print("\n❌ No data extracted - check file paths and formats")

print(f"\n📅 Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")