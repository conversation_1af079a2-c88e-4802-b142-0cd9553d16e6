# Import libraries
import pandas as pd
import numpy as np
from pathlib import Path
import ezdxf
import json
from datetime import datetime
from collections import defaultdict
import logging


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

logger.info("All tools imported successfully!")
logger.info(f"Starting extraction at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Papermill parameters - these will be injected by Papermill
site_name = "Castro"  # Site name for output file naming
project_type = "ENEL"  # Options: "ENEL", "USA"

target_files = ["GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf"]
                #, "GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg"]
search_path = "../../../data/raw"
output_dir = "../../../output_runs/cad_metadata"

logger.info("Configuration:")
logger.info(f"   Looking for: {target_files}")
logger.info(f"   Search in: {search_path}")
logger.info(f"   Save to: {output_dir}")

logger.info("STEP 3: Finding CAD files...")
logger.info("="*50)

# List to store files we find
found_files = []

# Look for each target file
for target_file in target_files:
    print(f"\nLooking for: {target_file}")
    
    # Use the 'find' command to search for the file
    result = !find {search_path} -name "{target_file}" 2>/dev/null
    
    if result:  # If we found the file
        file_path = Path(result[0])  # Get the first result
        file_size = file_path.stat().st_size / 1024 / 1024  # Size in MB
        
        print(f"-  Found: {file_path}")
        print(f"-  Size: {file_size:.1f} MB")
        
        found_files.append(file_path)
    else:
        print(f"-  Not found: {target_file}")

logger.info(f"\nSummary: Found {len(found_files)} out of {len(target_files)} target files")

print("STEP 4: Quick CAD analysis...")
print("=" * 50)

# Check if 'ogrinfo' tool is available
ogrinfo_check = !which ogrinfo 2>/dev/null

if ogrinfo_check:
    print("ogrinfo tool is available – showing structure of DXF files...\n")
    
    # Analyze each DXF file using ogrinfo
    for file_path in found_files:
        if file_path.suffix.lower() == '.dxf':
            print(f"\nAnalyzing: {file_path.name}")
            print("-" * 40)
            !ogrinfo -so "{file_path}"

def extract_solar_data(file_path):
    """
    Extract solar tracker and module data from a CAD file.
    
    This function looks for:
    - Solar trackers (INSERT entities in tracker layers)
    - Solar modules (POLYLINE entities in module layers)
    """
    print(f"\n🔧 Processing: {file_path.name}")
    
    try:
        # Open the CAD file
        doc = ezdxf.readfile(file_path)
        modelspace = doc.modelspace()  # Get the main drawing space
        
        # Lists to store what we find
        trackers = []  # Solar tracker positions
        modules = []   # Solar module boundaries
        
        print("   🔍 Scanning entities...")
        
        # Look at each entity (object) in the CAD file
        for entity in modelspace:
            # Get basic info about this entity
            layer_name = getattr(entity.dxf, 'layer', '').upper()  # Layer name in uppercase
            entity_type = entity.dxftype()  # Type of entity (INSERT, LINE, etc.)
            
            # Look for SOLAR TRACKERS
            # These are usually INSERT entities in layers with 'TRACKER' or 'CVT' in the name
            if (entity_type == 'INSERT' and 
                any(keyword in layer_name for keyword in ['TRACKER', 'CVT'])):
                
                if hasattr(entity.dxf, 'insert'):  # Make sure it has coordinates
                    point = entity.dxf.insert
                    
                    # Skip invalid coordinates (0,0,0 usually means no real position)
                    if not (point.x == 0.0 and point.y == 0.0):
                        tracker_data = {
                            'x': point.x,
                            'y': point.y,
                            'z': point.z,
                            'layer': layer_name,
                            'block_name': getattr(entity.dxf, 'name', ''),
                            'type': 'tracker',
                            'source_file': file_path.name
                        }
                        trackers.append(tracker_data)
            
            # Look for SOLAR MODULES
            # These are usually POLYLINE entities in layers with 'PVCASE', 'PV', or 'MODULE' in the name
            elif (entity_type in ['LWPOLYLINE', 'POLYLINE'] and 
                  any(keyword in layer_name for keyword in ['PVCASE', 'PV', 'MODULE'])):
                
                try:
                    # Get the boundary box of this polyline
                    bbox = entity.bbox()
                    if bbox:
                        # Calculate center point
                        center_x = (bbox[0].x + bbox[1].x) / 2
                        center_y = (bbox[0].y + bbox[1].y) / 2
                        center_z = (bbox[0].z + bbox[1].z) / 2
                        
                        # Skip invalid coordinates
                        if not (center_x == 0.0 and center_y == 0.0):
                            module_data = {
                                'x': center_x,
                                'y': center_y,
                                'z': center_z,
                                'layer': layer_name,
                                'x_min': bbox[0].x,
                                'y_min': bbox[0].y,
                                'x_max': bbox[1].x,
                                'y_max': bbox[1].y,
                                'type': 'module',
                                'source_file': file_path.name
                            }
                            modules.append(module_data)
                except:
                    # If we can't get the boundary, skip this entity
                    pass
        
        print(f"   ✅ Found {len(trackers)} trackers and {len(modules)} modules")
        return trackers, modules
        
    except Exception as e:
        print(f"   ❌ Error processing {file_path.name}: {e}")
        return [], []

print("🔧 Data extraction function ready!")

print("⚙️ STEP 6: Processing all CAD files...")
print("="*50)

# Lists to store all data from all files
all_trackers = []
all_modules = []

# Process each file we found
for file_path in found_files:
    # Only process DXF files (ezdxf works best with DXF)
    if file_path.suffix.lower() == '.dxf':
        # Extract data from this file
        trackers, modules = extract_solar_data(file_path)
        
        # Add to our master lists
        all_trackers.extend(trackers)
        all_modules.extend(modules)
        
    else:
        print(f"\n⏭️ Skipping: {file_path.name}")
        print("   (DWG files need to be converted to DXF first)")
        print("   (You can use AutoCAD, FreeCAD, or online converters)")

# Show summary of what we found
print(f"\n📊 EXTRACTION COMPLETE!")
print(f"   Total trackers found: {len(all_trackers)}")
print(f"   Total modules found: {len(all_modules)}")

# Show coordinate ranges if we found data
if all_trackers:
    tracker_coords = np.array([[t['x'], t['y']] for t in all_trackers])
    print(f"\n📍 Tracker coordinate ranges:")
    print(f"   X: {tracker_coords[:, 0].min():.1f} to {tracker_coords[:, 0].max():.1f}")
    print(f"   Y: {tracker_coords[:, 1].min():.1f} to {tracker_coords[:, 1].max():.1f}")

if all_modules:
    module_coords = np.array([[m['x'], m['y']] for m in all_modules])
    print(f"\n📍 Module coordinate ranges:")
    print(f"   X: {module_coords[:, 0].min():.1f} to {module_coords[:, 0].max():.1f}")
    print(f"   Y: {module_coords[:, 1].min():.1f} to {module_coords[:, 1].max():.1f}")

print("💾 STEP 7: Saving results...")
print("="*50)

if all_trackers or all_modules:
    # Create output directory with timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_path = Path(output_dir)
    run_output_dir = output_path / f"solar_extraction_{timestamp}"
    run_output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"📁 Output directory: {run_output_dir}")
    
    # Save tracker coordinates (most important for alignment)
    if all_trackers:
        trackers_df = pd.DataFrame(all_trackers)
        tracker_file = run_output_dir / f"tracker_coordinates_{timestamp}.csv"
        trackers_df.to_csv(tracker_file, index=False)
        print(f"✅ Saved tracker coordinates: {tracker_file.name}")
        print(f"   📊 {len(all_trackers)} tracker positions")
    
    # Save module boundaries (useful for validation)
    if all_modules:
        modules_df = pd.DataFrame(all_modules)
        module_file = run_output_dir / f"module_boundaries_{timestamp}.csv"
        modules_df.to_csv(module_file, index=False)
        print(f"✅ Saved module boundaries: {module_file.name}")
        print(f"   📊 {len(all_modules)} module areas")
    
    # Create a simple summary
    summary = {
        'extraction_info': {
            'timestamp': timestamp,
            'target_files': target_files,
            'files_processed': [f.name for f in found_files if f.suffix.lower() == '.dxf']
        },
        'results': {
            'tracker_count': len(all_trackers),
            'module_count': len(all_modules)
        },
        'coordinate_info': {},
        'next_steps': {
            'for_alignment': 'Use tracker_coordinates.csv as reference points for point cloud alignment',
            'for_validation': 'Use module_boundaries.csv to validate spatial coverage',
            'coordinate_system': 'Coordinates appear to be in UTM format (suitable for alignment)'
        }
    }
    
    # Add coordinate ranges to summary
    if all_trackers:
        summary['coordinate_info']['tracker_ranges'] = {
            'x_min': float(tracker_coords[:, 0].min()),
            'x_max': float(tracker_coords[:, 0].max()),
            'y_min': float(tracker_coords[:, 1].min()),
            'y_max': float(tracker_coords[:, 1].max())
        }
    
    if all_modules:
        summary['coordinate_info']['module_ranges'] = {
            'x_min': float(module_coords[:, 0].min()),
            'x_max': float(module_coords[:, 0].max()),
            'y_min': float(module_coords[:, 1].min()),
            'y_max': float(module_coords[:, 1].max())
        }
    
    # Save summary
    summary_file = run_output_dir / f"extraction_summary_{timestamp}.json"
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    print(f"✅ Saved summary: {summary_file.name}")
    
    print(f"\n🎯 SUCCESS! All files saved to: {run_output_dir}")
    print(f"\n📋 What to do next:")
    print(f"   1. Use tracker_coordinates.csv for point cloud alignment")
    print(f"   2. Use module_boundaries.csv for spatial validation")
    print(f"   3. Check extraction_summary.json for details")
    
else:
    print("❌ No data was extracted!")
    print("\n🔍 Troubleshooting:")
    print("   1. Check that DXF files were found")
    print("   2. Verify CAD files contain tracker/module layers")
    print("   3. Look for layers with names like 'TRACKER', 'CVT', 'PVCASE', 'PV'")

print(f"\n📅 Extraction completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")