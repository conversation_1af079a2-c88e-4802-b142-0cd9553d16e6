# Papermill parameters
project_type = "solar_project"  # Generic for any solar project
site_name = "cad_extraction"
cad_data_path = "../../../data/raw"  # Auto-discover CAD files
output_dir = "../../../output_runs/cad_metadata"
coordinate_system = "auto"  # Auto-detect or specify (e.g., EPSG:32633)
focus_on_alignment = True  # Extract only alignment-relevant data
target_files = ["GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf", "GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg"]  # Specific files to process

import pandas as pd
import numpy as np
from pathlib import Path
import ezdxf
import json
from datetime import datetime
from collections import defaultdict
import logging
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

print(f"Solar CAD Extraction for Alignment - {project_type.title()}")
print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print(f"Target files: {target_files}")
print("=" * 60)

# Check if ogrinfo is available
try:
    !which ogrinfo
    ogrinfo_available = True
    print("✅ ogrinfo is available for advanced CAD analysis")
except:
    ogrinfo_available = False
    print("⚠️ ogrinfo not found. Install GDAL tools for enhanced analysis:")
    print("  macOS: brew install gdal")
    print("  Ubuntu: sudo apt-get install gdal-bin")
    print("  Will use basic analysis only")

def discover_cad_files(base_path, target_files=None):
    """Discover DXF and DWG files, prioritizing target files."""
    base_path = Path(base_path)
    cad_files = []
    
    # Search for CAD files
    for pattern in ['**/*.dxf', '**/*.DXF', '**/*.dwg', '**/*.DWG']:
        cad_files.extend(base_path.glob(pattern))
    
    # Filter for target files if specified
    if target_files:
        target_files_found = []
        for target in target_files:
            matching = [f for f in cad_files if f.name == target]
            target_files_found.extend(matching)
        
        if target_files_found:
            print(f"Found {len(target_files_found)} target files out of {len(target_files)} requested")
            return sorted(target_files_found)
        else:
            print(f"Target files not found, using all discovered files")
    
    return sorted(cad_files)

def analyze_solar_layers(file_path):
    """Analyze CAD file for solar-specific layers."""
    try:
        doc = ezdxf.readfile(file_path)
        msp = doc.modelspace()
        
        # Count solar-relevant entities
        solar_layers = defaultdict(int)
        entity_types = defaultdict(int)
        all_layers = defaultdict(int)
        
        for entity in msp:
            layer = getattr(entity.dxf, 'layer', 'unknown')
            entity_type = entity.dxftype()
            
            all_layers[layer] += 1
            entity_types[entity_type] += 1
            
            # Focus on solar-relevant layers (broader patterns)
            layer_upper = layer.upper()
            if any(keyword in layer_upper for keyword in ['TRACKER', 'CVT', 'TRJ', 'PVCASE', 'PV', 'SOLAR', 'MODULE', 'PANEL']):
                solar_layers[layer] += 1
        
        return {
            'solar_layers': dict(solar_layers),
            'all_layers': dict(all_layers),
            'entity_types': dict(entity_types),
            'total_entities': sum(entity_types.values()),
            'solar_entities': sum(solar_layers.values())
        }
    except Exception as e:
        logger.warning(f"Could not analyze {file_path}: {e}")
        return None

# Discover and analyze CAD files
print("=== CAD FILE DISCOVERY ===")
cad_files = discover_cad_files(cad_data_path, target_files)

if not cad_files:
    print(f"No CAD files found in {cad_data_path}")
    print(f"Looking for: {target_files}")
else:
    print(f"Found {len(cad_files)} CAD files:")
    for i, file_path in enumerate(cad_files, 1):
        print(f"  {i}. {file_path.name} ({file_path.stat().st_size / 1024 / 1024:.1f} MB)")
    
    # Analyze solar content
    print(f"\n=== SOLAR CONTENT ANALYSIS ===")
    file_analyses = {}
    for file_path in cad_files:
        print(f"\nAnalyzing: {file_path.name}")
        analysis = analyze_solar_layers(file_path)
        if analysis:
            file_analyses[file_path.name] = analysis
            print(f"  Total entities: {analysis['total_entities']}")
            print(f"  Solar-relevant entities: {analysis['solar_entities']}")
            print(f"  Solar layers found: {len(analysis['solar_layers'])}")
            
            # Show top solar layers
            if analysis['solar_layers']:
                top_solar = sorted(analysis['solar_layers'].items(), key=lambda x: x[1], reverse=True)[:5]
                print(f"  Top solar layers: {dict(top_solar)}")

def parse_ogrinfo_output(output_text):
    """Parse ogrinfo output to extract useful information."""
    import re
    
    info = {
        'layers': [],
        'extent': None,
        'feature_counts': {},
        'coordinate_system': None
    }
    
    lines = output_text.split('\n')
    current_layer = None
    
    for line in lines:
        line = line.strip()
        
        # Extract layer information
        if line.startswith('Layer name:'):
            current_layer = line.split(':', 1)[1].strip()
            info['layers'].append(current_layer)
        
        # Extract feature count
        elif line.startswith('Feature Count:') and current_layer:
            count_str = line.split(':', 1)[1].strip()
            try:
                info['feature_counts'][current_layer] = int(count_str)
            except ValueError:
                pass
        
        # Extract extent
        elif line.startswith('Extent:'):
            extent_pattern = r'Extent: \(([0-9.-]+), ([0-9.-]+)\) - \(([0-9.-]+), ([0-9.-]+)\)'
            match = re.search(extent_pattern, line)
            if match:
                x_min, y_min, x_max, y_max = map(float, match.groups())
                info['extent'] = {
                    'x_min': x_min, 'y_min': y_min,
                    'x_max': x_max, 'y_max': y_max,
                    'x_range': x_max - x_min,
                    'y_range': y_max - y_min
                }
        
        # Extract coordinate system info
        elif 'PROJCS' in line or 'GEOGCS' in line:
            info['coordinate_system'] = line
    
    return info

# Perform ogrinfo analysis on DXF files
ogrinfo_results = {}

if ogrinfo_available:
    print("\n=== OGRINFO ADVANCED ANALYSIS ===")
    
    for file_path in cad_files:
        if file_path.suffix.lower() == '.dxf':  # ogrinfo works best with DXF
            print(f"\n📊 Analyzing {file_path.name} with ogrinfo:")
            
            # Run ogrinfo command
            result = !ogrinfo -so "{file_path}"
            
            if result:
                output_text = '\n'.join(result)
                parsed_info = parse_ogrinfo_output(output_text)
                ogrinfo_results[file_path.name] = parsed_info
                
                # Display key information
                print(f"  Layers found: {len(parsed_info['layers'])}")
                if parsed_info['extent']:
                    ext = parsed_info['extent']
                    print(f"  Spatial extent:")
                    print(f"    X: {ext['x_min']:.1f} to {ext['x_max']:.1f} (range: {ext['x_range']:.1f})")
                    print(f"    Y: {ext['y_min']:.1f} to {ext['y_max']:.1f} (range: {ext['y_range']:.1f})")
                
                if parsed_info['feature_counts']:
                    print(f"  Feature counts by layer:")
                    for layer, count in sorted(parsed_info['feature_counts'].items(), key=lambda x: x[1], reverse=True)[:5]:
                        print(f"    {layer}: {count} features")
                
                if parsed_info['coordinate_system']:
                    print(f"  Coordinate system detected: {parsed_info['coordinate_system'][:100]}...")
            else:
                print(f"  ❌ ogrinfo failed for {file_path.name}")
        else:
            print(f"  ⏭️ Skipping {file_path.name} (DWG files need conversion to DXF for ogrinfo)")
else:
    print("\n⚠️ Skipping ogrinfo analysis - tool not available")

def detect_coordinate_system_type(x_coords, y_coords):
    """Detect likely coordinate system type from coordinate ranges."""
    x_min, x_max = np.min(x_coords), np.max(x_coords)
    y_min, y_max = np.min(y_coords), np.max(y_coords)
    
    # Remove obvious invalid coordinates for analysis
    valid_mask = ~((x_coords == 0) & (y_coords == 0))
    if np.sum(valid_mask) > 0:
        x_clean = x_coords[valid_mask]
        y_clean = y_coords[valid_mask]
        x_min, x_max = np.min(x_clean), np.max(x_clean)
        y_min, y_max = np.min(y_clean), np.max(y_clean)
    
    # UTM coordinates (6-7 digits, specific ranges)
    if (100000 <= x_min < 1000000 and 1000000 <= y_min < 10000000):
        return "UTM"
    
    # Geographic coordinates (lat/lon)
    elif (abs(x_min) <= 180 and abs(x_max) <= 180 and abs(y_min) <= 90 and abs(y_max) <= 90):
        return "Geographic"
    
    # State Plane or other projected (large numbers)
    elif x_min > 1000000 or y_min > 1000000:
        return "State_Plane_or_Projected"
    
    # Local/Project coordinates
    else:
        return "Local_Project"

def generate_site_configuration(file_analyses, ogrinfo_results, site_name):
    """Generate comprehensive site configuration from all analyses."""
    print(f"\n=== GENERATING SITE CONFIGURATION ===")
    
    # Combine information from multiple sources
    all_extents = []
    coordinate_systems = []
    total_entities = 0
    
    # From ogrinfo results
    for filename, ogrinfo in ogrinfo_results.items():
        if ogrinfo.get('extent'):
            all_extents.append(ogrinfo['extent'])
        if ogrinfo.get('coordinate_system'):
            if 'UTM' in ogrinfo['coordinate_system']:
                coordinate_systems.append('UTM')
            elif 'GEOGCS' in ogrinfo['coordinate_system']:
                coordinate_systems.append('Geographic')
    
    # From file analyses
    for filename, analysis in file_analyses.items():
        if analysis:
            total_entities += analysis.get('total_entities', 0)
    
    # Determine overall bounds
    if all_extents:
        overall_extent = {
            'x_min': min(ext['x_min'] for ext in all_extents),
            'x_max': max(ext['x_max'] for ext in all_extents),
            'y_min': min(ext['y_min'] for ext in all_extents),
            'y_max': max(ext['y_max'] for ext in all_extents)
        }
        
        # Add safety buffer (20% of range)
        x_range = overall_extent['x_max'] - overall_extent['x_min']
        y_range = overall_extent['y_max'] - overall_extent['y_min']
        
        validation_bounds = {
            'x_min': overall_extent['x_min'] - x_range * 0.2,
            'x_max': overall_extent['x_max'] + x_range * 0.2,
            'y_min': overall_extent['y_min'] - y_range * 0.2,
            'y_max': overall_extent['y_max'] + y_range * 0.2
        }
    else:
        print("⚠️ No spatial extent information available")
        overall_extent = None
        validation_bounds = None
    
    # Determine coordinate system
    if coordinate_systems:
        coord_system = max(set(coordinate_systems), key=coordinate_systems.count)
    else:
        coord_system = "Unknown"
    
    # Generate configuration
    config = {
        'site_name': site_name,
        'generation_timestamp': datetime.now().isoformat(),
        'analysis_source': {
            'ogrinfo_available': len(ogrinfo_results) > 0,
            'files_analyzed': list(file_analyses.keys()),
            'total_entities_discovered': total_entities
        },
        'coordinate_system': {
            'detected_type': coord_system,
            'confidence': 'high' if len(coordinate_systems) > 0 else 'low',
            'epsg_suggestion': 'EPSG:32633' if coord_system == 'UTM' else 'auto'
        },
        'spatial_bounds': {
            'raw_extent': overall_extent,
            'validation_bounds': validation_bounds,
            'buffer_percentage': 20.0
        } if overall_extent else None,
        'validation_rules': {
            'skip_zero_coordinates': True,
            'use_bounds_validation': validation_bounds is not None,
            'use_statistical_outlier_detection': True,
            'outlier_threshold_iqr': 2.5,
            'minimum_valid_coordinates_required': 5
        },
        'ogrinfo_summary': ogrinfo_results,
        'file_analysis_summary': file_analyses
    }
    
    # Display configuration summary
    print(f"✅ Configuration generated for site: {site_name}")
    print(f"📍 Coordinate system: {coord_system} (confidence: {config['coordinate_system']['confidence']})")
    
    if validation_bounds:
        print(f"📐 Validation bounds:")
        print(f"   X: {validation_bounds['x_min']:.1f} to {validation_bounds['x_max']:.1f}")
        print(f"   Y: {validation_bounds['y_min']:.1f} to {validation_bounds['y_max']:.1f}")
    else:
        print(f"⚠️ No spatial bounds available - will use statistical validation only")
    
    print(f"📊 Total entities discovered: {total_entities}")
    
    return config

# Generate site configuration
site_config = generate_site_configuration(file_analyses, ogrinfo_results, site_name)

def create_adaptive_validator(site_config):
    """Create coordinate validation function based on site configuration."""
    
    if not site_config:
        print("⚠️ No site configuration available - using basic validation only")
        def basic_validator(entity_data):
            x, y = entity_data.get('x_coord', 0), entity_data.get('y_coord', 0)
            return not (x == 0.0 and y == 0.0)  # Just skip zero coordinates
        return basic_validator
    
    validation_rules = site_config.get('validation_rules', {})
    spatial_bounds = site_config.get('spatial_bounds')
    
    print(f"\n=== CREATING ADAPTIVE VALIDATOR ===")
    print(f"Site: {site_config.get('site_name', 'unknown')}")
    print(f"Coordinate system: {site_config.get('coordinate_system', {}).get('detected_type', 'unknown')}")
    
    # Validation components
    skip_zeros = validation_rules.get('skip_zero_coordinates', True)
    use_bounds = validation_rules.get('use_bounds_validation', False) and spatial_bounds
    use_statistical = validation_rules.get('use_statistical_outlier_detection', True)
    
    print(f"Validation methods:")
    print(f"  ✓ Skip zero coordinates: {skip_zeros}")
    print(f"  ✓ Bounds validation: {use_bounds}")
    print(f"  ✓ Statistical outlier detection: {use_statistical}")
    
    if use_bounds:
        bounds = spatial_bounds['validation_bounds']
        print(f"  📐 Validation bounds: X({bounds['x_min']:.1f}, {bounds['x_max']:.1f}), Y({bounds['y_min']:.1f}, {bounds['y_max']:.1f})")
    
    def adaptive_validator(entity_data):
        x, y = entity_data.get('x_coord', 0), entity_data.get('y_coord', 0)
        
        # Skip zero coordinates
        if skip_zeros and x == 0.0 and y == 0.0:
            return False
        
        # Bounds validation
        if use_bounds:
            bounds = spatial_bounds['validation_bounds']
            if not (bounds['x_min'] <= x <= bounds['x_max'] and bounds['y_min'] <= y <= bounds['y_max']):
                return False
        
        # Additional validation can be added here (statistical outlier detection)
        # For now, if it passes the above tests, it's valid
        return True
    
    return adaptive_validator

# Create validator based on site configuration
coordinate_validator = create_adaptive_validator(site_config)

# Test validator with sample coordinates if available
if 'file_analyses' in locals() and file_analyses:
    print(f"\n=== TESTING COORDINATE VALIDATOR ===")
    test_coords = [
        {'x_coord': 0.0, 'y_coord': 0.0},  # Should be rejected
        {'x_coord': 707500.0, 'y_coord': 4693000.0},  # Should be accepted (typical UTM)
        {'x_coord': 999999.0, 'y_coord': 999999.0},  # May be rejected if bounds validation active
    ]
    
    for i, test_coord in enumerate(test_coords, 1):
        result = coordinate_validator(test_coord)
        status = "✅ VALID" if result else "❌ INVALID"
        print(f"  Test {i}: ({test_coord['x_coord']}, {test_coord['y_coord']}) → {status}")

class GenericSolarClassifier:
    """Generic classifier for solar infrastructure across different sites."""
    
    def __init__(self):
        # Generic classification rules for any solar project site
        self.classification_rules = {
            # Tracker systems (primary alignment references)
            'tracker': {
                'layer_patterns': ['TRACKER', 'CVT', 'TRJ', 'SOLAR_TRACKER'],
                'entity_types': ['INSERT', 'BLOCK'],
                'keywords': ['tracker', 'cvt', 'trj', '1p52', '1p26', 'solar', 'track'],
                'block_patterns': ['1P', '1X', 'TRACKER', 'SOLAR'],
                'alignment_value': 'high'  # Primary reference points
            },
            
            # Module layouts (secondary alignment references)
            'module': {
                'layer_patterns': ['PVCASE', 'PV', 'MODULE', 'PANEL', 'SOLAR'],
                'entity_types': ['LWPOLYLINE', 'POLYLINE', 'HATCH'],
                'keywords': ['pvcase', 'pv', 'module', 'panel', 'solar', 'photovoltaic'],
                'block_patterns': ['PV', 'MODULE', 'PANEL'],
                'alignment_value': 'medium'  # Area boundaries
            },
            
            # DC electrical (infrastructure context)
            'electrical': {
                'layer_patterns': ['DC', 'ELECTRICAL', 'CABLE', 'POWER', 'WIRE'],
                'entity_types': ['POLYLINE', 'LWPOLYLINE', 'LINE'],
                'keywords': ['dc', 'electrical', 'cable', 'power', 'wire', 'voltage'],
                'block_patterns': ['ELECTRICAL', 'POWER'],
                'alignment_value': 'low'  # Context only
            },
            
            # Site infrastructure (context)
            'infrastructure': {
                'layer_patterns': ['ROAD', 'BUILDING', 'BOUNDARY', 'FENCE', 'ACCESS'],
                'entity_types': ['POLYLINE', 'LWPOLYLINE', 'HATCH', 'LINE'],
                'keywords': ['road', 'building', 'boundary', 'fence', 'access', 'site'],
                'block_patterns': ['BUILDING', 'STRUCTURE'],
                'alignment_value': 'low'  # Context only
            }
        }
        
        # Dynamic layer discovery for site-specific adaptation
        self.discovered_layers = set()
        self.site_specific_rules = {}
    
    def adapt_to_site(self, file_analyses):
        """Adapt classification rules based on discovered layers."""
        print("\n=== ADAPTING CLASSIFICATION TO SITE ===")
        
        # Collect all discovered layers
        for analysis in file_analyses.values():
            self.discovered_layers.update(analysis.get('all_layers', {}).keys())
        
        print(f"Total discovered layers: {len(self.discovered_layers)}")
        
        # Auto-enhance rules based on discovered layers
        for layer in self.discovered_layers:
            layer_upper = layer.upper()
            
            # Add to appropriate categories based on patterns
            for classification, rules in self.classification_rules.items():
                if any(pattern in layer_upper for pattern in rules['layer_patterns']):
                    if layer not in self.site_specific_rules:
                        self.site_specific_rules[layer] = classification
        
        print(f"Site-specific mappings created: {len(self.site_specific_rules)}")
        
        # Show mappings by category
        for classification in self.classification_rules.keys():
            mapped_layers = [layer for layer, cls in self.site_specific_rules.items() if cls == classification]
            if mapped_layers:
                print(f"  {classification}: {len(mapped_layers)} layers")
                for layer in mapped_layers[:3]:  # Show first 3
                    print(f"    - {layer}")
                if len(mapped_layers) > 3:
                    print(f"    ... and {len(mapped_layers) - 3} more")
    
    def classify_entity(self, entity_data):
        """Classify entity using generic and site-specific rules."""
        layer_name = entity_data.get('layer_name', '')
        entity_type = entity_data.get('entity_type', '')
        block_name = str(entity_data.get('block_name', '')).lower()
        
        # Check site-specific rules first (exact layer match)
        if layer_name in self.site_specific_rules:
            return self.site_specific_rules[layer_name]
        
        # Fall back to generic pattern matching
        layer_upper = layer_name.upper()
        
        for classification, rules in self.classification_rules.items():
            # Check layer patterns
            if any(pattern in layer_upper for pattern in rules['layer_patterns']):
                if entity_type in rules['entity_types']:
                    return classification
            
            # Check block name patterns
            if entity_type in rules['entity_types']:
                if any(pattern.lower() in block_name for pattern in rules['block_patterns']):
                    return classification
        
        return 'other'
    
    def get_alignment_value(self, classification):
        """Get alignment value for a classification."""
        if classification in self.classification_rules:
            return self.classification_rules[classification]['alignment_value']
        return 'none'

# Initialize classifier
classifier = GenericSolarClassifier()

# Adapt to discovered site layers
if 'file_analyses' in locals() and file_analyses:
    classifier.adapt_to_site(file_analyses)

print(f"\nGeneric Solar Classifier initialized and adapted")
print(f"Focus classifications: {list(classifier.classification_rules.keys())}")

def extract_alignment_entity(entity):
    """Extract entity data focused on alignment needs."""
    try:
        entity_type = entity.dxftype()
        layer_name = getattr(entity.dxf, 'layer', 'unknown')
        
        # Base entity data
        entity_data = {
            'entity_id': str(entity.dxf.handle),
            'entity_type': entity_type,
            'layer_name': layer_name
        }
        
        # Extract coordinates based on entity type
        if hasattr(entity.dxf, 'insert'):
            # INSERT entities (trackers) - PRIMARY ALIGNMENT POINTS
            insert_point = entity.dxf.insert
            entity_data.update({
                'x_coord': insert_point.x,
                'y_coord': insert_point.y,
                'z_coord': insert_point.z,
                'block_name': getattr(entity.dxf, 'name', ''),
                'rotation': getattr(entity.dxf, 'rotation', 0.0),
                'geometry_type': 'point'
            })
            
        elif hasattr(entity.dxf, 'start') and hasattr(entity.dxf, 'end'):
            # LINE entities
            start_point = entity.dxf.start
            end_point = entity.dxf.end
            entity_data.update({
                'x_coord': (start_point.x + end_point.x) / 2,
                'y_coord': (start_point.y + end_point.y) / 2,
                'z_coord': (start_point.z + end_point.z) / 2,
                'start_x': start_point.x,
                'start_y': start_point.y,
                'end_x': end_point.x,
                'end_y': end_point.y,
                'length': start_point.distance(end_point),
                'geometry_type': 'line'
            })
            
        elif entity_type in ['POLYLINE', 'LWPOLYLINE']:
            # POLYLINE entities (module areas) - AREA BOUNDARIES
            try:
                # Get bounding box for polylines
                bbox = entity.bbox()
                if bbox:
                    entity_data.update({
                        'x_coord': (bbox[0].x + bbox[1].x) / 2,
                        'y_coord': (bbox[0].y + bbox[1].y) / 2,
                        'z_coord': (bbox[0].z + bbox[1].z) / 2,
                        'x_min': bbox[0].x,
                        'y_min': bbox[0].y,
                        'x_max': bbox[1].x,
                        'y_max': bbox[1].y,
                        'geometry_type': 'area'
                    })
                else:
                    entity_data.update({'x_coord': 0.0, 'y_coord': 0.0, 'z_coord': 0.0, 'geometry_type': 'area'})
            except:
                entity_data.update({'x_coord': 0.0, 'y_coord': 0.0, 'z_coord': 0.0, 'geometry_type': 'area'})
                
        else:
            # Skip entities without useful coordinates for alignment
            return None
        
        # Classification and alignment value
        classification = classifier.classify_entity(entity_data)
        entity_data['classification'] = classification
        entity_data['alignment_value'] = classifier.get_alignment_value(classification)
        entity_data['extraction_timestamp'] = datetime.now().isoformat()
        
        # Only return entities useful for alignment
        if focus_on_alignment and entity_data['alignment_value'] == 'none':
            return None
            
        return entity_data
        
    except Exception as e:
        logger.warning(f"Error extracting entity data: {e}")
        return None

print("Alignment-focused entity extraction function defined")

def extract_alignment_entity_with_validation(entity):
    """Enhanced extraction with adaptive coordinate validation."""
    # First extract entity data
    entity_data = extract_alignment_entity(entity)
    
    if not entity_data:
        return None
    
    # Apply coordinate validation
    if not coordinate_validator(entity_data):
        return None  # Filter out invalid coordinates
    
    # Add validation metadata
    entity_data['coordinate_validated'] = True
    entity_data['validation_timestamp'] = datetime.now().isoformat()
    
    return entity_data

print("Enhanced entity extraction with adaptive validation ready")
print(f"Validation method: {'Adaptive (bounds + rules)' if site_config else 'Basic (zero-coordinate filtering)'}")

# Process CAD files
if not cad_files:
    print("No CAD files to process")
    alignment_entities_df = pd.DataFrame()
else:
    print(f"\n=== PROCESSING CAD FILES FOR ALIGNMENT DATA ===")
    
    all_alignment_entities = []
    overall_stats = defaultdict(int)
    file_stats = {}
    
    for file_path in cad_files:
        print(f"\nProcessing: {file_path.name}")
        
        try:
            doc = ezdxf.readfile(file_path)
            msp = doc.modelspace()
            
            file_entities = []
            file_processing_stats = defaultdict(int)
            
            for entity in msp:
                file_processing_stats['total_entities'] += 1
                overall_stats['total_entities'] += 1
                
                entity_data = extract_alignment_entity_with_validation(entity)
                if entity_data:
                    entity_data['source_file'] = file_path.name
                    file_entities.append(entity_data)
                    all_alignment_entities.append(entity_data)
                    
                    file_processing_stats['extracted_entities'] += 1
                    overall_stats['extracted_entities'] += 1
                    
                    classification = entity_data['classification']
                    alignment_value = entity_data['alignment_value']
                    
                    file_processing_stats[f"classification_{classification}"] += 1
                    file_processing_stats[f"alignment_{alignment_value}"] += 1
                    overall_stats[f"classification_{classification}"] += 1
                    overall_stats[f"alignment_{alignment_value}"] += 1
                else:
                    file_processing_stats['skipped_entities'] += 1
                    overall_stats['skipped_entities'] += 1
            
            file_stats[file_path.name] = dict(file_processing_stats)
            
            print(f"  Total entities: {file_processing_stats['total_entities']}")
            print(f"  Extracted for alignment: {file_processing_stats['extracted_entities']}")
            print(f"  Skipped (not alignment-relevant): {file_processing_stats['skipped_entities']}")
            
            # Show classification breakdown for this file
            classifications = [k.replace('classification_', '') for k in file_processing_stats.keys() if k.startswith('classification_')]
            if classifications:
                print("  Classifications: " + ', '.join([f"{c}({file_processing_stats[f'classification_{c}']})" for c in classifications]))

        except Exception as e:
            logger.error(f"Error processing {file_path.name}: {e}")
            continue
    
    print(f"\n=== OVERALL PROCESSING SUMMARY ===")
    print(f"Files processed: {len([f for f in file_stats.keys()])}")
    print(f"Total entities: {overall_stats['total_entities']}")
    print(f"Extracted for alignment: {overall_stats['extracted_entities']}")
    print(f"Extraction rate: {(overall_stats['extracted_entities'] / overall_stats['total_entities'] * 100):.1f}%")
    
    # Convert to DataFrame
    if all_alignment_entities:
        alignment_entities_df = pd.DataFrame(all_alignment_entities)
        print(f"\nAlignment DataFrame: {len(alignment_entities_df)} entities")
        
        # Show alignment value distribution
        alignment_dist = alignment_entities_df['alignment_value'].value_counts()
        print(f"\nAlignment Value Distribution:")
        for value, count in alignment_dist.items():
            print(f"  {value}: {count} entities")
        
        # Show classification distribution
        class_dist = alignment_entities_df['classification'].value_counts()
        print(f"\nClassification Distribution:")
        for classification, count in class_dist.items():
            print(f"  {classification}: {count} entities")
    else:
        print("No alignment-relevant entities extracted")
        alignment_entities_df = pd.DataFrame()

if not alignment_entities_df.empty:
    # Create output directory
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_path = Path(output_dir)
    run_output_dir = output_path / f"{project_type}_alignment_{timestamp}"
    run_output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"\n=== GENERATING MULTI-SITE COMPATIBLE OUTPUTS ===")
    print(f"Output directory: {run_output_dir}")
    
    # 1. High-value alignment points (trackers)
    high_value = alignment_entities_df[alignment_entities_df['alignment_value'] == 'high']
    if not high_value.empty:
        tracker_file = run_output_dir / f"tracker_alignment_points_{timestamp}.csv"
        high_value.to_csv(tracker_file, index=False)
        print(f"Tracker alignment points: {tracker_file.name} ({len(high_value)} points)")
        
        # Create simplified coordinate file for alignment algorithms
        coords_file = run_output_dir / f"tracker_coordinates_{timestamp}.csv"
        coords_df = high_value[['x_coord', 'y_coord', 'z_coord', 'block_name', 'rotation', 'source_file']].copy()
        coords_df.to_csv(coords_file, index=False)
        print(f"Tracker coordinates: {coords_file.name} ({len(coords_df)} coordinates)")
    
    # 2. Module area boundaries
    medium_value = alignment_entities_df[alignment_entities_df['alignment_value'] == 'medium']
    if not medium_value.empty:
        module_file = run_output_dir / f"module_boundaries_{timestamp}.csv"
        medium_value.to_csv(module_file, index=False)
        print(f"Module boundaries: {module_file.name} ({len(medium_value)} areas)")
    
    # 3. Complete alignment dataset
    complete_file = run_output_dir / f"solar_alignment_data_{timestamp}.csv"
    alignment_entities_df.to_csv(complete_file, index=False)
    print(f"Complete alignment data: {complete_file.name} ({len(alignment_entities_df)} entities)")
    
    # 4. Site-specific classification mapping (for reuse)
    classification_mapping = {
        'site_specific_rules': classifier.site_specific_rules,
        'discovered_layers': list(classifier.discovered_layers),
        'classification_stats': alignment_entities_df['classification'].value_counts().to_dict(),
        'files_processed': list(file_stats.keys()) if 'file_stats' in locals() else []
    }
    
    mapping_file = run_output_dir / f"site_classification_mapping_{timestamp}.json"
    with open(mapping_file, 'w') as f:
        json.dump(classification_mapping, f, indent=2)
    print(f"Classification mapping: {mapping_file.name} (for reuse on similar sites)")
    
    # 5. Comprehensive site configuration (enhanced)
    if site_config:
        # Update config with processing results
        site_config['processing_results'] = {
            'total_entities_processed': len(alignment_entities_df),
            'high_value_points': len(high_value) if not high_value.empty else 0,
            'medium_value_areas': len(medium_value) if not medium_value.empty else 0,
            'coordinate_validation_applied': True,
            'validation_success_rate': 'calculated_during_processing',
            'files_successfully_processed': list(file_stats.keys()) if 'file_stats' in locals() else []
        }
        
        comprehensive_config_file = run_output_dir / f"comprehensive_site_config_{timestamp}.json"
        with open(comprehensive_config_file, 'w') as f:
            json.dump(site_config, f, indent=2, default=str)
        print(f"Comprehensive site config: {comprehensive_config_file.name} (full reusable configuration)")
    
    # 6. Alignment summary for workflows
    alignment_summary = {
        'extraction_metadata': {
            'timestamp': timestamp,
            'project': project_type,
            'target_files': target_files,
            'files_processed': list(file_stats.keys()) if 'file_stats' in locals() else [],
            'coordinate_system': coordinate_system,
            'focus': 'alignment_ready_data',
            'multi_site_compatible': True
        },
        'processing_statistics': dict(overall_stats) if 'overall_stats' in locals() else {},
        'file_statistics': file_stats if 'file_stats' in locals() else {},
        'alignment_statistics': {
            'total_entities': len(alignment_entities_df),
            'high_value_points': len(high_value) if not high_value.empty else 0,
            'medium_value_areas': len(medium_value) if not medium_value.empty else 0,
            'coordinate_range': {
                'x_min': float(alignment_entities_df['x_coord'].min()),
                'x_max': float(alignment_entities_df['x_coord'].max()),
                'y_min': float(alignment_entities_df['y_coord'].min()),
                'y_max': float(alignment_entities_df['y_coord'].max())
            }
        },
        'classification_summary': alignment_entities_df['classification'].value_counts().to_dict(),
        'alignment_value_summary': alignment_entities_df['alignment_value'].value_counts().to_dict(),
        'recommended_usage': {
            'primary_alignment': 'Use tracker_coordinates.csv for initial point cloud registration',
            'area_validation': 'Use module_boundaries.csv for spatial extent validation',
            'complete_context': 'Use solar_alignment_data.csv for full spatial context',
            'site_adaptation': 'Use site_classification_mapping.json for similar sites'
        }
    }
    
    summary_file = run_output_dir / f"alignment_summary_{timestamp}.json"
    with open(summary_file, 'w') as f:
        json.dump(alignment_summary, f, indent=2, default=str)
    print(f"Alignment summary: {summary_file.name}")
    
    print(f"\n✅ Multi-site compatible alignment data extraction completed")
    print(f"📁 Files ready for alignment workflows: {run_output_dir}")
    
    print(f"\n🎯 Recommended Usage:")
    print(f"  1. Use tracker coordinates for initial point cloud registration")
    print(f"  2. Use module boundaries for spatial extent validation")
    print(f"  3. Apply coordinate transformations if needed (EPSG:32633)")
    print(f"  4. Reuse classification mapping for similar solar project sites")
    
else:
    print("No alignment data to export")

print(f"\n📅 Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Final summary of enhancements
print(f"\n" + "=" * 60)
print(f"ENHANCED SOLAR CAD EXTRACTION SUMMARY")
print(f"=" * 60)
print(f"\n🔧 Enhancements Applied:")
print(f"  ✅ ogrinfo integration for advanced CAD analysis")
print(f"  ✅ Automated site configuration generation")
print(f"  ✅ Adaptive coordinate validation (no hard-coding)")
print(f"  ✅ Multi-site compatible classification rules")
print(f"  ✅ Comprehensive configuration export for reuse")

if site_config:
    coord_system = site_config.get('coordinate_system', {}).get('detected_type', 'unknown')
    bounds_available = site_config.get('spatial_bounds') is not None
    print(f"\n📊 Site Analysis Results:")
    print(f"  Coordinate system detected: {coord_system}")
    print(f"  Spatial bounds available: {'Yes' if bounds_available else 'No'}")
    print(f"  Validation method: {'Bounds + Statistical' if bounds_available else 'Statistical only'}")
    
print(f"\n🚀 Ready for multi-site deployment!")
print(f"=" * 60)