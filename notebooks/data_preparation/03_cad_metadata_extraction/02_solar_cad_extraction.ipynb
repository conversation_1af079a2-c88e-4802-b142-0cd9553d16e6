# Simple parameters
target_files = ["GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf", "GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg"]
search_path = "../../../data/raw"
output_dir = "../../../output_runs/cad_metadata"

import pandas as pd
import numpy as np
from pathlib import Path
import ezdxf
import json
from datetime import datetime
from collections import defaultdict

print(f"Simple Solar CAD Extraction")
print(f"Target files: {target_files}")
print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

print("=== FINDING CAD FILES ===")

# Use simple find commands
print("\nSearching for target files...")
cad_files = []

for target_file in target_files:
    print(f"\nLooking for: {target_file}")
    result = !find {search_path} -name "{target_file}" 2>/dev/null
    
    if result:
        file_path = Path(result[0])
        size_mb = file_path.stat().st_size / 1024 / 1024
        print(f"  ✅ Found: {file_path} ({size_mb:.1f} MB)")
        cad_files.append(file_path)
    else:
        print(f"  ❌ Not found: {target_file}")

print(f"\nFiles to process: {len(cad_files)}")

print("\n=== QUICK CAD ANALYSIS ===")

# Check if ogrinfo is available
ogrinfo_check = !which ogrinfo
if ogrinfo_check:
    print("✅ ogrinfo available")
    
    # Analyze DXF files only (ogrinfo works best with DXF)
    for file_path in cad_files:
        if file_path.suffix.lower() == '.dxf':
            print(f"\n📊 Analyzing {file_path.name}:")
            !ogrinfo -so "{file_path}"
        else:
            print(f"\n⏭️ Skipping {file_path.name} (DWG - use DXF for ogrinfo)")
else:
    print("⚠️ ogrinfo not available - install with: brew install gdal")

def extract_coordinates_simple(file_path):
    """Simple coordinate extraction focused on trackers and modules."""
    try:
        doc = ezdxf.readfile(file_path)
        msp = doc.modelspace()
        
        trackers = []  # INSERT entities (high value for alignment)
        modules = []   # POLYLINE entities (medium value for alignment)
        
        for entity in msp:
            layer = getattr(entity.dxf, 'layer', '').upper()
            entity_type = entity.dxftype()
            
            # Extract tracker coordinates (INSERT entities)
            if entity_type == 'INSERT' and any(keyword in layer for keyword in ['TRACKER', 'CVT']):
                if hasattr(entity.dxf, 'insert'):
                    pt = entity.dxf.insert
                    # Skip zero coordinates
                    if not (pt.x == 0.0 and pt.y == 0.0):
                        trackers.append({
                            'x': pt.x, 'y': pt.y, 'z': pt.z,
                            'layer': layer,
                            'block_name': getattr(entity.dxf, 'name', ''),
                            'type': 'tracker'
                        })
            
            # Extract module boundaries (POLYLINE entities)
            elif entity_type in ['LWPOLYLINE', 'POLYLINE'] and any(keyword in layer for keyword in ['PVCASE', 'PV', 'MODULE']):
                try:
                    bbox = entity.bbox()
                    if bbox:
                        center_x = (bbox[0].x + bbox[1].x) / 2
                        center_y = (bbox[0].y + bbox[1].y) / 2
                        # Skip zero coordinates
                        if not (center_x == 0.0 and center_y == 0.0):
                            modules.append({
                                'x': center_x, 'y': center_y, 'z': (bbox[0].z + bbox[1].z) / 2,
                                'layer': layer,
                                'x_min': bbox[0].x, 'y_min': bbox[0].y,
                                'x_max': bbox[1].x, 'y_max': bbox[1].y,
                                'type': 'module'
                            })
                except:
                    pass
        
        return trackers, modules
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return [], []

print("Simple coordinate extraction function ready")

print("\n=== PROCESSING CAD FILES ===")

all_trackers = []
all_modules = []

for file_path in cad_files:
    if file_path.suffix.lower() == '.dxf':  # Process DXF files only
        print(f"\nProcessing: {file_path.name}")
        
        trackers, modules = extract_coordinates_simple(file_path)
        
        # Add source file info
        for tracker in trackers:
            tracker['source_file'] = file_path.name
        for module in modules:
            module['source_file'] = file_path.name
        
        all_trackers.extend(trackers)
        all_modules.extend(modules)
        
        print(f"  Trackers extracted: {len(trackers)}")
        print(f"  Modules extracted: {len(modules)}")
    else:
        print(f"\nSkipping: {file_path.name} (DWG format - convert to DXF first)")

print(f"\n=== EXTRACTION SUMMARY ===")
print(f"Total trackers: {len(all_trackers)}")
print(f"Total modules: {len(all_modules)}")

# Quick coordinate analysis
if all_trackers:
    tracker_coords = np.array([[t['x'], t['y']] for t in all_trackers])
    print(f"\nTracker coordinate ranges:")
    print(f"  X: {tracker_coords[:, 0].min():.1f} to {tracker_coords[:, 0].max():.1f}")
    print(f"  Y: {tracker_coords[:, 1].min():.1f} to {tracker_coords[:, 1].max():.1f}")

if all_modules:
    module_coords = np.array([[m['x'], m['y']] for m in all_modules])
    print(f"\nModule coordinate ranges:")
    print(f"  X: {module_coords[:, 0].min():.1f} to {module_coords[:, 0].max():.1f}")
    print(f"  Y: {module_coords[:, 1].min():.1f} to {module_coords[:, 1].max():.1f}")

if all_trackers or all_modules:
    # Create output directory
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_path = Path(output_dir)
    run_output_dir = output_path / f"simple_solar_extraction_{timestamp}"
    run_output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"\n=== SAVING OUTPUTS ===")
    print(f"Output directory: {run_output_dir}")
    
    # Save tracker coordinates (primary alignment points)
    if all_trackers:
        trackers_df = pd.DataFrame(all_trackers)
        tracker_file = run_output_dir / f"tracker_coordinates_{timestamp}.csv"
        trackers_df.to_csv(tracker_file, index=False)
        print(f"✅ Tracker coordinates: {tracker_file.name} ({len(all_trackers)} points)")
    
    # Save module boundaries (secondary alignment)
    if all_modules:
        modules_df = pd.DataFrame(all_modules)
        module_file = run_output_dir / f"module_boundaries_{timestamp}.csv"
        modules_df.to_csv(module_file, index=False)
        print(f"✅ Module boundaries: {module_file.name} ({len(all_modules)} areas)")
    
    # Save simple summary
    summary = {
        'extraction_timestamp': timestamp,
        'target_files': target_files,
        'files_processed': [f.name for f in cad_files if f.suffix.lower() == '.dxf'],
        'results': {
            'tracker_count': len(all_trackers),
            'module_count': len(all_modules)
        },
        'coordinate_ranges': {
            'tracker_x_range': [float(tracker_coords[:, 0].min()), float(tracker_coords[:, 0].max())] if all_trackers else None,
            'tracker_y_range': [float(tracker_coords[:, 1].min()), float(tracker_coords[:, 1].max())] if all_trackers else None,
            'module_x_range': [float(module_coords[:, 0].min()), float(module_coords[:, 0].max())] if all_modules else None,
            'module_y_range': [float(module_coords[:, 1].min()), float(module_coords[:, 1].max())] if all_modules else None
        },
        'usage': {
            'primary_alignment': 'Use tracker_coordinates.csv for point cloud registration',
            'area_validation': 'Use module_boundaries.csv for spatial validation'
        }
    }
    
    summary_file = run_output_dir / f"extraction_summary_{timestamp}.json"
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    print(f"✅ Summary: {summary_file.name}")
    
    print(f"\n🎯 Ready for alignment workflows!")
    print(f"📁 Output directory: {run_output_dir}")
    
else:
    print("\n❌ No data extracted - check file paths and formats")

print(f"\n📅 Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")