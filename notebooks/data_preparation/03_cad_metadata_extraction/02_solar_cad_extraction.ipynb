# Papermill parameters
site_name = "Castro"
project_type = "ENEL"
target_files = ["GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf"]
search_path = "../../../data/raw"
output_dir = "../../../output_runs/cad_metadata"

import pandas as pd
import numpy as np
from pathlib import Path
import ezdxf
import json
import logging
from datetime import datetime
from collections import defaultdict

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

logger.info("Solar CAD extraction started")
logger.info(f"Site: {site_name}, Project: {project_type}")
logger.info(f"Target files: {target_files}")
logger.info(f"Search path: {search_path}")
logger.info(f"Output directory: {output_dir}")

def load_coordinate_config(config_dir="../../../output_runs/coordinate_analysis"):
    """Load the most recent coordinate system configuration."""
    config_path = Path(config_dir)
    
    if not config_path.exists():
        logger.warning(f"Configuration directory not found: {config_path}")
        logger.warning("Run 01_coordinate_system_analysis.ipynb first")
        return None
    
    config_files = list(config_path.glob("*/site_coordinate_config_*.json"))
    
    if not config_files:
        logger.warning(f"No coordinate configuration found in {config_path}")
        logger.warning("Run 01_coordinate_system_analysis.ipynb first")
        return None
    
    latest_config = max(config_files, key=lambda x: x.stat().st_mtime)
    
    try:
        with open(latest_config, 'r') as f:
            config = json.load(f)
        
        logger.info(f"Loaded coordinate configuration: {latest_config.name}")
        logger.info(f"Coordinate system: {config['coordinate_system']['type']}")
        logger.info(f"Confidence: {config['coordinate_system']['confidence']}")
        
        return config
    
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return None

# Load configuration
coord_config = load_coordinate_config()

if coord_config and coord_config.get('valid_ranges'):
    valid_ranges = coord_config['valid_ranges']
    logger.info(f"Valid X range: {valid_ranges['x_min']:.2f} to {valid_ranges['x_max']:.2f}")
    logger.info(f"Valid Y range: {valid_ranges['y_min']:.2f} to {valid_ranges['y_max']:.2f}")
else:
    valid_ranges = None
    logger.warning("No valid ranges available - using basic validation")

def create_coordinate_validator(valid_ranges):
    """Create coordinate validation function based on detected ranges."""
    
    if not valid_ranges:
        def basic_validator(x, y, z=None):
            return x != 0.0 and y != 0.0 and abs(x) > 100 and abs(y) > 100
        return basic_validator
    
    def adaptive_validator(x, y, z=None):
        """Validate coordinates using site-specific ranges."""
        if x == 0.0 and y == 0.0:
            return False
        if abs(x) < 100 or abs(y) < 100:
            return False
        if not (valid_ranges['x_min'] <= x <= valid_ranges['x_max']):
            return False
        if not (valid_ranges['y_min'] <= y <= valid_ranges['y_max']):
            return False
        return True
    
    return adaptive_validator

# Create validator
is_valid_coordinate = create_coordinate_validator(valid_ranges)
logger.info("Coordinate validator created")

def find_cad_files(search_path, target_files):
    """Find target CAD files in search path."""
    found_files = []
    
    for target_file in target_files:
        result = !find {search_path} -name "{target_file}" 2>/dev/null
        
        if result:
            file_path = Path(result[0])
            file_size = file_path.stat().st_size / 1024 / 1024
            
            logger.info(f"Found: {file_path.name} ({file_size:.1f} MB)")
            found_files.append(file_path)
        else:
            logger.warning(f"Not found: {target_file}")
    
    return found_files

# Find files
logger.info("Searching for CAD files...")
cad_files = find_cad_files(search_path, target_files)
logger.info(f"Files to process: {len(cad_files)}")

if not cad_files:
    logger.error("No CAD files found")
    raise FileNotFoundError("No target CAD files found in search path")

def extract_solar_trackers(entity, layer, is_valid_coordinate):
    """Extract solar tracker coordinates from INSERT entities."""
    trackers = []
    
    if (entity.dxftype() == 'INSERT' and 
        any(keyword in layer.upper() for keyword in ['TRACKER', 'CVT'])):
        
        if hasattr(entity.dxf, 'insert'):
            point = entity.dxf.insert
            
            if is_valid_coordinate(point.x, point.y, point.z):
                tracker_data = {
                    'x': point.x,
                    'y': point.y,
                    'z': point.z,
                    'layer': layer,
                    'block_name': getattr(entity.dxf, 'name', ''),
                    'rotation': getattr(entity.dxf, 'rotation', 0.0),
                    'type': 'tracker'
                }
                trackers.append(tracker_data)
    
    return trackers

def extract_solar_modules(entity, layer, is_valid_coordinate):
    """Extract solar module boundaries from POLYLINE entities."""
    modules = []
    
    if (entity.dxftype() in ['LWPOLYLINE', 'POLYLINE'] and 
        any(keyword in layer.upper() for keyword in ['PVCASE', 'PV', 'MODULE'])):
        
        try:
            bbox = entity.bbox()
            if bbox:
                center_x = (bbox[0].x + bbox[1].x) / 2
                center_y = (bbox[0].y + bbox[1].y) / 2
                center_z = (bbox[0].z + bbox[1].z) / 2
                
                if is_valid_coordinate(center_x, center_y, center_z):
                    module_data = {
                        'x': center_x,
                        'y': center_y,
                        'z': center_z,
                        'layer': layer,
                        'x_min': bbox[0].x,
                        'y_min': bbox[0].y,
                        'x_max': bbox[1].x,
                        'y_max': bbox[1].y,
                        'width': bbox[1].x - bbox[0].x,
                        'height': bbox[1].y - bbox[0].y,
                        'type': 'module'
                    }
                    modules.append(module_data)
        except:
            pass
    
    return modules

logger.info("Solar equipment extraction functions ready")

def process_cad_file(file_path, is_valid_coordinate):
    """Process a single CAD file and extract solar equipment data."""
    logger.info(f"Processing: {file_path.name}")
    
    try:
        doc = ezdxf.readfile(file_path)
        modelspace = doc.modelspace()
        
        all_trackers = []
        all_modules = []
        entity_stats = defaultdict(int)
        
        for entity in modelspace:
            layer = getattr(entity.dxf, 'layer', 'UNKNOWN')
            entity_type = entity.dxftype()
            entity_stats[f"{entity_type}_{layer}"] += 1
            
            # Extract trackers
            trackers = extract_solar_trackers(entity, layer, is_valid_coordinate)
            for tracker in trackers:
                tracker['source_file'] = file_path.name
            all_trackers.extend(trackers)
            
            # Extract modules
            modules = extract_solar_modules(entity, layer, is_valid_coordinate)
            for module in modules:
                module['source_file'] = file_path.name
            all_modules.extend(modules)
        
        logger.info(f"Extracted {len(all_trackers)} trackers and {len(all_modules)} modules")
        
        return all_trackers, all_modules, dict(entity_stats)
        
    except Exception as e:
        logger.error(f"Error processing {file_path.name}: {e}")
        return [], [], {}

# Process all CAD files
logger.info("Starting CAD file processing...")

all_trackers = []
all_modules = []
processing_stats = {}

for file_path in cad_files:
    if file_path.suffix.lower() == '.dxf':
        trackers, modules, entity_stats = process_cad_file(file_path, is_valid_coordinate)
        
        all_trackers.extend(trackers)
        all_modules.extend(modules)
        
        processing_stats[file_path.name] = {
            'tracker_count': len(trackers),
            'module_count': len(modules),
            'entity_types': entity_stats
        }
    else:
        logger.warning(f"Skipping {file_path.name} (DWG format - convert to DXF first)")

logger.info(f"Processing complete: {len(all_trackers)} total trackers, {len(all_modules)} total modules")

def analyze_extraction_quality(trackers, modules, coord_config):
    """Analyze the quality of extracted data."""
    analysis = {
        'extraction_timestamp': datetime.now().isoformat(),
        'coordinate_system_used': coord_config['coordinate_system']['type'] if coord_config else 'Unknown',
        'validation_applied': coord_config is not None
    }
    
    if trackers:
        tracker_coords = np.array([[t['x'], t['y']] for t in trackers])
        analysis['tracker_analysis'] = {
            'count': len(trackers),
            'x_range': [float(tracker_coords[:, 0].min()), float(tracker_coords[:, 0].max())],
            'y_range': [float(tracker_coords[:, 1].min()), float(tracker_coords[:, 1].max())],
            'spatial_extent': {
                'width': float(tracker_coords[:, 0].max() - tracker_coords[:, 0].min()),
                'height': float(tracker_coords[:, 1].max() - tracker_coords[:, 1].min())
            }
        }
        
        logger.info(f"Tracker spatial extent: {analysis['tracker_analysis']['spatial_extent']['width']:.1f} x {analysis['tracker_analysis']['spatial_extent']['height']:.1f} m")
    
    if modules:
        module_coords = np.array([[m['x'], m['y']] for m in modules])
        analysis['module_analysis'] = {
            'count': len(modules),
            'x_range': [float(module_coords[:, 0].min()), float(module_coords[:, 0].max())],
            'y_range': [float(module_coords[:, 1].min()), float(module_coords[:, 1].max())],
            'average_dimensions': {
                'width': float(np.mean([m['width'] for m in modules if 'width' in m])),
                'height': float(np.mean([m['height'] for m in modules if 'height' in m]))
            } if any('width' in m for m in modules) else None
        }
    
    return analysis

# Analyze extraction quality
if all_trackers or all_modules:
    quality_analysis = analyze_extraction_quality(all_trackers, all_modules, coord_config)
    logger.info("Data quality analysis complete")
else:
    logger.warning("No data extracted for quality analysis")
    quality_analysis = None

def save_extraction_results(trackers, modules, quality_analysis, processing_stats, 
                          site_name, output_dir):
    """Save extraction results to CSV files and generate summary."""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_path = Path(output_dir)
    run_output_dir = output_path / f"{site_name.lower()}_solar_extraction_{timestamp}"
    run_output_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"Saving results to: {run_output_dir}")
    
    saved_files = []
    
    # Save tracker coordinates
    if trackers:
        trackers_df = pd.DataFrame(trackers)
        tracker_file = run_output_dir / f"tracker_coordinates_{timestamp}.csv"
        trackers_df.to_csv(tracker_file, index=False)
        saved_files.append(tracker_file.name)
        logger.info(f"Saved tracker coordinates: {tracker_file.name} ({len(trackers)} points)")
    
    # Save module boundaries
    if modules:
        modules_df = pd.DataFrame(modules)
        module_file = run_output_dir / f"module_boundaries_{timestamp}.csv"
        modules_df.to_csv(module_file, index=False)
        saved_files.append(module_file.name)
        logger.info(f"Saved module boundaries: {module_file.name} ({len(modules)} areas)")
    
    # Save comprehensive summary
    summary = {
        'extraction_info': {
            'site_name': site_name,
            'timestamp': timestamp,
            'target_files': target_files,
            'files_processed': list(processing_stats.keys())
        },
        'results': {
            'tracker_count': len(trackers),
            'module_count': len(modules),
            'files_generated': saved_files
        },
        'processing_stats': processing_stats,
        'quality_analysis': quality_analysis,
        'coordinate_config_used': coord_config['analysis_info']['timestamp'] if coord_config else None,
        'usage_instructions': {
            'tracker_coordinates': 'Primary alignment points for point cloud registration',
            'module_boundaries': 'Secondary validation areas for spatial coverage checks',
            'coordinate_system': quality_analysis['coordinate_system_used'] if quality_analysis else 'Unknown'
        }
    }
    
    summary_file = run_output_dir / f"extraction_summary_{timestamp}.json"
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2, default=str)
    
    logger.info(f"Saved extraction summary: {summary_file.name}")
    
    return run_output_dir, saved_files

# Save results
if all_trackers or all_modules:
    output_directory, output_files = save_extraction_results(
        all_trackers, all_modules, quality_analysis, processing_stats, 
        site_name, output_dir
    )
    
    logger.info("Solar CAD extraction completed successfully")
    logger.info(f"Output directory: {output_directory}")
    logger.info(f"Files generated: {output_files}")
    
    # Final summary
    print(f"\nExtraction Summary:")
    print(f"Site: {site_name}")
    print(f"Trackers extracted: {len(all_trackers)}")
    print(f"Modules extracted: {len(all_modules)}")
    print(f"Coordinate system: {quality_analysis['coordinate_system_used'] if quality_analysis else 'Unknown'}")
    print(f"Output directory: {output_directory}")
    
else:
    logger.error("No data extracted - check CAD files and layer names")
    raise ValueError("No solar equipment data extracted from CAD files")