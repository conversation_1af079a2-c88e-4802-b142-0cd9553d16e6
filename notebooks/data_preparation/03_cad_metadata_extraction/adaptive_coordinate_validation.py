# Adaptive Coordinate Validation - No Hard-Coding!
# This approach automatically detects valid coordinate ranges from the data itself

def extract_all_coordinates_first_pass(file_path):
    """
    First pass: Extract ALL coordinates without filtering to analyze the data distribution.
    This helps us understand what's valid vs invalid automatically.
    """
    try:
        doc = ezdxf.readfile(file_path)
        modelspace = doc.modelspace()
        
        all_coords = []
        
        for entity in modelspace:
            layer_name = getattr(entity.dxf, 'layer', '').upper()
            entity_type = entity.dxftype()
            
            # Collect coordinates from INSERT entities (trackers)
            if entity_type == 'INSERT' and any(keyword in layer_name for keyword in ['TRACKER', 'CVT']):
                if hasattr(entity.dxf, 'insert'):
                    point = entity.dxf.insert
                    all_coords.append({'x': point.x, 'y': point.y, 'z': point.z, 'type': 'tracker'})
            
            # Collect coordinates from POLYLINE entities (modules)
            elif entity_type in ['LWPOLYLINE', 'POLYLINE'] and any(keyword in layer_name for keyword in ['PVCASE', 'PV', 'MODULE']):
                try:
                    bbox = entity.bbox()
                    if bbox:
                        center_x = (bbox[0].x + bbox[1].x) / 2
                        center_y = (bbox[0].y + bbox[1].y) / 2
                        center_z = (bbox[0].z + bbox[1].z) / 2
                        all_coords.append({'x': center_x, 'y': center_y, 'z': center_z, 'type': 'module'})
                except:
                    pass
        
        return all_coords
    except Exception as e:
        print(f"Error in first pass: {e}")
        return []

def analyze_coordinate_distribution(coords):
    """
    Analyze coordinate distribution to automatically detect valid ranges.
    Uses statistical methods to identify the main cluster of coordinates.
    """
    if not coords:
        return None
    
    import numpy as np
    
    # Convert to numpy arrays for analysis
    x_coords = np.array([c['x'] for c in coords])
    y_coords = np.array([c['y'] for c in coords])
    
    # Remove obvious invalid coordinates (zeros, very small values)
    valid_mask = (x_coords != 0.0) & (y_coords != 0.0) & (np.abs(x_coords) > 100) & (np.abs(y_coords) > 100)
    
    if not np.any(valid_mask):
        print("⚠️ No obviously valid coordinates found")
        return None
    
    x_valid = x_coords[valid_mask]
    y_valid = y_coords[valid_mask]
    
    # Use statistical analysis to find the main cluster
    # Method 1: Remove outliers using IQR (Interquartile Range)
    def remove_outliers_iqr(data):
        Q1 = np.percentile(data, 25)
        Q3 = np.percentile(data, 75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        return lower_bound, upper_bound
    
    x_min, x_max = remove_outliers_iqr(x_valid)
    y_min, y_max = remove_outliers_iqr(y_valid)
    
    # Add some buffer for safety (5% on each side)
    x_range = x_max - x_min
    y_range = y_max - y_min
    
    x_min_buffered = x_min - 0.05 * x_range
    x_max_buffered = x_max + 0.05 * x_range
    y_min_buffered = y_min - 0.05 * y_range
    y_max_buffered = y_max + 0.05 * y_range
    
    validation_ranges = {
        'x_min': x_min_buffered,
        'x_max': x_max_buffered,
        'y_min': y_min_buffered,
        'y_max': y_max_buffered,
        'total_coords_analyzed': len(coords),
        'valid_coords_found': len(x_valid),
        'outliers_removed': len(coords) - len(x_valid)
    }
    
    print(f"📊 Adaptive coordinate analysis:")
    print(f"   Total coordinates analyzed: {validation_ranges['total_coords_analyzed']}")
    print(f"   Valid coordinates found: {validation_ranges['valid_coords_found']}")
    print(f"   Outliers removed: {validation_ranges['outliers_removed']}")
    print(f"   Detected X range: {x_min_buffered:.1f} to {x_max_buffered:.1f}")
    print(f"   Detected Y range: {y_min_buffered:.1f} to {y_max_buffered:.1f}")
    
    return validation_ranges

def create_adaptive_validator(validation_ranges):
    """
    Create a validation function based on automatically detected ranges.
    """
    if not validation_ranges:
        # Fallback to basic validation if analysis failed
        def basic_validator(x, y, z=None):
            return x != 0.0 and y != 0.0 and abs(x) > 100 and abs(y) > 100
        return basic_validator
    
    def adaptive_validator(x, y, z=None):
        """Validate coordinates using automatically detected ranges."""
        # Basic checks first
        if x == 0.0 and y == 0.0:
            return False
        if abs(x) < 100 or abs(y) < 100:
            return False
        
        # Range checks using detected bounds
        if not (validation_ranges['x_min'] <= x <= validation_ranges['x_max']):
            return False
        if not (validation_ranges['y_min'] <= y <= validation_ranges['y_max']):
            return False
        
        return True
    
    return adaptive_validator

# Usage example:
def extract_solar_data_adaptive(file_path):
    """
    Extract solar data using adaptive coordinate validation.
    """
    print(f"\n🔧 Processing: {file_path.name}")
    
    # Step 1: First pass - collect all coordinates
    print("   📊 Analyzing coordinate distribution...")
    all_coords = extract_all_coordinates_first_pass(file_path)
    
    # Step 2: Analyze distribution and create validator
    validation_ranges = analyze_coordinate_distribution(all_coords)
    is_valid_coordinate = create_adaptive_validator(validation_ranges)
    
    # Step 3: Second pass - extract with validation
    print("   🔍 Extracting with adaptive validation...")
    
    try:
        doc = ezdxf.readfile(file_path)
        modelspace = doc.modelspace()
        
        trackers = []
        modules = []
        
        for entity in modelspace:
            layer_name = getattr(entity.dxf, 'layer', '').upper()
            entity_type = entity.dxftype()
            
            # Extract trackers with validation
            if entity_type == 'INSERT' and any(keyword in layer_name for keyword in ['TRACKER', 'CVT']):
                if hasattr(entity.dxf, 'insert'):
                    point = entity.dxf.insert
                    if is_valid_coordinate(point.x, point.y, point.z):
                        tracker_data = {
                            'x': point.x, 'y': point.y, 'z': point.z,
                            'layer': layer_name,
                            'block_name': getattr(entity.dxf, 'name', ''),
                            'type': 'tracker',
                            'source_file': file_path.name
                        }
                        trackers.append(tracker_data)
            
            # Extract modules with validation
            elif entity_type in ['LWPOLYLINE', 'POLYLINE'] and any(keyword in layer_name for keyword in ['PVCASE', 'PV', 'MODULE']):
                try:
                    bbox = entity.bbox()
                    if bbox:
                        center_x = (bbox[0].x + bbox[1].x) / 2
                        center_y = (bbox[0].y + bbox[1].y) / 2
                        center_z = (bbox[0].z + bbox[1].z) / 2
                        
                        if is_valid_coordinate(center_x, center_y, center_z):
                            module_data = {
                                'x': center_x, 'y': center_y, 'z': center_z,
                                'layer': layer_name,
                                'x_min': bbox[0].x, 'y_min': bbox[0].y,
                                'x_max': bbox[1].x, 'y_max': bbox[1].y,
                                'type': 'module',
                                'source_file': file_path.name
                            }
                            modules.append(module_data)
                except:
                    pass
        
        print(f"   ✅ Found {len(trackers)} valid trackers and {len(modules)} valid modules")
        return trackers, modules, validation_ranges
        
    except Exception as e:
        print(f"   ❌ Error processing {file_path.name}: {e}")
        return [], [], None

print("🎯 Adaptive coordinate validation ready!")
print("📊 This automatically detects valid coordinate ranges from the data itself")
print("🌍 Works for any site - no hard-coding required!")
