#!/usr/bin/env python3
"""
Enhanced Foundation Pile Extractor for Montalto di Castro

This script specifically extracts and classifies foundation piles that were missed
in the original CAD extraction, based on the discovery of foundation pile layers.

Key Findings:
- Foundation piles are represented as LINE entities in specific layers
- CVT - PILE LATERAL: 210 entities
- CVT - PILE DRIVE: 144 entities  
- CVT - Pile END: 112 entities
- Total: 466 foundation pile LINE entities

Author: Preetam Balijepalli
Date: July 2025
Project: As-Built Foundation Analysis - Castro
"""

import pandas as pd
import numpy as np
from pathlib import Path
import ezdxf
import json
from datetime import datetime
from collections import defaultdict
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedFoundationPileClassifier:
    """Enhanced classifier that properly identifies foundation piles."""
    
    def __init__(self):
        # Foundation pile layer patterns (discovered from analysis)
        self.foundation_pile_layers = [
            'CVT - PILE LATERAL',
            'CVT - PILE DRIVE', 
            'CVT - <PERSON>le END',
            'CVT - Pile Drive PLINT',
            'CVT - Pile Lateral PLINT',
            'CVT - Pile END Plint'
        ]
        
        # Foundation pile block patterns
        self.foundation_pile_blocks = [
            'omega pile sez 1-2',
            'pile foundation',
            'foundation pile'
        ]
        
        # Tracker patterns (for module support piles)
        self.tracker_patterns = [
            'CVT_Tracker 1x52 int', 'CVT_Tracker 1x52 ext', 'CVT_Tracker 1X52 Edge',
            'CVT_Tracker 1x26 int', 'CVT_Tracker 1x26 ext'
        ]
        
        # Expected quantities for validation
        self.expected_quantities = {
            'foundation_piles': 4199,
            'module_support_piles': 23764,
            'trackers': 543
        }
    
    def classify_entity(self, entity, layer_name, block_name, text_content):
        """Enhanced classification that distinguishes foundation from module support piles."""
        entity_type = entity.dxftype()
        layer_lower = layer_name.lower()
        block_lower = block_name.lower()
        
        # Foundation pile classification (highest priority)
        if layer_name in self.foundation_pile_layers:
            return 'foundation_pile'
        
        if any(block_pattern.lower() in block_lower for block_pattern in self.foundation_pile_blocks):
            return 'foundation_pile'
        
        # Tracker classification (module support)
        for tracker_pattern in self.tracker_patterns:
            if tracker_pattern in layer_name:
                return 'tracker'
        
        # Module support pile classification (derived from trackers)
        if any(keyword in layer_lower for keyword in ['tracker', 'cvt']) and entity_type == 'INSERT':
            return 'module_support_pile'
        
        # General pile classification (fallback)
        if any(keyword in layer_lower for keyword in ['pile', 'piling']):
            return 'pile_general'
        
        # Infrastructure elements
        if any(keyword in layer_lower for keyword in ['road', 'strada']):
            return 'road'
        
        if any(keyword in layer_lower for keyword in ['fence', 'recinzione']):
            return 'fence'
        
        if any(keyword in layer_lower for keyword in ['electrical', 'voltage', 'trench']):
            return 'electrical'
        
        # Text annotations
        if entity_type in ['TEXT', 'MTEXT'] and text_content:
            return 'annotation'
        
        return 'unknown'
    
    def get_pile_type_details(self, classification, layer_name, entity_type):
        """Get detailed information about pile types."""
        details = {
            'pile_category': 'unknown',
            'structural_function': 'unknown',
            'representation_type': entity_type
        }
        
        if classification == 'foundation_pile':
            details['pile_category'] = 'foundation'
            details['structural_function'] = 'primary_structural_support'
            
            # Determine specific foundation pile type from layer
            if 'LATERAL' in layer_name:
                details['pile_subtype'] = 'lateral_support'
            elif 'DRIVE' in layer_name:
                details['pile_subtype'] = 'drive_pile'
            elif 'END' in layer_name:
                details['pile_subtype'] = 'end_pile'
            elif 'PLINT' in layer_name:
                details['pile_subtype'] = 'plinth_connection'
            else:
                details['pile_subtype'] = 'foundation_general'
                
        elif classification == 'module_support_pile':
            details['pile_category'] = 'module_support'
            details['structural_function'] = 'solar_panel_mounting'
            details['pile_subtype'] = 'tracker_support'
            
        elif classification == 'tracker':
            details['pile_category'] = 'tracker_system'
            details['structural_function'] = 'solar_tracking'
            details['pile_subtype'] = 'tracking_mechanism'
        
        return details

def extract_enhanced_pile_data(entity, classifier):
    """Extract enhanced pile data with proper foundation/module support distinction."""
    try:
        entity_type = entity.dxftype()
        layer_name = getattr(entity.dxf, 'layer', 'unknown')
        
        # Base entity data
        entity_data = {
            'entity_id': str(entity.dxf.handle),
            'entity_type': entity_type,
            'layer_name': layer_name,
            'color': getattr(entity.dxf, 'color', 256),
            'linetype': getattr(entity.dxf, 'linetype', 'Continuous'),
            'lineweight': getattr(entity.dxf, 'lineweight', -1)
        }
        
        # Extract coordinates based on entity type
        if entity_type == 'LINE':
            # LINE entities (foundation piles)
            start = getattr(entity.dxf, 'start', (0, 0, 0))
            end = getattr(entity.dxf, 'end', (0, 0, 0))
            length = float(np.linalg.norm(np.array(end) - np.array(start)))
            
            entity_data.update({
                'x_coord': float((start[0] + end[0]) / 2),
                'y_coord': float((start[1] + end[1]) / 2),
                'z_coord': float((start[2] + end[2]) / 2),
                'geometry_type': 'line',
                'start_x': float(start[0]),
                'start_y': float(start[1]),
                'start_z': float(start[2]),
                'end_x': float(end[0]),
                'end_y': float(end[1]),
                'end_z': float(end[2]),
                'length': length,
                'pile_length': length  # For foundation piles
            })
            
        elif entity_type == 'INSERT':
            # INSERT entities (trackers, module support)
            insert_point = getattr(entity.dxf, 'insert', (0, 0, 0))
            entity_data.update({
                'x_coord': float(insert_point[0]),
                'y_coord': float(insert_point[1]),
                'z_coord': float(insert_point[2]),
                'geometry_type': 'insert',
                'block_name': getattr(entity.dxf, 'name', ''),
                'rotation': getattr(entity.dxf, 'rotation', 0.0),
                'scale_x': getattr(entity.dxf, 'xscale', 1.0),
                'scale_y': getattr(entity.dxf, 'yscale', 1.0),
                'scale_z': getattr(entity.dxf, 'zscale', 1.0)
            })
            
        elif entity_type == 'CIRCLE':
            # CIRCLE entities (pile holes, markers)
            center = getattr(entity.dxf, 'center', (0, 0, 0))
            radius = getattr(entity.dxf, 'radius', 0.0)
            entity_data.update({
                'x_coord': float(center[0]),
                'y_coord': float(center[1]),
                'z_coord': float(center[2]),
                'geometry_type': 'circle',
                'radius': radius,
                'diameter': radius * 2
            })
            
        else:
            # Default handling
            entity_data.update({
                'x_coord': 0.0,
                'y_coord': 0.0,
                'z_coord': 0.0,
                'geometry_type': entity_type.lower()
            })
        
        # Classify entity
        block_name = entity_data.get('block_name', '')
        text_content = entity_data.get('text_content', '')
        classification = classifier.classify_entity(entity, layer_name, block_name, text_content)
        entity_data['classification'] = classification
        
        # Get detailed pile information
        pile_details = classifier.get_pile_type_details(classification, layer_name, entity_type)
        entity_data.update(pile_details)
        
        # Add extraction metadata
        entity_data['extraction_timestamp'] = datetime.now().isoformat()
        entity_data['extraction_method'] = 'enhanced_foundation_pile_extraction'
        
        return entity_data
        
    except Exception as e:
        logger.warning(f"Error extracting entity data: {e}")
        return {
            'entity_id': 'error',
            'entity_type': getattr(entity, 'dxftype', lambda: 'unknown')(),
            'error': str(e),
            'extraction_timestamp': datetime.now().isoformat()
        }

def process_cad_file_enhanced(file_path, classifier):
    """Process CAD file with enhanced foundation pile detection."""
    logger.info(f"Processing CAD file with enhanced foundation pile detection: {file_path.name}")
    
    try:
        doc = ezdxf.readfile(file_path)
        msp = doc.modelspace()
        
        all_entities = []
        processing_stats = {
            'total_entities': 0,
            'successful_extractions': 0,
            'failed_extractions': 0,
            'classification_counts': defaultdict(int),
            'pile_type_counts': defaultdict(int),
            'foundation_pile_details': defaultdict(int)
        }
        
        # Process all entities
        for entity in msp:
            entity_data = extract_enhanced_pile_data(entity, classifier)
            entity_data['source_file'] = file_path.name
            entity_data['source_space'] = 'modelspace'
            all_entities.append(entity_data)
            
            # Update statistics
            processing_stats['total_entities'] += 1
            if 'error' not in entity_data:
                processing_stats['successful_extractions'] += 1
                classification = entity_data.get('classification', 'unknown')
                processing_stats['classification_counts'][classification] += 1
                
                # Track pile types
                pile_category = entity_data.get('pile_category', 'unknown')
                if pile_category != 'unknown':
                    processing_stats['pile_type_counts'][pile_category] += 1
                
                # Track foundation pile subtypes
                if classification == 'foundation_pile':
                    pile_subtype = entity_data.get('pile_subtype', 'unknown')
                    processing_stats['foundation_pile_details'][pile_subtype] += 1
            else:
                processing_stats['failed_extractions'] += 1
        
        return all_entities, processing_stats
        
    except Exception as e:
        logger.error(f"Error processing CAD file: {e}")
        return [], {}

def validate_enhanced_extraction(processing_stats, expected_quantities):
    """Validate enhanced extraction against ground truth."""
    validation_results = {}
    
    print(f"\n=== ENHANCED FOUNDATION PILE VALIDATION ===")
    print(f"{'Pile Type':<25} {'Extracted':<12} {'Expected':<12} {'Match %':<10} {'Status':<15}")
    print(f"{'-'*25} {'-'*12} {'-'*12} {'-'*10} {'-'*15}")
    
    # Foundation piles validation
    foundation_count = processing_stats['classification_counts']['foundation_pile']
    expected_foundation = expected_quantities['foundation_piles']
    foundation_match = (foundation_count / expected_foundation) * 100 if expected_foundation > 0 else 0
    foundation_status = '✅ Excellent' if foundation_match >= 95 else '✅ Good' if foundation_match >= 80 else '⚠️  Partial' if foundation_match >= 50 else '❌ Poor'
    
    validation_results['foundation_piles'] = {
        'extracted': foundation_count,
        'expected': expected_foundation,
        'match_percentage': foundation_match,
        'status': foundation_status
    }
    
    print(f"{'foundation_piles':<25} {foundation_count:<12} {expected_foundation:<12} {foundation_match:<10.1f} {foundation_status:<15}")
    
    # Tracker validation
    tracker_count = processing_stats['classification_counts']['tracker']
    expected_trackers = expected_quantities['trackers']
    tracker_match = (tracker_count / expected_trackers) * 100 if expected_trackers > 0 else 0
    tracker_status = '✅ Excellent' if tracker_match >= 95 else '✅ Good' if tracker_match >= 80 else '⚠️  Partial' if tracker_match >= 50 else '❌ Poor'
    
    validation_results['trackers'] = {
        'extracted': tracker_count,
        'expected': expected_trackers,
        'match_percentage': tracker_match,
        'status': tracker_status
    }
    
    print(f"{'trackers':<25} {tracker_count:<12} {expected_trackers:<12} {tracker_match:<10.1f} {tracker_status:<15}")
    
    # Foundation pile breakdown
    if foundation_count > 0:
        print(f"\n=== FOUNDATION PILE BREAKDOWN ===")
        for pile_subtype, count in processing_stats['foundation_pile_details'].items():
            percentage = (count / foundation_count) * 100
            print(f"  {pile_subtype}: {count} ({percentage:.1f}%)")
    
    return validation_results

def main():
    """Main enhanced extraction function."""
    print("Enhanced Foundation Pile Extractor for Montalto di Castro")
    print("=" * 60)
    
    # Initialize enhanced classifier
    classifier = EnhancedFoundationPileClassifier()
    
    # Process CAD file
    cad_file_path = Path("../../../data/raw/motali_de_castro/cad/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf")
    
    if not cad_file_path.exists():
        print(f"CAD file not found: {cad_file_path}")
        return
    
    # Extract entities with enhanced classification
    all_entities, processing_stats = process_cad_file_enhanced(cad_file_path, classifier)
    
    if not all_entities:
        print("No entities extracted")
        return
    
    # Create DataFrame
    entities_df = pd.DataFrame(all_entities)
    
    print(f"\n=== ENHANCED EXTRACTION RESULTS ===")
    print(f"Total entities processed: {processing_stats['total_entities']}")
    print(f"Successful extractions: {processing_stats['successful_extractions']}")
    
    print(f"\nClassification Results:")
    for classification, count in processing_stats['classification_counts'].items():
        print(f"  {classification}: {count}")
    
    # Validate against expected quantities
    validation_results = validate_enhanced_extraction(processing_stats, classifier.expected_quantities)
    
    # Save enhanced results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = Path("../../../output_runs/cad_metadata")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Save main enhanced extraction
    enhanced_file = output_dir / f"enhanced_foundation_pile_extraction_{timestamp}.csv"
    entities_df.to_csv(enhanced_file, index=False)
    
    # Save foundation piles specifically
    foundation_piles = entities_df[entities_df['classification'] == 'foundation_pile']
    if len(foundation_piles) > 0:
        foundation_file = output_dir / f"foundation_piles_extracted_{timestamp}.csv"
        foundation_piles.to_csv(foundation_file, index=False)
        print(f"\nFoundation piles saved to: {foundation_file}")
    
    # Save validation report
    validation_report = {
        'extraction_metadata': {
            'timestamp': timestamp,
            'method': 'enhanced_foundation_pile_extraction',
            'source_file': str(cad_file_path)
        },
        'processing_statistics': dict(processing_stats),
        'validation_results': validation_results
    }
    
    validation_file = output_dir / f"enhanced_validation_report_{timestamp}.json"
    with open(validation_file, 'w') as f:
        json.dump(validation_report, f, indent=2, default=str)
    
    print(f"\nResults saved to: {output_dir}")
    print(f"Enhanced extraction: {enhanced_file.name}")
    print(f"Validation report: {validation_file.name}")
    
    # Summary
    foundation_count = processing_stats['classification_counts']['foundation_pile']
    expected_foundation = classifier.expected_quantities['foundation_piles']
    
    print(f"\n=== SUMMARY ===")
    print(f"Foundation piles found: {foundation_count}")
    print(f"Foundation piles expected: {expected_foundation}")
    print(f"Foundation pile detection rate: {(foundation_count/expected_foundation)*100:.1f}%")
    
    if foundation_count >= expected_foundation * 0.8:
        print("✅ Excellent foundation pile detection!")
    elif foundation_count >= expected_foundation * 0.5:
        print("⚠️  Good foundation pile detection with some gaps")
    else:
        print("❌ Foundation pile detection needs improvement")

if __name__ == "__main__":
    main()
