# Coordinate Validation Fix for Solar CAD Extraction
# Add this function to your notebook to improve coordinate filtering

def is_valid_coordinate(x, y, z=None):
    """
    Validate if coordinates are reasonable for the Castro solar site.
    
    Based on the reference data spatial distribution, valid coordinates should be:
    - X: approximately 707,000 to 708,000 (UTM Zone 33N)
    - Y: approximately 4,692,600 to 4,693,100 (UTM Zone 33N)
    """
    
    # Skip obvious invalid coordinates
    if x == 0.0 and y == 0.0:
        return False
    
    # Skip coordinates that are clearly out of range for Castro site
    # Based on reference data ranges from the plots
    if not (707000 <= x <= 708500):  # X coordinate range
        return False
        
    if not (4692500 <= y <= 4693200):  # Y coordinate range  
        return False
    
    # Skip coordinates that are too close to origin (likely placeholders)
    if abs(x) < 1000 or abs(y) < 1000:
        return False
    
    return True

# Updated extraction logic - replace your current coordinate filtering with this:

# For tracker extraction:
if hasattr(entity.dxf, 'insert'):
    point = entity.dxf.insert
    
    # Use improved validation instead of simple zero check
    if is_valid_coordinate(point.x, point.y, point.z):
        tracker_data = {
            'x': point.x,
            'y': point.y,
            'z': point.z,
            'layer': layer_name,
            'block_name': getattr(entity.dxf, 'name', ''),
            'type': 'tracker',
            'source_file': file_path.name
        }
        trackers.append(tracker_data)

# For module extraction:
if bbox:
    center_x = (bbox[0].x + bbox[1].x) / 2
    center_y = (bbox[0].y + bbox[1].y) / 2
    center_z = (bbox[0].z + bbox[1].z) / 2
    
    # Use improved validation
    if is_valid_coordinate(center_x, center_y, center_z):
        module_data = {
            'x': center_x,
            'y': center_y,
            'z': center_z,
            'layer': layer_name,
            'x_min': bbox[0].x,
            'y_min': bbox[0].y,
            'x_max': bbox[1].x,
            'y_max': bbox[1].y,
            'type': 'module',
            'source_file': file_path.name
        }
        modules.append(module_data)

print("✅ Coordinate validation function ready!")
print("📍 Valid coordinate ranges:")
print("   X: 707,000 to 708,500 (UTM Zone 33N)")
print("   Y: 4,692,500 to 4,693,200 (UTM Zone 33N)")
print("🎯 This should filter out invalid coordinates and improve alignment!")
