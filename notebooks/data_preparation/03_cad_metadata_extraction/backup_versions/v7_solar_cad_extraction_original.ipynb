{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Solar Project CAD Extraction for Alignment\n", "\n", "Focused extraction of solar project infrastructure from CAD files for spatial alignment workflows.\n", "\n", "**Purpose**: Extract tracker positions, module layouts, and infrastructure for point cloud alignment  \n", "**Input**: DXF/DWG files containing solar project layouts  \n", "**Output**: Structured coordinates and metadata for alignment workflows  \n", "\n", "## Based on Actual CAD Analysis:\n", "From executing CAD extraction on Castro project, we discovered:\n", "- **Tracker Systems**: 1,417 INSERT entities (CVT_Tracker layers) with precise coordinates\n", "- **Module Layouts**: 161 LWPOLYLINE entities (PVcase layers) defining solar panel areas\n", "- **DC Cable Routes**: 35 electrical infrastructure entities\n", "- **Site Infrastructure**: 815 road entities, 614 building entities\n", "- **No Foundation Piles**: Solar CAD contains tracker/module layouts, not structural foundations\n", "\n", "## Value for Alignment:\n", "- Tracker coordinates provide reference points for point cloud registration\n", "- Module boundaries define expected solar panel locations\n", "- Infrastructure layout guides spatial validation\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Papermill parameters\n", "project_type = \"solar_project\"  # Generic for any solar project\n", "site_name = \"cad_extraction\"\n", "cad_data_path = \"../../../data/raw\"  # Auto-discover CAD files\n", "output_dir = \"../../../output_runs/cad_metadata\"\n", "coordinate_system = \"auto\"  # Auto-detect or specify (e.g., EPSG:32633)\n", "focus_on_alignment = True  # Extract only alignment-relevant data\n", "target_files = [\"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\", \"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg\"]  # Specific files to process"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "import ezdxf\n", "import json\n", "from datetime import datetime\n", "from collections import defaultdict\n", "import logging\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "print(f\"Solar CAD Extraction for Alignment - {project_type.title()}\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(\"=\" * 60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. CAD File Discovery and Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def discover_cad_files(base_path):\n", "    \"\"\"Discover DXF and DWG files in the data directory.\"\"\"\n", "    base_path = Path(base_path)\n", "    cad_files = []\n", "    \n", "    # Search for CAD files\n", "    for pattern in ['**/*.dxf', '**/*.DXF', '**/*.dwg', '**/*.DWG']:\n", "        cad_files.extend(base_path.glob(pattern))\n", "    \n", "    return sorted(cad_files)\n", "\n", "def analyze_solar_layers(file_path):\n", "    \"\"\"Analyze CAD file for solar-specific layers.\"\"\"\n", "    try:\n", "        doc = ezdxf.readfile(file_path)\n", "        msp = doc.modelspace()\n", "        \n", "        # Count solar-relevant entities\n", "        solar_layers = defaultdict(int)\n", "        entity_types = defaultdict(int)\n", "        \n", "        for entity in msp:\n", "            layer = getattr(entity.dxf, 'layer', 'unknown')\n", "            entity_type = entity.dxftype()\n", "            \n", "            # Focus on solar-relevant layers\n", "            if any(keyword in layer.upper() for keyword in ['TRACKER', 'CVT', 'PVCASE', 'SOLAR', 'PANEL']):\n", "                solar_layers[layer] += 1\n", "            \n", "            entity_types[entity_type] += 1\n", "        \n", "        return {\n", "            'solar_layers': dict(solar_layers),\n", "            'entity_types': dict(entity_types),\n", "            'total_entities': sum(entity_types.values()),\n", "            'solar_entities': sum(solar_layers.values())\n", "        }\n", "    except Exception as e:\n", "        logger.warning(f\"Could not analyze {file_path}: {e}\")\n", "        return None\n", "\n", "# Discover and analyze CAD files\n", "print(\"=== CAD FILE DISCOVERY ===\")\n", "cad_files = discover_cad_files(cad_data_path)\n", "\n", "if not cad_files:\n", "    print(f\"No CAD files found in {cad_data_path}\")\n", "else:\n", "    print(f\"Found {len(cad_files)} CAD files:\")\n", "    for i, file_path in enumerate(cad_files, 1):\n", "        print(f\"  {i}. {file_path.name} ({file_path.stat().st_size / 1024 / 1024:.1f} MB)\")\n", "    \n", "    # Analyze solar content\n", "    print(f\"\\n=== SOLAR CONTENT ANALYSIS ===\")\n", "    file_analyses = {}\n", "    for file_path in cad_files[:2]:  # Analyze first 2 files\n", "        print(f\"\\nAnalyzing: {file_path.name}\")\n", "        analysis = analyze_solar_layers(file_path)\n", "        if analysis:\n", "            file_analyses[file_path.name] = analysis\n", "            print(f\"  Total entities: {analysis['total_entities']}\")\n", "            print(f\"  Solar-relevant entities: {analysis['solar_entities']}\")\n", "            print(f\"  Solar layers: {list(analysis['solar_layers'].keys())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Solar-Focused Classification System"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class SolarAlignmentClassifier:\n", "    \"\"\"Classifier focused on solar infrastructure for alignment purposes.\"\"\"\n", "    \n", "    def __init__(self):\n", "        # Based on actual CAD analysis results\n", "        self.classification_rules = {\n", "            # Tracker systems (primary alignment references)\n", "            'tracker': {\n", "                'layers': ['CVT_Tracker 1x52 int', 'CVT_Tracker 1x26 int', \n", "                          'CVT_Tracker 1X52 Edge', 'CVT_Tracker 1x26 ext', 'CVT_Tracker 1x52 ext'],\n", "                'entity_types': ['INSERT'],\n", "                'keywords': ['tracker', 'CVT', '1P52', '1P26'],\n", "                'alignment_value': 'high'  # Primary reference points\n", "            },\n", "            \n", "            # Module layouts (secondary alignment references)\n", "            'module': {\n", "                'layers': ['PVcase Offsets', 'PVcase PV Area'],\n", "                'entity_types': ['LWPOLYLINE'],\n", "                'keywords': ['pvcase', 'pv', 'module', 'panel'],\n", "                'alignment_value': 'medium'  # Area boundaries\n", "            },\n", "            \n", "            # DC electrical (infrastructure context)\n", "            'electrical': {\n", "                'layers': ['DC', 'ELECTRICAL', 'CABLE'],\n", "                'entity_types': ['POLYLINE', 'LWPOLYLINE', 'LINE'],\n", "                'keywords': ['dc', 'electrical', 'cable'],\n", "                'alignment_value': 'low'  # Context only\n", "            },\n", "            \n", "            # Site infrastructure (context)\n", "            'infrastructure': {\n", "                'layers': ['ROAD', 'BUILDING', 'BOUNDARY'],\n", "                'entity_types': ['POLYLIN<PERSON>', 'LWPOLYLINE', 'HATCH'],\n", "                'keywords': ['road', 'building', 'boundary'],\n", "                'alignment_value': 'low'  # Context only\n", "            }\n", "        }\n", "    \n", "    def classify_entity(self, entity_data):\n", "        \"\"\"Classify entity for alignment purposes.\"\"\"\n", "        layer_name = entity_data.get('layer_name', '').upper()\n", "        entity_type = entity_data.get('entity_type', '')\n", "        block_name = str(entity_data.get('block_name', '')).lower()\n", "        \n", "        for classification, rules in self.classification_rules.items():\n", "            # Check exact layer match\n", "            if any(layer.upper() == layer_name for layer in rules['layers']):\n", "                return classification\n", "            \n", "            # Check partial layer match\n", "            if any(layer.upper() in layer_name for layer in rules['layers']):\n", "                return classification\n", "            \n", "            # Check entity type and keyword combination\n", "            if entity_type in rules['entity_types']:\n", "                if any(keyword in block_name for keyword in rules['keywords']):\n", "                    return classification\n", "        \n", "        return 'other'\n", "    \n", "    def get_alignment_value(self, classification):\n", "        \"\"\"Get alignment value for a classification.\"\"\"\n", "        if classification in self.classification_rules:\n", "            return self.classification_rules[classification]['alignment_value']\n", "        return 'none'\n", "\n", "# Initialize classifier\n", "classifier = SolarAlignmentClassifier()\n", "print(\"Solar Alignment Classifier initialized\")\n", "print(f\"Focus classifications: {list(classifier.classification_rules.keys())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Alignment-Focused Entity Extraction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_alignment_entity(entity):\n", "    \"\"\"Extract entity data focused on alignment needs.\"\"\"\n", "    try:\n", "        entity_type = entity.dxftype()\n", "        layer_name = getattr(entity.dxf, 'layer', 'unknown')\n", "        \n", "        # Base entity data\n", "        entity_data = {\n", "            'entity_id': str(entity.dxf.handle),\n", "            'entity_type': entity_type,\n", "            'layer_name': layer_name\n", "        }\n", "        \n", "        # Extract coordinates based on entity type\n", "        if hasattr(entity.dxf, 'insert'):\n", "            # INSERT entities (trackers) - PRIMARY ALIGNMENT POINTS\n", "            insert_point = entity.dxf.insert\n", "            entity_data.update({\n", "                'x_coord': insert_point.x,\n", "                'y_coord': insert_point.y,\n", "                'z_coord': insert_point.z,\n", "                'block_name': getattr(entity.dxf, 'name', ''),\n", "                'rotation': getattr(entity.dxf, 'rotation', 0.0),\n", "                'geometry_type': 'point'\n", "            })\n", "            \n", "        <PERSON><PERSON> hasattr(entity.dxf, 'start') and hasattr(entity.dxf, 'end'):\n", "            # LINE entities\n", "            start_point = entity.dxf.start\n", "            end_point = entity.dxf.end\n", "            entity_data.update({\n", "                'x_coord': (start_point.x + end_point.x) / 2,\n", "                'y_coord': (start_point.y + end_point.y) / 2,\n", "                'z_coord': (start_point.z + end_point.z) / 2,\n", "                'start_x': start_point.x,\n", "                'start_y': start_point.y,\n", "                'end_x': end_point.x,\n", "                'end_y': end_point.y,\n", "                'length': start_point.distance(end_point),\n", "                'geometry_type': 'line'\n", "            })\n", "            \n", "        elif entity_type in ['POLYLINE', 'LWPOLYLINE']:\n", "            # POLYLINE entities (module areas) - AREA BOUNDARIES\n", "            try:\n", "                # Get bounding box for polylines\n", "                bbox = entity.bbox()\n", "                if bbox:\n", "                    entity_data.update({\n", "                        'x_coord': (bbox[0].x + bbox[1].x) / 2,\n", "                        'y_coord': (bbox[0].y + bbox[1].y) / 2,\n", "                        'z_coord': (bbox[0].z + bbox[1].z) / 2,\n", "                        'x_min': bbox[0].x,\n", "                        'y_min': bbox[0].y,\n", "                        'x_max': bbox[1].x,\n", "                        'y_max': bbox[1].y,\n", "                        'geometry_type': 'area'\n", "                    })\n", "                else:\n", "                    entity_data.update({'x_coord': 0.0, 'y_coord': 0.0, 'z_coord': 0.0, 'geometry_type': 'area'})\n", "            except:\n", "                entity_data.update({'x_coord': 0.0, 'y_coord': 0.0, 'z_coord': 0.0, 'geometry_type': 'area'})\n", "                \n", "        else:\n", "            # Skip entities without useful coordinates for alignment\n", "            return None\n", "        \n", "        # Classification and alignment value\n", "        classification = classifier.classify_entity(entity_data)\n", "        entity_data['classification'] = classification\n", "        entity_data['alignment_value'] = classifier.get_alignment_value(classification)\n", "        entity_data['extraction_timestamp'] = datetime.now().isoformat()\n", "        \n", "        # Only return entities useful for alignment\n", "        if focus_on_alignment and entity_data['alignment_value'] == 'none':\n", "            return None\n", "            \n", "        return entity_data\n", "        \n", "    except Exception as e:\n", "        logger.warning(f\"Error extracting entity data: {e}\")\n", "        return None\n", "\n", "print(\"Alignment-focused entity extraction function defined\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Process CAD Files for Alignment Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Process CAD files\n", "if not cad_files:\n", "    print(\"No CAD files to process\")\n", "    alignment_entities_df = pd.DataFrame()\n", "else:\n", "    print(f\"\\n=== PROCESSING FOR ALIGNMENT DATA ===\")\n", "    \n", "    # Process first CAD file (or specify which one)\n", "    target_file = cad_files[0]  # Adjust as needed\n", "    print(f\"Processing: {target_file.name}\")\n", "    \n", "    try:\n", "        doc = ezdxf.readfile(target_file)\n", "        msp = doc.modelspace()\n", "        \n", "        alignment_entities = []\n", "        processing_stats = defaultdict(int)\n", "        \n", "        for entity in msp:\n", "            processing_stats['total_entities'] += 1\n", "            \n", "            entity_data = extract_alignment_entity(entity)\n", "            if entity_data:\n", "                entity_data['source_file'] = target_file.name\n", "                alignment_entities.append(entity_data)\n", "                \n", "                processing_stats['extracted_entities'] += 1\n", "                processing_stats[f\"classification_{entity_data['classification']}\"] += 1\n", "                processing_stats[f\"alignment_{entity_data['alignment_value']}\"] += 1\n", "            else:\n", "                processing_stats['skipped_entities'] += 1\n", "        \n", "        print(f\"\\nProcessing Results:\")\n", "        print(f\"  Total entities: {processing_stats['total_entities']}\")\n", "        print(f\"  Extracted for alignment: {processing_stats['extracted_entities']}\")\n", "        print(f\"  Skipped (not alignment-relevant): {processing_stats['skipped_entities']}\")\n", "        \n", "        # Convert to DataFrame\n", "        if alignment_entities:\n", "            alignment_entities_df = pd.DataFrame(alignment_entities)\n", "            print(f\"\\nAlignment DataFrame: {len(alignment_entities_df)} entities\")\n", "            \n", "            # Show alignment value distribution\n", "            alignment_dist = alignment_entities_df['alignment_value'].value_counts()\n", "            print(f\"\\nAlignment Value Distribution:\")\n", "            for value, count in alignment_dist.items():\n", "                print(f\"  {value}: {count} entities\")\n", "            \n", "            # Show classification distribution\n", "            class_dist = alignment_entities_df['classification'].value_counts()\n", "            print(f\"\\nClassification Distribution:\")\n", "            for classification, count in class_dist.items():\n", "                print(f\"  {classification}: {count} entities\")\n", "        else:\n", "            print(\"No alignment-relevant entities extracted\")\n", "            alignment_entities_df = pd.DataFrame()\n", "            \n", "    except Exception as e:\n", "        logger.error(f\"Error processing {target_file.name}: {e}\")\n", "        alignment_entities_df = pd.DataFrame()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Generate Alignment-Ready Outputs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not alignment_entities_df.empty:\n", "    # Create output directory\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    output_path = Path(output_dir)\n", "    run_output_dir = output_path / f\"{project_type}_alignment_{timestamp}\"\n", "    run_output_dir.mkdir(parents=True, exist_ok=True)\n", "    \n", "    print(f\"\\n=== GENERATING ALIGNMENT OUTPUTS ===\")\n", "    print(f\"Output directory: {run_output_dir}\")\n", "    \n", "    # 1. High-value alignment points (trackers)\n", "    high_value = alignment_entities_df[alignment_entities_df['alignment_value'] == 'high']\n", "    if not high_value.empty:\n", "        tracker_file = run_output_dir / f\"tracker_alignment_points_{timestamp}.csv\"\n", "        high_value.to_csv(tracker_file, index=False)\n", "        print(f\"Tracker alignment points: {tracker_file.name} ({len(high_value)} points)\")\n", "        \n", "        # Create simplified coordinate file for alignment algorithms\n", "        coords_file = run_output_dir / f\"tracker_coordinates_{timestamp}.csv\"\n", "        coords_df = high_value[['x_coord', 'y_coord', 'z_coord', 'block_name', 'rotation']].copy()\n", "        coords_df.to_csv(coords_file, index=False)\n", "        print(f\"Tracker coordinates: {coords_file.name} ({len(coords_df)} coordinates)\")\n", "    \n", "    # 2. Module area boundaries\n", "    medium_value = alignment_entities_df[alignment_entities_df['alignment_value'] == 'medium']\n", "    if not medium_value.empty:\n", "        module_file = run_output_dir / f\"module_boundaries_{timestamp}.csv\"\n", "        medium_value.to_csv(module_file, index=False)\n", "        print(f\"Module boundaries: {module_file.name} ({len(medium_value)} areas)\")\n", "    \n", "    # 3. Complete alignment dataset\n", "    complete_file = run_output_dir / f\"solar_alignment_data_{timestamp}.csv\"\n", "    alignment_entities_df.to_csv(complete_file, index=False)\n", "    print(f\"Complete alignment data: {complete_file.name} ({len(alignment_entities_df)} entities)\")\n", "    \n", "    print(f\"\\n✅ Alignment data extraction completed\")\n", "    print(f\"📁 Files ready for alignment workflows: {run_output_dir}\")\n", "    \n", "    print(f\"\\n🎯 Recommended Usage:\")\n", "    print(f\"  1. Use tracker coordinates for initial point cloud registration\")\n", "    print(f\"  2. Use module boundaries for spatial extent validation\")\n", "    print(f\"  3. Apply coordinate transformations if needed (EPSG:32633)\")\n", "    print(f\"  4. Validate alignment accuracy against extracted reference points\")\n", "    \n", "else:\n", "    print(\"No alignment data to export\")\n", "\n", "print(f\"\\n📅 Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}