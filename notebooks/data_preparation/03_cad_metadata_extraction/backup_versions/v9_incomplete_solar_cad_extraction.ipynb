{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Solar CAD Metadata Extraction\n", "\n", "Simplified, focused extraction using shell commands and minimal Python.\n", "\n", "**Purpose**: Extract tracker coordinates and module boundaries for alignment  \n", "**Approach**: Use `!` commands for file discovery and analysis, minimal Python functions  \n", "**Target**: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf/dwg files  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["# Papermill parameters - these will be injected by Papermill\n", "site_name = \"Castro\"  # Site name for output file naming\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "\n", "target_files = [\"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\", \"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg\"]\n", "search_path = \"../../../data/raw\"\n", "output_dir = \"../../../output_runs/cad_metadata\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Setup and Imports"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "\n", "import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "import ezdxf\n", "import json\n", "from datetime import datetime\n", "from collections import defaultdict\n", "import logging"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Starting Solar CAD Metadata Extraction\n", "INFO:__main__:Target files: ['GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf', 'GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg']\n", "INFO:__main__:Timestamp: 2025-07-01 14:48:03\n"]}], "source": ["logger.info(f\"Starting Solar CAD Metadata Extraction\")\n", "logger.info(f\"Target files: {target_files}\")\n", "logger.info(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Find CAD Files"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:=== FINDING CAD FILES ===\n", "INFO:__main__:\n", "Looking for: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Searching for target files...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:  Found: ../../../data/raw/motali_de_castro/cad/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf (15.5 MB)\n", "INFO:__main__:\n", "Looking for: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg\n", "INFO:__main__:  Found: ../../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg (4.6 MB)\n"]}], "source": ["# Find target files\n", "logger.info(\"=== FINDING CAD FILES ===\")\n", "print(\"\\nSearching for target files...\")\n", "for target_file in target_files:\n", "    logger.info(f\"\\nLooking for: {target_file}\")\n", "    result = !find {search_path} -name \"{target_file}\" 2>/dev/null\n", "    if result:\n", "        file_path = Path(result[0])\n", "        size_mb = file_path.stat().st_size / 1024 / 1024\n", "        logger.info(f\"  Found: {file_path} ({size_mb:.1f} MB)\")\n", "    else:\n", "        logger.info(f\"  Not found: {target_file}\")\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Quick CAD Analysis with ogrin<PERSON>"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== QUICK CAD ANALYSIS ===\n", "ogrinfo available\n", "\n", "Analyzing GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf:\n", "INFO: Open of `../../../data/raw/motali_de_castro/cad/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf'\n", "      using driver `DXF' successful.\n", "1: entities\n", "\n", "Skipping GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg (DWG - use DXF for ogrinfo)\n"]}], "source": ["print(\"\\n=== QUICK CAD ANALYSIS ===\")\n", "\n", "# Check if ogrinfo is available\n", "ogrinfo_check = !which ogrinfo\n", "if ogrinfo_check:\n", "    print(\"ogrinfo available\")\n", "    \n", "    # Analyze DXF files only (ogrinfo works best with DXF)\n", "    for file_path in cad_files:\n", "        if file_path.suffix.lower() == '.dxf':\n", "            print(f\"\\nAnalyzing {file_path.name}:\")\n", "            !ogrinfo -so \"{file_path}\"\n", "        else:\n", "            print(f\"\\nSkipping {file_path.name} (DWG - use DXF for ogrinfo)\")\n", "else:\n", "    print(\"ogrinfo not available - install with: brew install gdal\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Simple Coordinate Extraction"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Simple coordinate extraction function ready\n"]}], "source": ["def extract_coordinates_simple(file_path):\n", "    \"\"\"Simple coordinate extraction focused on trackers and modules.\"\"\"\n", "    try:\n", "        doc = ezdxf.readfile(file_path)\n", "        msp = doc.modelspace()\n", "        \n", "        trackers = []  # INSERT entities (high value for alignment)\n", "        modules = []   # POLYLINE entities (medium value for alignment)\n", "        \n", "        for entity in msp:\n", "            layer = getattr(entity.dxf, 'layer', '').upper()\n", "            entity_type = entity.dxftype()\n", "            \n", "            # Extract tracker coordinates (INSERT entities)\n", "            if entity_type == 'INSERT' and any(keyword in layer for keyword in ['TRACKER', 'CVT']):\n", "                if hasattr(entity.dxf, 'insert'):\n", "                    pt = entity.dxf.insert\n", "                    # Skip zero coordinates\n", "                    if not (pt.x == 0.0 and pt.y == 0.0):\n", "                        trackers.append({\n", "                            'x': pt.x, 'y': pt.y, 'z': pt.z,\n", "                            'layer': layer,\n", "                            'block_name': getattr(entity.dxf, 'name', ''),\n", "                            'type': 'tracker'\n", "                        })\n", "            \n", "            # Extract module boundaries (POLYLINE entities)\n", "            elif entity_type in ['LWPOLYLINE', 'POLYLINE'] and any(keyword in layer for keyword in ['PVCASE', 'PV', 'MODULE']):\n", "                try:\n", "                    bbox = entity.bbox()\n", "                    if bbox:\n", "                        center_x = (bbox[0].x + bbox[1].x) / 2\n", "                        center_y = (bbox[0].y + bbox[1].y) / 2\n", "                        # Skip zero coordinates\n", "                        if not (center_x == 0.0 and center_y == 0.0):\n", "                            modules.append({\n", "                                'x': center_x, 'y': center_y, 'z': (bbox[0].z + bbox[1].z) / 2,\n", "                                'layer': layer,\n", "                                'x_min': bbox[0].x, 'y_min': bbox[0].y,\n", "                                'x_max': bbox[1].x, 'y_max': bbox[1].y,\n", "                                'type': 'module'\n", "                            })\n", "                except:\n", "                    pass\n", "        \n", "        return trackers, modules\n", "        \n", "    except Exception as e:\n", "        print(f\"Error processing {file_path}: {e}\")\n", "        return [], []\n", "\n", "print(\"Simple coordinate extraction function ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Process Files and Extract Data"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:\n", "=== PROCESSING CAD FILES ===\n", "INFO:__main__:\n", "Loading DXF file: ../../../data/raw/motali_de_castro/cad/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\n"]}], "source": ["logger.info(\"\\n=== PROCESSING CAD FILES ===\")\n", "\n", "# Load DXF CAD File\n", "if cad_files:\n", "    dxf_file_path = cad_files[0]  # or select specific one if multiple\n", "    logger.info(f\"\\nLoading DXF file: {dxf_file_path}\")\n", "    \n", "    doc = ezdxf.readfile(dxf_file_path)\n", "    modelspace = doc.modelspace()\n", "else:\n", "    raise FileNotFoundError(\"No CAD files found. Please check the search path and file names.\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["INSERT               566\n", "LWPOLYLINE           252\n", "MTEXT                124\n", "HATCH                 43\n", "LINE                  34\n", "POLYLINE              32\n", "CIRCLE                 6\n", "OLE2FRAME              3\n", "ARC                    1\n", "ACAD_PROXY_ENTITY      1\n", "ATTDEF                 1\n", "ACAD_TABLE             1\n", "PDFREFERENCE           1\n", "Name: count, dtype: int64"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["# Entity Type Exploration\n", "all_entities = list(modelspace)\n", "entity_types = [e.dxftype() for e in all_entities]\n", "pd.Series(entity_types).value_counts()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Entity Type</th>\n", "      <th>Count</th>\n", "      <th>Percentage</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>INSERT</td>\n", "      <td>566</td>\n", "      <td>53.15%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>LWPOLYLINE</td>\n", "      <td>252</td>\n", "      <td>23.66%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>MTEXT</td>\n", "      <td>124</td>\n", "      <td>11.64%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>HATCH</td>\n", "      <td>43</td>\n", "      <td>4.04%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>LINE</td>\n", "      <td>34</td>\n", "      <td>3.19%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>POLYLINE</td>\n", "      <td>32</td>\n", "      <td>3.00%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>CIRCLE</td>\n", "      <td>6</td>\n", "      <td>0.56%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>OLE2FRAME</td>\n", "      <td>3</td>\n", "      <td>0.28%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>ARC</td>\n", "      <td>1</td>\n", "      <td>0.09%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>ACAD_PROXY_ENTITY</td>\n", "      <td>1</td>\n", "      <td>0.09%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>ATTDEF</td>\n", "      <td>1</td>\n", "      <td>0.09%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>ACAD_TABLE</td>\n", "      <td>1</td>\n", "      <td>0.09%</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>PDFREFERENCE</td>\n", "      <td>1</td>\n", "      <td>0.09%</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          Entity Type  Count Percentage\n", "0              INSERT    566     53.15%\n", "1          LWPOLYLINE    252     23.66%\n", "2               MTEXT    124     11.64%\n", "3               HATCH     43      4.04%\n", "4                LINE     34      3.19%\n", "5            POLYLINE     32      3.00%\n", "6              CIRCLE      6      0.56%\n", "7           OLE2FRAME      3      0.28%\n", "8                 ARC      1      0.09%\n", "9   ACAD_PROXY_ENTITY      1      0.09%\n", "10             ATTDEF      1      0.09%\n", "11         ACAD_TABLE      1      0.09%\n", "12       PDFREFERENCE      1      0.09%"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["# Entity Type Frequency Table\n", "entity_type_series = pd.Series(entity_types)\n", "entity_counts = entity_type_series.value_counts().reset_index()\n", "entity_counts.columns = ['Entity Type', 'Count']\n", "entity_counts['Percentage'] = (entity_counts['Count'] / entity_counts['Count'].sum()) * 100\n", "entity_counts['Percentage'] = entity_counts['Percentage'].map(\"{:.2f}%\".format)\n", "entity_counts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA90AAAHqCAYAAAAZLi26AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAAckZJREFUeJzt3X18j/X////7a5u9dvLaifMZY5gNc5pKKEarEYrISeQ0JQqRGBWmDBHpRGezqSg5SZLzchJvJykTJSd9ImVLOdmcjm3H7w+/vb4Or2029tqc3K6Xy/Py9no+n8fz+TheO3j32PM4nofFMAxDAAAAAACgwLkUdQAAAAAAANyqSLoBAAAAAHASkm4AAAAAAJyEpBsAAAAAACch6QYAAAAAwElIugEAAAAAcBKSbgAAAAAAnISkGwAAAAAAJyHpBgAAAADASUi6AQC4wtixY2WxWAplroiICEVERNg/r1u3ThaLRQsWLCiU+Xv16qXg4OBCmetanT59Wk8++aQCAgJksVg0ZMiQog5JUuFeJ7cai8WisWPHFnUYAFAoSLoBALe0hIQEWSwWe/Hw8FBgYKCioqI0Y8YMnTp1qkDmOXLkiMaOHavExMQCGa8g3cix5cWECROUkJCgZ555Rp988omeeOKJHPsGBwebft6Xl5YtW+Z77rNnz2rs2LFat25dnmNdvHhxvufJSW7nc3lJSEgosDlvZuvWrdOjjz6qgIAAubu7q0yZMmrbtq0WLVqUbf89e/bY/104efJktn0iIiLs37OLi4t8fX0VFhamJ554QqtXr3bi2QC4VVgMwzCKOggAAJwlISFBvXv3VkxMjCpXrqyLFy8qOTlZ69at0+rVq1WxYkUtWbJEderUsR+Tnp6u9PR0eXh45Hme7du366677lJ8fLx69eqV5+MuXLggSXJ3d5d0KWlo3ry55s+fr44dO+Z5nGuN7eLFi8rMzJTVai2QuZzhnnvukZubmzZu3HjVvsHBwSpevLiGDRvm0BYYGKgWLVrka+7//vtPpUuX1pgxYxxWZrO7Tmw2mzp27FhgSfDixYt1+vRp++dly5bps88+07Rp01SqVCl7fePGjVWlSpUCmbMwnD9/Xm5ubnJzcyuwMceMGaOYmBhVq1ZNXbt2VaVKlXTs2DEtW7ZM69at05w5c/T444+bjhk9erRmzZqlEydO6O2339aTTz7pMG5ERIR+//13xcbGSpLOnDmjAwcOaNGiRfq///s/derUSZ9++qmKFStWYOcC4NZScP/SAQBwA2vVqpXuvPNO++fo6Gh99913atOmjR5++GHt2bNHnp6eklTgyUB2zp49Ky8vL3uyXVRuhkTh6NGjqlmzZp77ly9fXt27d3diRJcUxnXSrl070+fk5GR99tlnateu3Q3/WEBu8vMLrbxYsGCBYmJi1LFjR82dO9d0XQ8fPlwrV67UxYsXTccYhqG5c+fq8ccf1x9//KE5c+Zkm3RLkp+fn8M1NXHiRA0aNEjvvvuugoODNWnSpAI9JwC3Dm4vBwDctlq0aKGXX35Zhw4d0qeffmqvz+5Z3dWrV+vee++Vv7+/bDabwsLCNGrUKEmXVqfvuusuSVLv3r0dbvmNiIhQrVq19OOPP6pp06by8vKyH3vlM91ZMjIyNGrUKAUEBMjb21sPP/ywDh8+bOoTHByc7ar65WNeLbbsnuk+c+aMhg0bpqCgIFmtVoWFhWnKlCm68uY4i8WiZ599VosXL1atWrVktVoVHh6uFStWZP+FX+Ho0aPq27evypYtKw8PD9WtW1ezZ8+2t2c93/7HH3/om2++scd+8ODBPI2fm169eslms+nvv/9Wu3btZLPZVLp0ab3wwgvKyMiQJB08eFClS5eWJI0bN84+f9aK95XXicVi0ZkzZzR79mx73169emnt2rWyWCz68ssvHeKYO3euLBaLNm/efE3nMWbMGBUrVkz//vuvQ9tTTz0lf39/nT9/XtKl66VNmzZatWqV6tWrJw8PD9WsWTPbW69PnjypIUOG2K+BkJAQTZo0SZmZmaZ+n3/+uRo0aCAfHx/5+vqqdu3aevPNN68a95XPdGd9lwcOHFCvXr3k7+8vPz8/9e7dW2fPnr3qeC+//LJKlCihWbNmZfuLpKioKLVp08ZUt2nTJh08eFBdunRRly5dtGHDBv31119XnSuLq6urZsyYoZo1a+rtt99WSkpKno8FcHsh6QYA3Nayng9etWpVjn1++eUXtWnTRmlpaYqJidHUqVP18MMPa9OmTZKkGjVqKCYmRtKlROeTTz7RJ598oqZNm9rHOHbsmFq1aqV69epp+vTpat68ea5xvfbaa/rmm280YsQIDRo0SKtXr1ZkZKTOnTuXr/PLS2yXMwxDDz/8sKZNm6aWLVvqjTfeUFhYmIYPH66hQ4c69N+4caMGDBigLl26aPLkyTp//rw6dOigY8eO5RrXuXPnFBERoU8++UTdunXT66+/Lj8/P/Xq1cuetNWoUUOffPKJSpUqpXr16tljz0qEc3Lx4kX9999/DuXK7y4jI0NRUVEqWbKkpkyZombNmmnq1Kn64IMPJEmlS5fWzJkzJUnt27e3z//oo49mO+8nn3wiq9Wq++67z9736aefVkREhIKCgjRnzhyHY+bMmaOqVauqUaNGuZ5TTp544gmlp6dr3rx5pvoLFy5owYIF6tChg2lVef/+/ercubNatWql2NhYubm56bHHHjM9m3z27Fk1a9ZMn376qXr06KEZM2aoSZMmio6ONl0Dq1evVteuXVW8eHFNmjRJEydOVEREhP3vxbXo1KmTTp06pdjYWHXq1EkJCQkaN25crsfs379fv/32m9q1aycfH588z5X13d91111q27atvLy89Nlnn+UrXldXV3Xt2lVnz57N0+MPAG5TBgAAt7D4+HhDkvHDDz/k2MfPz8+oX7++/fOYMWOMy/8vctq0aYYk499//81xjB9++MGQZMTHxzu0NWvWzJBkvPfee9m2NWvWzP557dq1hiSjfPnyRmpqqr3+iy++MCQZb775pr2uUqVKRs+ePa86Zm6x9ezZ06hUqZL98+LFiw1Jxquvvmrq17FjR8NisRgHDhyw10ky3N3dTXU7d+40JBlvvfWWw1yXmz59uiHJ+PTTT+11Fy5cMBo1amTYbDbTuVeqVMlo3bp1ruNd3ldStiU2NtZ03pKMmJgY0/H169c3GjRoYP/877//GpKMMWPGOMx15XViGIbh7e2d7c8kOjrasFqtxsmTJ+11R48eNdzc3LIdOyevv/66Icn4448/7HWNGjUyGjZsaOq3aNEiQ5Kxdu1ae13Wd7Nw4UJ7XUpKilGuXDnT9T9+/HjD29vb2Ldvn2nMkSNHGq6ursaff/5pGIZhDB482PD19TXS09PzHH+WK7/TrO+yT58+pn7t27c3SpYsmetYX331lSHJmDZtWp7nv3DhglGyZElj9OjR9rrHH3/cqFu3rkPfZs2aGeHh4TmO9eWXXzr83QSAy7HSDQC47dlstlx3Mff395ckffXVVw631+aV1WpV796989y/R48eplW7jh07qly5clq2bNk1zZ9Xy5Ytk6urqwYNGmSqHzZsmAzD0PLly031kZGRqlq1qv1znTp15Ovrq//7v/+76jwBAQHq2rWrva5YsWIaNGiQTp8+rfXr11/zOTRs2FCrV692KJfPlaV///6mz/fdd99VY78WPXr0UFpamulVcPPmzVN6evp1P3/eo0cPbd26Vb///ru9bs6cOQoKClKzZs1MfQMDA9W+fXv7Z19fX/Xo0UM7duxQcnKyJGn+/Pm67777VLx4cdOdApGRkcrIyNCGDRskXfp7cebMmQLdwTu7n8exY8eUmpqa4zFZbflZ5V6+fLmOHTtmuia6du2qnTt36pdffslXzDabTZIK7E0IAG49JN0AgNve6dOnc/0P9s6dO6tJkyZ68sknVbZsWXXp0kVffPFFvhLw8uXL52vTtGrVqpk+WywWhYSEFMjzzLk5dOiQAgMDHb6PGjVq2NsvV7FiRYcxihcvrhMnTlx1nmrVqsnFxfyfIjnNkx+lSpVSZGSkQ6lUqZKpn4eHh8Ot6nmJ/VpUr15dd911l+kW8zlz5uiee+5RSEjIdY3duXNnWa1W+9gpKSlaunSpunXr5rA3QUhIiENdaGioJNmvrf3792vFihUqXbq0qURGRkq69Cy+JA0YMEChoaFq1aqVKlSooD59+uT5ef6cXHk9FS9eXJJy/Zn4+vpKyl/S++mnn6py5cqyWq06cOCADhw4oKpVq8rLyyvbxwByk7W7fH6SfgC3F3YvBwDc1v766y+lpKTkmvh4enpqw4YNWrt2rb755hutWLFC8+bNU4sWLbRq1Sq5urpedZ6sndEL0pXJU5aMjIw8xVQQcprHuAneSFpY31GWHj16aPDgwfrrr7+UlpamLVu26O23377ucYsXL642bdpozpw5euWVV7RgwQKlpaVd8wp6ZmamHnjgAb344ovZtmcl6WXKlFFiYqJWrlyp5cuXa/ny5YqPj1ePHj1MG+Llx7VcT9WrV5ck7dq1K09zpKam6uuvv9b58+cdfrklXdrc7rXXXsvx79eVdu/eLUnX/csTALcukm4AwG3tk08+kXRpd+PcuLi46P7779f999+vN954QxMmTNDo0aO1du1aRUZG5vk/0PNq//79ps+GYejAgQOm94kXL15cJ0+edDj20KFDpnc25ye2SpUqac2aNTp16pRp5e63336ztxeESpUq6eeff1ZmZqZptbug57le+f255ta/S5cuGjp0qD777DOdO3dOxYoVU+fOna83REmXEvpHHnlEP/zwg+bMmaP69esrPDzcod+BAwdkGIYpzn379kmSfRf7qlWr6vTp0/aV7dy4u7urbdu2atu2rTIzMzVgwAC9//77evnllwstCQ0NDVVYWJi++uorvfnmm/bbvXOyaNEinT9/XjNnzjS961yS9u7dq5deekmbNm3Svffee9W5MzIyNHfuXHl5eeWpP4DbE7eXAwBuW999953Gjx+vypUrq1u3bjn2O378uENdvXr1JElpaWmSJG9vb0nKNgm+Fh9//LHpdtkFCxYoKSlJrVq1stdVrVpVW7Zs0YULF+x1S5cudXi1WH5ie+ihh5SRkeGwAjtt2jRZLBbT/NfjoYceUnJysmnX7fT0dL311luy2WwOzyIXFS8vL0l5/7l6e3vn2LdUqVJq1aqVPv30U82ZM0ctW7Z0SPquVatWrVSqVClNmjRJ69evz3GV+8iRI6ZXl6Wmpurjjz9WvXr1FBAQIOnSDuKbN2/WypUrHY4/efKk0tPTJclhh3oXFxf7L4Wy/l4UlnHjxunYsWN68skn7fFdbtWqVVq6dKmkS7eWV6lSRf3791fHjh1N5YUXXpDNZsvTLeYZGRkaNGiQ9uzZo0GDBtlvcweAK7HSDQC4LSxfvly//fab0tPT9c8//+i7777T6tWrValSJS1ZssT0WqUrxcTEaMOGDWrdurUqVaqko0eP6t1331WFChXsq1tVq1aVv7+/3nvvPfn4+Mjb21sNGzZU5cqVryneEiVK6N5771Xv3r31zz//aPr06QoJCVG/fv3sfZ588kktWLBALVu2VKdOnfT777/r008/NW1slt/Y2rZtq+bNm2v06NE6ePCg6tatq1WrVumrr77SkCFDHMa+Vk899ZTef/999erVSz/++KOCg4O1YMECbdq0SdOnT7+u52P//vtv03vXs9hsNrVr1y5fY3l6eqpmzZqaN2+eQkNDVaJECdWqVUu1atXKtn+DBg20Zs0avfHGGwoMDFTlypXVsGFDe3uPHj3UsWNHSdL48ePzFUtuihUrpi5duujtt9+2v8YqO6Ghoerbt69++OEHlS1bVrNmzdI///yj+Ph4e5/hw4dryZIlatOmjXr16qUGDRrozJkz2rVrlxYsWKCDBw+qVKlSevLJJ3X8+HG1aNFCFSpU0KFDh/TWW2+pXr169mfzC0vnzp21a9cuvfbaa9qxY4e6du2qSpUq6dixY1qxYoW+/fZbzZ07V0eOHNHatWsdNgrMYrVaFRUVpfnz52vGjBn2d36npKTYr6mzZ8/qwIEDWrRokX7//Xd16dKlQH+WAG5BRbp3OgAATpb1yrCs4u7ubgQEBBgPPPCA8eabb5peTZXlyldBffvtt8YjjzxiBAYGGu7u7kZgYKDRtWtXh1cqffXVV0bNmjUNNzc30yu6cnvlUE6vDPvss8+M6Ohoo0yZMoanp6fRunVr49ChQw7HT5061ShfvrxhtVqNJk2aGNu3b3cYM7fYrnxlmGEYxqlTp4znn3/eCAwMNIoVK2ZUq1bNeP31143MzExTP0nGwIEDHWLK6VVmV/rnn3+M3r17G6VKlTLc3d2N2rVrZ/tas4J6Zdjl59mzZ0/D29vb4fjsXgP2v//9z2jQoIHh7u5uetVVdn1/++03o2nTpoanp6chyeF7SEtLM4oXL274+fkZ586dy9M5XS67V4Zl2bZtmyHJePDBB7M9Nut7XLlypVGnTh3DarUa1atXN+bPn+/Q99SpU0Z0dLQREhJiuLu7G6VKlTIaN25sTJkyxbhw4YJhGIaxYMEC48EHHzTKlCljuLu7GxUrVjSefvppIykp6arnoRxeGXbla/my/v5md77Zyfq7WqZMGcPNzc0oXbq00bZtW+Orr74yDOPS3xdJxrfffpvjGAkJCYYk+zFZr/zLKjabzahWrZrRvXt3Y9WqVXmKC8DtzWIYN8FOJwAAALeA9PR0BQYGqm3btoqLiyvQsXfu3Kl69erp448/1hNPPOHQHhwcrFq1atlvswYAFA6e6QYAACgkixcv1r///qsePXoU+NgffvihbDabHn300QIfGwBw7XimGwAAwMm2bt2qn3/+WePHj1f9+vULdKO4r7/+Wr/++qs++OADPfvss/aN8wAANwaSbgAAACebOXOmPv30U9WrV08JCQkFOvZzzz2nf/75Rw899JDGjRtXoGMDAK4fz3QDAAAAAOAkPNMNAAAAAICTkHQDAAAAAOAkPNON20JmZqaOHDkiHx8fWSyWog4HAAAAwE3OMAydOnVKgYGBcnHJeT2bpBu3hSNHjigoKKiowwAAAABwizl8+LAqVKiQYztJN24LPj4+ki79hfD19S3iaAAAAADc7FJTUxUUFGTPNXJC0o3bQtYt5b6+viTdAAAAAArM1R5fJenGbeXR+s/KzdW9qMMAAAAAkA8r9n1U1CFcM3YvBwAAAADASUi6AQAAAABwEpJuAAAAAACchKQbAAAAAAAnIekGAAAAAMBJSLoBAAAAAHASkm4AAAAAAJyEpBuSpF69eqldu3b2P1ssFk2cONHUZ/HixQ4vfv/www9Vt25d2Ww2+fv7q379+oqNjbW3jx07VhaLxaFUr17d3iciIsJe7+HhodDQUMXGxsowjByPv7wAAAAAwI3KragDwI3Jw8NDkyZN0tNPP63ixYtn22fWrFkaMmSIZsyYoWbNmiktLU0///yzdu/ebeoXHh6uNWvWmOrc3MyXXr9+/RQTE6O0tDR99913euqpp+Tv768XXnhB/fv3t/e766679NRTT6lfv34FdKYAAAAA4Dwk3chWZGSkDhw4oNjYWE2ePDnbPkuWLFGnTp3Ut29fe114eLhDPzc3NwUEBOQ6n5eXl71P79699fbbb2v16tV65plnZLPZ7P1cXV3l4+Nz1fEAAAAA4EbA7eXIlqurqyZMmKC33npLf/31V7Z9AgICtGXLFh06dKjA5jUMQ99//71+++03ubu7F9i4AAAAAFAUSLqRo/bt26tevXoaM2ZMtu1jxoyRv7+/goODFRYWpl69eumLL75QZmamqd+uXbtks9lM5fJbxiXp3Xfflc1mk9VqVdOmTZWZmalBgwZdc+xpaWlKTU01FQAAAAAobNxejlxNmjRJLVq00AsvvODQVq5cOW3evFm7d+/Whg0b9L///U89e/bURx99pBUrVsjF5dLvdMLCwrRkyRLTsb6+vqbP3bp10+jRo3XixAmNGTNGjRs3VuPGja857tjYWI0bN+6ajwcAAACAgsBKN3LVtGlTRUVFKTo6Osc+tWrV0oABA/Tpp59q9erVWr16tdavX29vd3d3V0hIiKmUKVPGNIafn59CQkJ011136YsvvtDbb7/tsPlafkRHRyslJcVeDh8+fM1jAQAAAMC1YqUbVzVx4kTVq1dPYWFhV+1bs2ZNSdKZM2eueT6bzabBgwfrhRde0I4dO67ptWBWq1VWq/WaYwAAAACAgsBKN66qdu3a6tatm2bMmGGqf+aZZzR+/Hht2rRJhw4d0pYtW9SjRw+VLl1ajRo1svdLT09XcnKyqfzzzz+5zvn0009r3759WrhwoVPOCQAAAAAKA0k38iQmJsZhg7TIyEht2bJFjz32mEJDQ9WhQwd5eHjo22+/VcmSJe39fvnlF5UrV85UKlWqlOt8JUqUUI8ePTR27FiHeQEAAADgZmExDMMo6iAAZ0tNTZWfn5/ur/KE3Fx5FRkAAABwM1mx76OiDsFBVo6RkpLisFH05VjpBgAAAADASUi6AQAAAABwEpJuAAAAAACchKQbAAAAAAAnIekGAAAAAMBJSLoBAAAAAHASt6IOAChMi3a8net2/gAAAABQkFjpBgAAAADASUi6AQAAAABwEpJuAAAAAACchKQbAAAAAAAnIekGAAAAAMBJSLoBAAAAAHASXhmG28pjD76sYm7Wog4DAG4qSzdOLuoQAAC4abHSDQAAAACAk5B0AwAAAADgJCTdAAAAAAA4CUk3AAAAAABOQtINAAAAAICTkHQDAAAAAOAkJN1FpFevXmrXrp2prly5cpo4caKpbuTIkbJYLFq3bp2pPiIiQk888YQkKSEhQRaLRRaLRS4uLqpQoYJ69+6to0ePmo5ZunSpmjVrJh8fH3l5eemuu+5SQkKCqc/BgwdlsViUmJhor/vkk0/k7e2tAwcOmPoeOXJExYsX19tvvy1JCg4O1vTp07M93yvHzfpcpkwZnTp1ytS3Xr16Gjt2rOlcs87v8tK/f/9s5wIAAACAGwVJ9w0kIiLCIbleu3atgoKCTPXnz5/Xli1b1KJFC3udr6+vkpKS9Ndff+nDDz/U8uXL7Um5JL311lt65JFH1KRJE23dulU///yzunTpov79++uFF17INa4nnnhCUVFR6tWrlzIzM+31/fr1U4MGDTRw4MBrPudTp05pypQpV+3Xr18/JSUlmcrkybw3FgAAAMCNjaT7BtK8eXNt2rRJ6enpki4lpDt27NCIESNMSffmzZuVlpam5s2b2+ssFosCAgIUGBioVq1aadCgQVqzZo3OnTunw4cPa9iwYRoyZIgmTJigmjVrKiQkRMOGDdPrr7+uqVOnauvWrbnG9v7772vfvn164403JF1aXd+0aZPi4+NlsViu+Zyfe+45vfHGGw6r8lfy8vJSQECAqfj6+l7zvAAAAABQGEi6byDNmzfX6dOn9cMPP0iSvv/+e4WGhqpDhw7aunWrzp8/L+nS6ndwcLCCg4NzHMvT01OZmZlKT0/XggULdPHixWxXtJ9++mnZbDZ99tlnucZWunRpffDBB3r55Ze1evVqPf/883rzzTcVFBR07ScsqWvXrgoJCVFMTMx1jQMAAAAANyKS7htItWrVVL58efuq9rp169SsWTMFBASoYsWK2rx5s73+8lXuK+3fv1/vvfee7rzzTvn4+Gjfvn3y8/NTuXLlHPq6u7urSpUq2rdv31Xja9eunTp16qSWLVuqWbNm6tmz57Wd6GUsFosmTpyoDz74QL///nuO/d59913ZbDZTmTNnTo7909LSlJqaaioAAAAAUNhIum8wlz/XvW7dOkVEREiSmjVrpnXr1uncuXPaunWrQ9KdkpIim80mLy8vhYWFqWzZsrkmpdfq5ZdfVmZmpl566aUCGzMqKkr33nuvXn755Rz7dOvWTYmJiaby8MMP59g/NjZWfn5+9nK9K/IAAAAAcC1Ium8wWc91Hzt2TDt27FCzZs0kXUq6165dq//973+6cOGCaRM1SfLx8VFiYqJ2796tM2fOaMOGDQoNDZUkhYaGKiUlRUeOHHGY78KFC/r999/tfa/Gzc3N9L8FZeLEiZo3b5527NiRbbufn59CQkJMxcfHJ8fxoqOjlZKSYi+HDx8u0HgBAAAAIC9Ium8wzZs315kzZ/TGG2+oWrVqKlOmjCSpadOm2rZtm5YvX26/Df1yLi4uCgkJUZUqVeTp6Wlq69Chg4oVK6apU6c6zPfee+/pzJkz6tq1q/NOKg/uvvtuPfrooxo5cmSBjGe1WuXr62sqAAAAAFDYCna5EvmSkpJieh+2JJUsWVIVK1bUW2+9pW7dutnrg4KCFBgYqA8++CDfCXLFihU1efJkDRs2TB4eHnriiSdUrFgxffXVVxo1apSGDRumhg0bmo7Zu3evwzjh4eFXnevvv/92OKdKlSrlKc7XXntN4eHh2a6inz17VsnJyaY6q9Wq4sWL52lsAAAAACgKJN1FaN26dapfv76prm/fvmrevLlmz55tf547S7NmzZSQkJDrJmo5GTJkiKpUqaIpU6bozTffVEZGhsLDwzVz5kz17t3boX+XLl0c6vJyi/aUKVMc3rv9ySef6N57773qsaGhoerTp48++OADh7YPP/xQH374oakuKipKK1asuOq4AAAAAFBULIZhGEUdBOBsqamp8vPz04MNB6mYm7WowwGAm8rSjZOLOgQAAG44WTlGSkpKro+z8kw3AAAAAABOQtINAAAAAICTkHQDAAAAAOAkJN0AAAAAADgJSTcAAAAAAE5C0g0AAAAAgJPwnm7cVuavGp/rdv4AAAAAUJBY6QYAAAAAwElIugEAAAAAcBKSbgAAAAAAnISkGwAAAAAAJyHpBgAAAADASUi6AQAAAABwEl4ZhttKhy6xKlbMWtRhAJKkZV+NLeoQAAAA4GSsdAMAAAAA4CQk3QAAAAAAOAlJNwAAAAAATkLSDQAAAACAk5B0AwAAAADgJCTdAAAAAAA4CUk3AAAAAABOQtINk169eslisah///4ObQMHDpTFYrH3ya2MHTtWBw8ezLF9y5YtkqQRI0YoODhYp06dMs3Vtm1bNW3aVK+88spV5wIAAACAGxVJNxwEBQXp888/17lz5+x158+f19y5c1WxYkVJUlJSkr1Mnz5dvr6+proXXnjBfuyaNWtMbUlJSWrQoIEkKSYmRjabTUOHDrX3nzVrltauXav4+Hi9+OKLpuMqVKigmJgYUx0AAAAA3KjcijoA3HjuuOMO/f7771q0aJG6desmSVq0aJEqVqyoypUrS5ICAgLs/f38/GSxWEx1kvTff/9JkkqWLOnQlsVqtWr27Nlq1KiROnTooJo1a+r555/X5MmTVbVqVUmSzWaz93d1dZWPj0+O4wEAAADAjYSVbmSrT58+io+Pt3+eNWuWevfu7ZS5GjRooOjoaD355JN64okndPfdd+uZZ565rjHT0tKUmppqKgAAAABQ2Ei6ka3u3btr48aNOnTokA4dOqRNmzape/fu1zRW48aNZbPZTOVKL730klxcXLR161bFxcVd97PasbGx8vPzs5egoKDrGg8AAAAArgW3lyNbpUuXVuvWrZWQkCDDMNS6dWuVKlXqmsaaN2+eatSokWuf1atXKzk5WZL0ww8/2J8dv1bR0dGm58RTU1NJvAEAAAAUOpJu5KhPnz569tlnJUnvvPPONY8TFBSkkJCQHNtPnDihfv366aWXXpJhGBowYICaNWt2zUm+dOlZcavVes3HAwAAAEBB4PZy5Khly5a6cOGCLl68qKioKKfN89xzzykgIECjRo3S6NGjVb58eQ0cONBp8wEAAABAYWGlGzlydXXVnj177H++VseOHbPfOp7F399fHh4e+vLLLzV//nz9+OOPcnO7dDnOnj1bd955pxYuXKgOHTpc+wkAAAAAQBFjpRu58vX1la+v73WNERkZqXLlypnK4sWL9d9//6l///4aM2aMatWqZe9fu3ZtjRkzRgMGDLC/dgwAAAAAbkYWwzCMog4CcLbU1FT5+fkpstVIFSvGs964MSz7amxRhwAAAIBrlJVjpKSk5LpQyUo3AAAAAABOQtINAAAAAICTkHQDAAAAAOAkJN0AAAAAADgJSTcAAAAAAE5C0g0AAAAAgJO4FXUAQGFa+Hn0db93HAAAAADyipVuAAAAAACchKQbAAAAAAAnIekGAAAAAMBJSLoBAAAAAHASkm4AAAAAAJyE3ctxW2nz1CS5uXsUdRhO893HLxd1CAAAAAAuw0o3AAAAAABOQtINAAAAAICTkHQDAAAAAOAkJN0AAAAAADgJSTcAAAAAAE5C0g0AAAAAgJOQdAMAAAAA4CQk3TDp1auX2rVr51C/bt06WSwWnTx50lRfvXp1Wa1WJScnm/rlVtatWydJWrhwoSIiIuTn5yebzaY6deooJiZGx48flyQlJCTI398/2zgtFosWL15cQGcNAAAAAM5B0o1rtnHjRp07d04dO3bU7NmzJUmNGzdWUlKSvXTq1EktW7Y01TVu3FijR49W586dddddd2n58uXavXu3pk6dqp07d+qTTz4p4jMDAAAAgILhVtQB4OYVFxenxx9/XM2aNdPgwYM1YsQIubu7KyAgwN7H09NTaWlpprpt27ZpwoQJmj59ugYPHmyvDw4O1gMPPOCwmg4AAAAANyuSblyTU6dOaf78+dq6dauqV6+ulJQUff/997rvvvuueuycOXNks9k0YMCAbNtzuqU8P9LS0pSWlmb/nJqaet1jAgAAAEB+kXTDwdKlS2Wz2Ux1GRkZps+ff/65qlWrpvDwcElSly5dFBcXl6eke//+/apSpYqKFSt21b4pKSkOseRFbGysxo0bl+/jAAAAAKAgkXTDQfPmzTVz5kxT3datW9W9e3f751mzZpk+d+/eXc2aNdNbb70lHx+fXMc3DCPPsfj4+Oinn35yqK9WrVqux0VHR2vo0KH2z6mpqQoKCsrzvAAAAABQEEi64cDb21shISGmur/++sv+519//VVbtmzRtm3bNGLECHt9RkaGPv/8c/Xr1y/X8UNDQ7Vx40ZdvHjxqqvdLi4uDrHkhdVqldVqzfdxAAAAAFCQ2L0c+RYXF6emTZtq586dSkxMtJehQ4cqLi7uqsc//vjjOn36tN59991s29lIDQAAAMCtgpVu5MvFixf1ySefKCYmRrVq1TK1Pfnkk3rjjTf0yy+/2J/1zk7Dhg314osvatiwYfr777/Vvn17BQYG6sCBA3rvvfd07733mnY1BwAAAICbFSvdyJcNGzbo2LFjat++vUNbjRo1VKNGjTytdk+aNElz587V1q1bFRUVpfDwcA0dOlR16tRRz549nRE6AAAAABQ6i5GfXa2Am1Rqaqr8/Px0X+dRcnP3KOpwnOa7j18u6hAAAACA20JWjpGSkiJfX98c+7HSDQAAAACAk5B0AwAAAADgJCTdAAAAAAA4CUk3AAAAAABOQtINAAAAAICTkHQDAAAAAOAkbkUdAFCYln4wItft/AEAAACgILHSDQAAAACAk5B0AwAAAADgJCTdAAAAAAA4CUk3AAAAAABOQtINAAAAAICTsHs5bisPDJskN3ePog4jzza983JRhwAAAADgOrDSDQAAAACAk5B0AwAAAADgJCTdAAAAAAA4CUk3AAAAAABOQtINAAAAAICTkHQDAAAAAOAkJN0AAAAAADgJSTcKVK9evdSuXbts24KDgzV9+nTTZ4vFoi1btpj6DRkyRBEREfbPY8eOlcVicSjVq1d3whkAAAAAQMFxK+oAcHvz8PDQiBEjtH79+lz7hYeHa82aNaY6NzcuXwAAAAA3Nla6UaSeeuopbdmyRcuWLcu1n5ubmwICAkylVKlShRQlAAAAAFwbkm4UqcqVK6t///6Kjo5WZmZmgY2blpam1NRUUwEAAACAwkbSjSL30ksv6Y8//tCcOXNy7LNr1y7ZbDZT6d+/f479Y2Nj5efnZy9BQUHOCB0AAAAAcsVDsShypUuX1gsvvKBXXnlFnTt3zrZPWFiYlixZYqrz9fXNcczo6GgNHTrU/jk1NZXEGwAAAEChI+nGDWHo0KF699139e6772bb7u7urpCQkDyPZ7VaZbVaCyo8AAAAALgm3F6OG4LNZtPLL7+s1157TadOnSrqcAAAAACgQLDSjQKXkpKixMREU13JkiWvetxTTz2ladOmae7cuWrYsKGpLT09XcnJyaY6i8WismXLXne8AAAAAOAsJN0ocOvWrVP9+vVNdX379r3qccWKFdP48eP1+OOPO7T98ssvKleunKnOarXq/Pnz1xcsAAAAADiRxTAMo6iDAJwtNTVVfn5+uvvJUXJz9yjqcPJs0zsvF3UIAAAAALKRlWOkpKTkuskzz3QDAAAAAOAkJN0AAAAAADgJSTcAAAAAAE5C0g0AAAAAgJOQdAMAAAAA4CQk3QAAAAAAOAnv6cZtZfXUEblu5w8AAAAABYmVbgAAAAAAnISkGwAAAAAAJyHpBgAAAADASUi6AQAAAABwEpJuAAAAAACchN3LcVuJeGWiXK0eRR3GVf0w6ZWiDgEAAABAAWClGwAAAAAAJyHpBgAAAADASUi6AQAAAABwEpJuAAAAAACchKQbAAAAAAAnueak+8CBA1q5cqXOnTsnSTIMo8CCAgAAAADgVpDvpPvYsWOKjIxUaGioHnroISUlJUmS+vbtq2HDhhV4gAAAAAAA3KzynXQ///zzcnNz059//ikvLy97fefOnbVixYoCDQ5516tXL1ksFlksFrm7uyskJEQxMTFKT0+XJGVkZGjatGmqXbu2PDw8VLx4cbVq1UqbNm0yjZOQkCB/f39TXd++fVW7dm1duHDBVL9s2TK5u7vrp59+0sGDB2WxWJSYmJhtfFeOm5CQIIvFopYtW5r6nTx5UhaLRevWrbPXZZ3XleXzzz/P35cEAAAAAIUs30n3qlWrNGnSJFWoUMFUX61aNR06dKjAAkP+tWzZUklJSdq/f7+GDRumsWPH6vXXX5dhGOrSpYtiYmI0ePBg7dmzR+vWrVNQUJAiIiK0ePHiXMedNm2aTp06pTFjxtjrTp48qX79+unll1/WHXfccU3xurm5ac2aNVq7du1V+8bHxyspKclU2rVrd03zAgAAAEBhccvvAWfOnDGtcGc5fvy4rFZrgQSFa2O1WhUQECBJeuaZZ/Tll19qyZIlqlKlihYsWKAlS5aobdu29v4ffPCBjh07pieffFIPPPCAvL29sx3X19dX8fHxioqKUrt27dSwYUMNGTJE5cuXV3R09DXH6+3trU6dOmnkyJHaunVrrn39/f3t5wYAAAAAN4t8r3Tfd999+vjjj+2fLRaLMjMzNXnyZDVv3rxAg8P18fT01IULFzR37lyFhoaaEu4sw4YN07Fjx7R69epcx2revLkGDBignj17av78+friiy/08ccfy80t37+3MRk7dqx27dqlBQsWXNc4V0pLS1NqaqqpAAAAAEBhy3fSPXnyZH3wwQdq1aqVLly4oBdffFG1atXShg0bNGnSJGfEiHwyDENr1qzRypUr1aJFC+3bt081atTItm9W/b59+646bmxsrCSpS5cumjBhgqpXr37dsQYGBmrw4MEaPXq0/fnz7HTt2lU2m81U/vzzz1xj9fPzs5egoKDrjhUAAAAA8ivfSXetWrW0b98+3XvvvXrkkUd05swZPfroo9qxY4eqVq3qjBiRR0uXLpXNZpOHh4datWqlzp07a+zYsZIK5pVunp6eeuGFF+Tl5aXBgwdf93hZRowYoX///VezZs3Ksc+0adOUmJhoKoGBgTn2j46OVkpKir0cPny4wOIFAAAAgLy6pnuD/fz8NHr06IKOBdepefPmmjlzptzd3RUYGGi/9Ts0NFR79uzJ9pis+tDQ0DzN4ebmJldXV1ksloIJWpee146Ojta4cePUpk2bbPsEBAQoJCQkz2NarVb2GAAAAABQ5PK90i1JJ06c0JQpU9S3b1/17dtXU6dO1fHjxws6NuSTt7e3QkJCVLFiRdOz1l26dNH+/fv19ddfOxwzdepUlSxZUg888EBhhurgueeek4uLi958880ijQMAAAAAClK+V7o3bNigtm3bys/PT3feeackacaMGYqJidHXX3+tpk2bFniQuD5dunTR/Pnz1bNnT73++uu6//77lZqaqnfeeUdLlizR/PnzTTuXZ2RkOLxv22q15vhc+OX27t3rUBceHn7V4zw8PDRu3DgNHDgw2/aTJ08qOTnZVOfj45PjjusAAAAAcCPId9I9cOBAde7cWTNnzpSrq6ukS0nagAEDNHDgQO3atavAg8T1sVgs+uKLLzR9+nRNmzZNAwYMkIeHhxo1aqR169apSZMmpv6nT59W/fr1TXVVq1bVgQMHrjpXly5dHOry+jx1z549NXXqVP36668Obb1793aoi42N1ciRI/M0NgAAAAAUBYuRzx22PD09lZiYqLCwMFP93r17Va9ePZ07d65AAwQKQmpqqvz8/FR/cLRcrR5FHc5V/TDplaIOAQAAAEAusnKMlJQU+fr65tgv389033HHHdluyrVnzx7VrVs3v8MBAAAAAHDLyvft5YMGDdLgwYN14MAB3XPPPZKkLVu26J133tHEiRP1888/2/vWqVOn4CIFAAAAAOAmk+/by11ccl8ct1gsMgxDFotFGRkZ1xUcUFC4vRwAAABAQcrr7eX5Xun+448/riswAAAAAABuF/lOukuVKsVrmgAAAAAAyIN8b6RWtmxZ9enTRxs3bnRGPAAAAAAA3DLy/Uz34sWLlZCQoGXLlik4OFh9+vRRjx49FBgY6KwYgeuW1+ctAAAAACAvnPbKsHbt2mnx4sX6+++/1b9/f82dO1eVKlVSmzZttGjRIqWnp19X4AAAAAAA3CryvdKdnbfeekvDhw/XhQsXVKpUKfXv318jR46Ul5dXQcQIXDdWugEAAAAUJKftXp7ln3/+0ezZs5WQkKBDhw6pY8eO6tu3r/766y9NmjRJW7Zs0apVq651eAAAAAAAbnp5TrpjYmL0wgsvaMWKFYqPj9fKlStVs2ZNDRgwQN27d5e/v7+9b+PGjVWjRg1nxAsAAAAAwE0jz890jxs3TqdPn1bv3r0VGBioTZs2KTExUc8++6wp4ZakwMBAjR49uqBjBQAAAADgppLnZ7pdXFyUnJwsm83Gs9q46WQ9b1Fr9Ei5eliz7ZP40tjCDQoAAADATcspu5dbLBYSbgAAAAAA8ihfG6mFhobKYrHk2uf48ePXFRAAAAAAALeKfCXd48aNk5+fn7NiAQAAAADglpKvpLtLly4qU6aMs2IBAAAAAOCWkudnuq92WzkAAAAAADDLc9Kdx03OAQAAAADA/y/Pt5dnZmY6Mw4AAAAAAG45+XplGG5tycnJeu6551SlShVZrVYFBQWpbdu2+vbbbyVJwcHBmj59ur1/cHCwLBaL/VVytWvX1kcffeQw7tq1a/XQQw+pZMmS8vLyUs2aNTVs2DD9/fffkqR169bJYrHo5MmT2cY1duxY+zyXl+rVqxf4dwAAAAAABYmkG5KkgwcPqkGDBvruu+/0+uuva9euXVqxYoWaN2+ugQMH5nhcTEyMkpKStHv3bnXv3l39+vXT8uXL7e3vv/++IiMjFRAQoIULF+rXX3/Ve++9p5SUFE2dOjXP8YWHhyspKclUNm7ceF3nDAAAAADOlq/dy3HrGjBggCwWi7Zt2yZvb297fXh4uPr06ZPjcT4+PgoICJAkjRgxQpMnT9bq1avVqlUr/fXXXxo0aJAGDRqkadOm2Y8JDg5W06ZNc1zZzo6bm5t9HgAAAAC4WeR7pfvMmTPOiANF6Pjx41qxYoUGDhxoSriz+Pv7X3WMzMxMLVy4UCdOnJC7u7skaf78+bpw4YJefPHFbI/Jy7gAAAAAcDPLd9JdtmxZ9enTh1t7byEHDhyQYRjX9Iz0iBEjZLPZZLVa1bFjRxUvXlxPPvmkJGn//v3y9fVVuXLlrjvGXbt2yWazmUr//v1z7J+WlqbU1FRTAQAAAIDClu+k+9NPP9Xx48fVokULhYaGauLEiTpy5IgzYkMhuZ7XwQ0fPlyJiYn67rvv1LBhQ02bNk0hISH2cQvq/e5hYWFKTEw0lZiYmBz7x8bGys/Pz16CgoIKJA4AAAAAyI98J93t2rXT4sWL9ffff6t///6aO3euKlWqpDZt2mjRokVKT093RpxwomrVqslisei3337L97GlSpVSSEiI7rvvPs2fP1+DBg3Sr7/+KkkKDQ1VSkqKkpKSrjtGd3d3hYSEmEqZMmVy7B8dHa2UlBR7OXz48HXHAAAAAAD5dc27l5cuXVpDhw7Vzz//rDfeeENr1qxRx44dFRgYqFdeeUVnz54tyDjhRCVKlFBUVJTeeeedbJ/Zz+uGZ0FBQercubOio6MlSR07dpS7u7smT56cbf/8bKSWX1arVb6+vqYCAAAAAIXtmncv/+effzR79mwlJCTo0KFD6tixo/r27au//vpLkyZN0pYtW7Rq1aqCjBVO9M4776hJkya6++67FRMTozp16ig9PV2rV6/WzJkztWfPnjyNM3jwYNWqVUvbt2/XnXfeqWnTpunZZ59VamqqevTooeDgYP3111/6+OOPZbPZTK8N27Vrl3x8fOyfLRaL6tatK0lKT09XcnKyaS6LxaKyZcsWwNkDAAAAgHPkO+letGiR4uPjtXLlStWsWVMDBgxQ9+7dTTtRN27cWDVq1CjIOOFkVapU0U8//aTXXntNw4YNU1JSkkqXLq0GDRpo5syZeR6nZs2aevDBB/XKK69o2bJlGjBggEJDQzVlyhS1b99e586dU3BwsNq0aaOhQ4eajm3atKnps6urq/1xhV9++cVhQzar1arz589f4xkDAAAAgPNZjHzuouXn56cuXbroySef1F133ZVtn3Pnzmny5MkaM2ZMgQQJXK/U1FT5+fmp1uiRcvWwZtsn8aWxhRsUAAAAgJtWVo6RkpKS6+Os+V7pTkpKkpeXV659PD09SbgBAAAAALe9fG+k5uPjo6NHjzrUHzt2TK6urgUSFAAAAAAAt4J8J9053Y2elpYmd3f36w4IAAAAAIBbRZ5vL58xY4akSztGf/TRR7LZbPa2jIwMbdiwQdWrVy/4CAEAAAAAuEnlOemeNm2apEsr3e+9957pVnJ3d3cFBwfrvffeK/gIAQAAAAC4SeU56f7jjz8kSc2bN9eiRYtUvHhxpwUFAAAAAMCtIN+vDANuRnndzh8AAAAA8qJAXxk2dOhQjR8/Xt7e3ho6dGiufd944438RQoAAAAAwC0qT0n3jh07dPHiRUnSTz/9JIvF4tSgAAAAAAC4FXB7OW4L3F4OAAAAoCDlNcfI93u6+/Tpo1OnTjnUnzlzRn369MnvcAAAAAAA3LLynXTPnj1b586dc6g/d+6cPv744wIJCgAAAACAW0GeXxmWmpoqwzBkGIZOnTolDw8Pe1tGRoaWLVumMmXKOCVIoKA0eW+CXD2skqTEQeOKOBoAAAAAt7o8J93+/v6yWCyyWCwKDQ11aLdYLBo3jiQGAAAAAIAseU66165dK8Mw1KJFCy1cuFAlSpSwt7m7u6tSpUoKDAx0SpAAAAAAANyM8px0N2vWTJL0xx9/KCgoSC4u+X4cHAAAAACA20qek+4slSpV0smTJ7Vt2zYdPXpUmZmZpvYePXoUWHAAAAAAANzM8p10f/311+rWrZtOnz4tX19fWSwWe5vFYiHpBgAAAADg/5fve8SHDRumPn366PTp0zp58qROnDhhL8ePH3dGjAAAAAAA3JTynXT//fffGjRokLy8vJwRDwAAAAAAt4x8J91RUVHavn27M2K5LR0+fFh9+vRRYGCgfRf4wYMH69ixY/Y+ERERGjJkSI5jZL3K7cry+eefS5LWrVunRx55ROXKlZO3t7fq1aunOXPmmMYYO3ZstmOsWbPGod3V1VVBQUF66qmnsr274dy5cypRooRKlSqltLQ0h/bg4GBTfJcLDw+XxWJRQkKCQ/8ry8SJE3P9bgEAAACgqOX7me7WrVtr+PDh+vXXX1W7dm0VK1bM1P7www8XWHC3uv/7v/9To0aNFBoaqs8++0yVK1fWL7/8ouHDh2v58uXasmWL6dVsuYmPj1fLli1Ndf7+/pKk//3vf6pTp45GjBihsmXLaunSperRo4f8/PzUpk0be//w8HB7kp3l8vmz2jMyMrRnzx716dNHKSkpmjdvnumYhQsXKjw8XIZhaPHixercubNDvEFBQYqPj1eXLl3sdVu2bFFycrK8vb0d+sfExKhfv36mOh8fn6t8KwAAAABQtPKddGclPjExMQ5tFotFGRkZ1x/VbWLgwIFyd3fXqlWr5OnpKUmqWLGi6tevr6pVq2r06NGaOXNmnsby9/dXQEBAtm2jRo0yfR48eLBWrVqlRYsWmZJuNze3HMe4sr18+fJ67LHHFB8f79AvLi5O3bt3l2EYiouLyzbp7tatm6ZNm6bDhw8rKChIkjRr1ix169ZNH3/8sUN/Hx+fXGMDAAAAgBtRvm8vz8zMzLGQcOfd8ePHtXLlSg0YMMCecGcJCAhQt27dNG/ePBmG4ZT5U1JS8ryKnp2DBw9q5cqVcnd3N9X//vvv2rx5szp16qROnTrp+++/16FDhxyOL1u2rKKiojR79mxJ0tmzZzVv3jz16dPnmmMCAAAAgBtNvpNuFIz9+/fLMAzVqFEj2/YaNWroxIkT+vfff/M0XteuXWWz2Uzlzz//zLbvF198oR9++EG9e/c21e/atct0/N13351tu6enp/1W+BEjRpj6zJo1S61atVLx4sVVokQJRUVFZbsaLkl9+vRRQkKCDMPQggULVLVqVdWrVy/bviNGjHA4v++//z7H7yMtLU2pqammAgAAAACFLc9J90MPPaSUlBT754kTJ+rkyZP2z8eOHVPNmjULNLjbQUGtZE+bNk2JiYmmEhgY6NBv7dq16t27tz788EOFh4eb2sLCwkzHL1y4MNv2H374QSNGjFBUVJSee+45e3tGRoZmz56t7t272+u6d++uhIQEZWZmOsTSunVrnT59Whs2bNCsWbNyXeUePny4w/ndeeedOfaPjY2Vn5+fvWTdwg4AAAAAhSnPSffKlStNO1FPmDDBtHN1enq69u7dW7DR3cJCQkJksVi0Z8+ebNv37Nmj4sWLq3Tp0nkaLyAgQCEhIabi5mZ+ZH/9+vVq27atpk2bph49ejiM4e7ubjr+ykQ1q71WrVqaOHGiXF1dNW7cOHv7ypUr9ffff6tz585yc3OTm5ubunTpokOHDunbb791mM/NzU1PPPGExowZo61bt6pbt245nl+pUqUczu/K2/IvFx0drZSUFHs5fPhwjn0BAAAAwFnynHRfuSLrrGeNbxclS5bUAw88oHfffVfnzp0ztSUnJ2vOnDnq3LmzLBZLgcy3bt06tW7dWpMmTdJTTz1VIGO+9NJLmjJlio4cOSLp0gZqXbp0cViR7tKli+Li4rIdo0+fPlq/fr0eeeQRFS9evEDikiSr1SpfX19TAQAAAIDClu/dy1Fw3n77bTVu3FhRUVF69dVXTa8MK1++vF577TV733///VeJiYmm48uVK6eyZctKkk6ePKnk5GRTu4+Pj7y9vbV27Vq1adNGgwcPVocOHez93N3dr2sztUaNGqlOnTqaMGGCxowZo6+//lpLlixRrVq1TP169Oih9u3b6/jx4w7z1ahRQ//995+8vLxynevUqVMO5+fl5UUyDQAAAOCGlueVbovF4rDqWlCrsLeratWqafv27apSpYo6deqkqlWr6qmnnlLz5s21efNmU4I6d+5c1a9f31Q+/PBDe3vv3r1Vrlw5U3nrrbckSbNnz9bZs2cVGxtran/00Uev+xyef/55ffTRR3r33Xfl7e2t+++/36HP/fffL09PT3366afZjlGyZMlcbxWXpFdeecXh/F588cXrjh8AAAAAnMli5PE+cRcXF7Vq1UpWq1WS9PXXX6tFixby9vaWdGm36BUrVvDaMNyQUlNT5efnp1qTRsjV49I1nDho3FWOAgAAAIDsZeUYKSkpud6Bm+fby3v27Gn6fPkO1Vmy25wLAAAAAIDbVZ6T7pzetQwAAAAAALKX52e6AQAAAABA/pB0AwAAAADgJCTdAAAAAAA4CUk3AAAAAABOkueN1IBbwab+o3Ldzh8AAAAAChIr3QAAAAAAOAlJNwAAAAAATkLSDQAAAACAk5B0AwAAAADgJCTdAAAAAAA4CUk3AAAAAABOQtINAAAAAICTkHQDAAAAAOAkJN0AAAAAADgJSTcAAAAAAE5C0g0AAAAAgJOQdAMAAAAA4CQk3QAAAAAAOAlJN5xq8+bNcnV1VevWrU31Bw8elMVisZcSJUqoWbNm+v777039DMPQBx98oIYNG8pms8nf31933nmnpk+frrNnzxbmqQAAAABAvpF0w6ni4uL03HPPacOGDTpy5IhD+5o1a5SUlKQNGzYoMDBQbdq00T///GNvf+KJJzRkyBA98sgjWrt2rRITE/Xyyy/rq6++0qpVqwrzVAAAAAAg39yKOgDcuk6fPq158+Zp+/btSk5OVkJCgkaNGmXqU7JkSQUEBCggIECjRo3S559/rq1bt+rhhx/WF198oTlz5mjx4sV65JFH7McEBwfr4YcfVmpqamGfEgAAAADkCyvdcJovvvhC1atXV1hYmLp3765Zs2bJMIxs+547d04ff/yxJMnd3V2SNGfOHIWFhZkS7iwWi0V+fn7OCx4AAAAACgAr3XCauLg4de/eXZLUsmVLpaSkaP369YqIiLD3ady4sVxcXHT27FkZhqEGDRro/vvvlyTt379fYWFh1zR3Wlqa0tLS7J9ZFQcAAABQFFjphlPs3btX27ZtU9euXSVJbm5u6ty5s+Li4kz95s2bpx07dmjhwoUKCQlRQkKCihUrJkk5rornRWxsrPz8/OwlKCjo2k8GAAAAAK4RK91wiri4OKWnpyswMNBeZxiGrFar3n77bXtdUFCQqlWrpmrVqik9PV3t27fX7t27ZbVaFRoaqt9+++2a5o+OjtbQoUPtn1NTU0m8AQAAABQ6VrpR4NLT0/Xxxx9r6tSpSkxMtJedO3cqMDBQn332WbbHdezYUW5ubnr33XclSY8//rj27dunr776yqGvYRhKSUnJMQar1SpfX19TAQAAAIDCRtKNArd06VKdOHFCffv2Va1atUylQ4cODreYZ7FYLBo0aJAmTpyos2fPqlOnTurcubO6du2qCRMmaPv27Tp06JCWLl2qyMhIrV27tpDPDAAAAADyh6QbBS4uLk6RkZHZ7i7eoUMHbd++PceNzXr27KmLFy/q7bfflsVi0dy5c/XGG29o8eLFatasmerUqaOxY8fqkUceUVRUlLNPBQAAAACui8W4nt2qgJtEamqq/Pz8lJKSwq3mAAAAAK5bXnMMVroBAAAAAHASkm4AAAAAAJyEpBsAAAAAACch6QYAAAAAwElIugEAAAAAcBKSbgAAAAAAnISkGwAAAAAAJyHpBgAAAADASUi6AQAAAABwEpJuAAAAAACchKQbAAAAAAAnIekGAAAAAMBJSLoBAAAAAHASkm4AAAAAAJyEpBsAAAAAACch6QYAAAAAwElIugEAAAAAcBKSbgAAAAAAnISkGwAAAAAAJyHpBgAAAADASW6IpHvz5s1ydXVV69atHdouXLigyZMnq27duvLy8lKpUqXUpEkTxcfH6+LFi3ke5+DBg7JYLPbi4+Oj8PBwDRw4UPv3789zrAkJCfYxXFxcVKFCBfXu3VtHjx6197l8Hl9fX91111366quvHMY6d+6cxowZo9DQUFmtVpUqVUqPPfaYfvnlF3ufESNGKDg4WKdOnTId27ZtWzVt2lRHjx5VQECAJkyY4DB+p06ddM899ygjIyPXcxo7dqwp5qxSvXp1e5+IiAhZLBZ9/vnnpmOnT5+u4OBgU5+cSkREhCQpODhY06dP17p163Ltb7FYNH78eJUrV07Hjx83zbtz505ZrVYtXbo013MDAAAAgKJ0QyTdcXFxeu6557RhwwYdOXLEXn/hwgVFRUVp4sSJeuqpp/S///1P27Zt08CBA/XWW2+ZktPcxrncmjVrlJSUpJ07d2rChAnas2eP6tatq2+//TbP8fr6+iopKUl//fWXPvzwQy1fvlxPPPGEqU98fLySkpK0fft2NWnSRB07dtSuXbvs7WlpaYqMjNSsWbP06quvat++fVq2bJnS09PVsGFDbdmyRZIUExMjm82moUOH2o+dNWuW1q5dq/j4eJUpU0YffPCBxo0bZxp//vz5Wrp0qWbPni1XV9ernlN4eLiSkpJMZePGjaY+Hh4eeumllxx+2ZFl0aJF9mO3bdsm6f9930lJSVq0aJGpf+PGjU3zderUSS1btjTVjRgxQkFBQRo4cKD9uIsXL6pnz57q3r272rRpc9VzAwAAAIAiYxSxU6dOGTabzfjtt9+Mzp07G6+99pq9bdKkSYaLi4vx008/ORx34cIF4/Tp03kaxzAM448//jAkGTt27DDVZ2RkGBEREUalSpWM9PT0q8YbHx9v+Pn5mepee+01w8XFxTh79qxhGIYhyfjyyy/t7ampqYYk480337TXTZw40bBYLEZiYqJDPHfeeadRs2ZNIzMz0zAMw9i+fbtRrFgxY/ny5cahQ4cMX19f45133jEd16tXL6N+/frGhQsXjKNHjxqlS5c2zZebMWPGGHXr1s21T7NmzYzevXsbJUuWNM09bdo0o1KlSg79c/q+DcMwKlWqZEybNs2hvmfPnsYjjzziUL9nzx7Dw8PDmD9/vj3eSpUqGSkpKbnGfLmUlBRDUr6OAQAAAICc5DXHKPKV7i+++ELVq1dXWFiYunfvrlmzZskwDEnSnDlzFBkZqfr16zscV6xYMXl7e+dpnNy4uLho8ODBOnTokH788cdrOgdPT09lZmYqPT3doS09PV1xcXGSJHd3d3v93Llz9cADD6hu3boO8Tz//PP69ddftXPnTklSgwYNFB0drSeffFJPPPGE7r77bj3zzDOm4958800dO3ZM48eP14ABA1SrVi0999xz13Q+OfH19dXo0aMVExOjM2fOFOjYualevbpiY2P1zDPPaOXKlYqNjVV8fLx8fX0LLQYAAAAAuBZFnnTHxcWpe/fukqSWLVsqJSVF69evlyTt37/f9FzxtY5zNVlzHDx4MJ/RX4rxvffe05133ikfHx97fdeuXWWz2WS1WvX8888rODhYnTp1srfv27dPNWrUyHbMrPp9+/bZ61566SW5uLho69atiouLk8ViMR3j6+ur+Ph4TZgwQatWrVJ8fLxDn9zs2rVLNpvNVPr37+/Qb8CAAfLw8NAbb7yR57ELwuDBg1WrVi099NBDeuaZZ9S8efNc+6elpSk1NdVUAAAAAKCwFWnSvXfvXm3btk1du3aVJLm5ualz5872leG8rFTnZZyryZonr0lqSkqKbDabvLy8FBYWprJly2rOnDmmPtOmTVNiYqKWL1+umjVr6qOPPlKJEiWynTcvVq9ereTkZGVmZuqHH37Itk+LFi10zz336IknnlClSpXyPLYkhYWFKTEx0VRiYmIc+lmtVsXExGjKlCn677//8jXH9bBYLBo9erQyMzP10ksvXbV/bGys/Pz87CUoKKgQogQAAAAAM7einDwuLk7p6ekKDAy01xmGIavVqrfffluhoaH67bffrnscPz+/XI/fs2ePJKly5cp5itvHx0c//fSTXFxcVK5cOXl6ejr0CQgIUEhIiEJCQhQfH6+HHnpIv/76q8qUKSNJCg0Ntc+bUzyhoaGSpBMnTqhfv3566aWXZBiGBgwYoGbNmqlUqVIOx7q5ucnNLf8/Vnd3d4WEhOSpb/fu3TVlyhS9+uqr9p3LC0PWeeXl/KKjo02bz6WmppJ4AwAAACh0RbbSnZ6ero8//lhTp041ra7u3LlTgYGB+uyzz/T4449rzZo12rFjh8PxFy9e1JkzZ/I0Tm4yMzM1Y8YMVa5cOdtnx7Pj4uKikJAQValSJduE+0p33323GjRooNdee81e16VLF61Zs8b+3Pbl8UybNk01a9a0P+/93HPPKSAgQKNGjdLo0aNVvnx5027ehc3FxUWxsbGaOXPmNd2SXxisVqt8fX1NBQAAAAAKW5GtdC9dulQnTpxQ3759HVaiO3TooLi4OG3cuFHffPON7r//fo0fP1733nuvfHx8tH37dk2aNElxcXE6ePDgVce5/NnkY8eOKTk5WWfPntXu3bs1ffp0bdu2Td98802eXq11rYYMGaL27dvrxRdfVPny5fX888/rq6++Utu2bTV16lQ1bNhQ//zzj/01ZmvWrJHFYtGXX36p+fPn68cff7Sv8M6ePVt33nmnFi5cqA4dOhRIfOnp6UpOTjbVWSwWlS1bNtv+rVu3VsOGDfX+++/n2AcAAAAAbndFttIdFxenyMjIbG/97tChg7Zv3669e/dq9erVevHFF/X+++/rnnvu0V133aUZM2Zo0KBBqlWrVp7G+fnnn+11kZGRKleunGrXrq2RI0eqRo0a+vnnn6+6Mdf1atmypSpXrmxf7fbw8NB3332nHj16aNSoUQoJCVHLli3l6uqqLVu26J577tF///2n/v37a8yYMapVq5Z9rNq1a2vMmDEaMGBAgT1X/csvv6hcuXKmcrXnwidNmqTz588XyPwAAAAAcCuyGPnZzQu4SaWmpsrPz08pKSncag4AAADguuU1xyjyV4YBAAAAAHCrIum+Qnh4uMP7qrPKla8Fu1nkdD42m03ff/99UYcHAAAAALesIn1l2I1o2bJlunjxYrZtN+uGYYmJiTm2lS9fvvACAQAAAIDbDEn3Fa62edjNKK/v3wYAAAAAFCxuLwcAAAAAwElIugEAAAAAcBKSbgAAAAAAnISkGwAAAAAAJyHpBgAAAADASUi6AQAAAABwEpJuAAAAAACchKQbAAAAAAAnIekGAAAAAMBJSLoBAAAAAHASkm4AAAAAAJyEpBsAAAAAACch6QYAAAAAwElIugEAAAAAcBKSbgAAAAAAnISkGwAAAAAAJyHpvs1t3rxZrq6uat26tSSpV69eslgsOZbg4OBc2y0Wiw4ePKixY8faP7u5ualUqVJq2rSppk+frrS0NFMMERER2Y7Tv39/e5/s2u+9995C/a4AAAAAIL8shmEYRR0Eis6TTz4pm82muLg47d27V97e3jp37py9vVy5coqPj1fLli0lSRcvXlSxYsXs7Y8++qhq1aqlmJgYe13p0qU1fvx4LViwQGvWrFFmZqaOHTumdevW6dVXX1WFChW0bt06+fj4SLqUdIeGhprGkCQvLy/5+vpKupR0Xx6HJLm7u6tEiRJ5Os/U1FT5+fkpJSXFPiYAAAAAXKu85hhuhRgTbjCnT5/WvHnztH37diUnJyshIUGjRo2Sn5+fqZ+/v78CAgKyHcPd3V1eXl7Ztru5udnrAwMDVbt2bT3wwAOqW7euJk2apFdffdXeN6cx8hoHAAAAANyIuL38NvbFF1+oevXqCgsLU/fu3TVr1iw5+8aH6tWrq1WrVlq0aJFT5wEAAACAGwFJ920sLi5O3bt3lyS1bNlSKSkpWr9+vdPnrV69ug4ePGiqe/fdd2Wz2Uxlzpw5pj5du3Y1tS9evDjHOdLS0pSammoqAAAAAFDYuL38NrV3715t27ZNX375paRLt4J37txZcXFxioiIcOrchmHIYrGY6rp166bRo0eb6sqWLWv6PG3aNEVGRto/lytXLsc5YmNjNW7cuAKIFgAAAACuHUn3bSouLk7p6ekKDAy01xmGIavVqrffftvhue6CtGfPHlWuXNlU5+fnp5CQkFyPCwgIuGqfLNHR0Ro6dKj9c2pqqoKCgvIfLAAAAABcB24vvw2lp6fr448/1tSpU5WYmGgvO3fuVGBgoD777DOnzf3bb79pxYoV6tChg9PmkCSr1SpfX19TAQAAAIDCxkr3bWjp0qU6ceKE+vbt67Ci3aFDB8XFxZnekX2t0tPTlZyc7PDKsHr16mn48OGmvmfPnlVycrKpzmq1qnjx4tcdBwAAAAAUFVa6b0NxcXGKjIzM9hbyDh06aPv27fr555+ve55ffvlF5cqVU8WKFRUREaEvvvhC0dHR+v7772Wz2Ux9P/zwQ5UrV85Uunbtet0xAAAAAEBRshjOfkcUcAPI64vrAQAAACAv8ppjsNINAAAAAICTkHQDAAAAAOAkJN0AAAAAADgJSTcAAAAAAE5C0g0AAAAAgJOQdAMAAAAA4CQk3QAAAAAAOAlJNwAAAAAATkLSDQAAAACAk5B0AwAAAADgJCTdAAAAAAA4CUk3AAAAAABOQtINAAAAAICTkHQDAAAAAOAkJN0AAAAAADgJSTcAAAAAAE5C0g0AAAAAgJOQdAMAAAAA4CQk3QAAAAAAOAlJNwAAAAAATkLSDQAAAACAk5B0F6LNmzfL1dVVrVu3dmi7cOGCJk+erLp168rLy0ulSpVSkyZNFB8fr4sXL+Z5nIMHD8pisdiLj4+PwsPDNXDgQO3fvz9PcUZERJjGuLJERETY+z799NNydXXV/PnzHcYZO3as6Tg/Pz/dd999Wr9+valfcHCwpk+fnm0sV57P5WXLli15Oh8AAAAAKCok3YUoLi5Ozz33nDZs2KAjR47Y6y9cuKCoqChNnDhRTz31lP73v/9p27ZtGjhwoN566y398ssveRrncmvWrFFSUpJ27typCRMmaM+ePapbt66+/fbbq8a5aNEiJSUlKSkpSdu2bTONl5SUpEWLFkmSzp49q88//1wvvviiZs2ale1Y4eHh9uM2b96satWqqU2bNkpJScnTd3bl+VxeGjRokK8xAAAAAKCwuRV1ALeL06dPa968edq+fbuSk5OVkJCgUaNGSZKmT5+uDRs2aPv27apfv779mCpVquixxx7ThQsX8jTO5UqWLKmAgAD7OG3bttX999+vvn376vfff5erq2uOsZYoUcL+5/PnzzuMl2X+/PmqWbOmRo4cqcDAQB0+fFhBQUGmPm5ubvbjAgICFBMTo/j4eO3bt0933XVXnr67nOYHAAAAgBsdK92F5IsvvlD16tUVFham7t27a9asWTIMQ5I0Z84cRUZGmhLuLMWKFZO3t3eexsmNi4uLBg8erEOHDunHH38skHOKi4tT9+7d5efnp1atWikhISHX/mlpaYqPj5e/v7/CwsIKJIbc5kpNTTUVAAAAAChsJN2FJCtBlaSWLVsqJSXF/mzz/v37Vb169ese52qy5jh48GA+o3e0f/9+bdmyRZ07d5Ykde/eXfHx8Q6/ANi1a5dsNptsNps8PT01ZcoUffbZZ/L19c3XfI0bN7aPk1VyExsbKz8/P3u5cgUeAAAAAAoDSXch2Lt3r7Zt26auXbtKunTLdefOnRUXFydJeVqpzss4V5M1j8Viye8pOJg1a5aioqJUqlQpSdJDDz2klJQUfffdd6Z+YWFhSkxMVGJion788Uc988wzeuyxx7R9+/Z8zTdv3jz7OFklN9HR0UpJSbGXw4cP52s+AAAAACgIPNNdCOLi4pSenq7AwEB7nWEYslqtevvttxUaGqrffvvtusfx8/PL9fg9e/ZIkipXrnyNZ3JJRkaGZs+ereTkZLm5uZnqZ82apfvvv99e5+7urpCQEPvn+vXra/HixZo+fbo+/fTTPM8ZFBRkGudqrFarrFZrnvsDAAAAgDOQdDtZenq6Pv74Y02dOlUPPvigqa1du3b67LPP9Pjjj2vUqFHasWOHw3PdFy9e1IULF2S1Wq86Tv/+/XOMIzMzUzNmzFDlypWzfXY8P5YtW6ZTp05px44dpg3Zdu/erd69e+vkyZPy9/fP8XhXV1edO3fuumIAAAAAgJsBSbeTLV26VCdOnFDfvn0dVqI7dOiguLg4bdy4Ud98843uv/9+jR8/Xvfee698fHy0fft2TZo0SXFxcTp48OBVx7k86T527JiSk5N19uxZ7d69W9OnT9e2bdv0zTff5LpzeV7ExcWpdevWqlu3rqm+Zs2aev755zVnzhwNHDhQ0qVfOiQnJ0uSTp06pXnz5unXX3/ViBEjTMf+/fffDreMV6pUyeF8Lufv7y8PD4/rOhcAAAAAcCaLkdcHinFN2rZtq8zMTH3zzTcObdu2bVPDhg21c+dOhYWFadq0aZo7d672798vLy8v1ahRQ/369VO3bt3Uvn37PI3j6+trun3cy8tLlSpVUvPmzfX888/n6xZt6dKma5UrV9aOHTtUr149/fPPP6pQoYLmzp2rxx57zKH/gAEDtGXLFv30008aO3asxo0bZ4qlatWqGjBggOkXBMHBwTp06JDDWJ988onuvffeHG+H/+yzz9SlS5c8nUdqaqr8/PyUkpKS703cAAAAAOBKec0xSLpxWyDpBgAAAFCQ8ppjsHs5AAAAAABOQtJ9mwoPD3d473VWmTNnTlGHBwAAAAC3BDZSu00tW7ZMFy9ezLatbNmyhRwNAAAAANyaSLpvU5fvDA4AAAAAcA5uLwcAAAAAwElIugEAAAAAcBKSbgAAAAAAnISkGwAAAAAAJyHpBgAAAADASUi6AQAAAABwEpJuAAAAAACchKQbAAAAAAAnIekGAAAAAMBJSLoBAAAAAHASkm4AAAAAAJyEpBsAAAAAACch6QYAAAAAwElIugEAAAAAcBKSbgAAAAAAnISkGwAAAAAAJyHpzkavXr1ksVhksVjk7u6ukJAQxcTEKD09XevWrbO3ubi4yM/PT/Xr19eLL76opKQk0zhjx4619728rFmzxqHd1dVVQUFBeuqpp3T8+HHTOMHBwdmOM3HiREnSwYMHs23v3r17ru0Wi0VbtmyRJCUkJJjOq1y5curcubP+/PNPUywRERHZjtO/f397H4vFIg8PDx06dMh0bLt27dSrVy9TXXJysp577jlVqVJFVqtVQUFBatu2rb799ts8nz8AAAAA3KjcijqAG1XLli0VHx+vtLQ0LVu2TAMHDlSxYsXUqFEjSdLevXvl6+ur1NRU/fTTT5o8ebLi4uK0bt061a5d2z5OeHi4PcnOUqJECYf2jIwM7dmzR3369FFKSormzZtnOiYmJkb9+vUz1fn4+Jg+r1mzRuHh4fbPnp6eubZLUsmSJe1/9vX11d69e2UYhv744w8NGDBAjz32mLZu3Wo6pl+/foqJiTHVeXl5mT5bLBa98sormj17tnJy8OBBNWnSRP7+/nr99ddVu3ZtXbx4UStXrtTAgQP122+/5ev8AQAAAOBGQ9KdA6vVqoCAAEnSM888oy+//FJLliyxJ91lypSRv7+/AgICFBoaqkceeUT169fXM888o40bN9rHcXNzs4+Tncvby5cvr8cee0zx8fEO/Xx8fHIdR7qUQOfW52rtFovF3l6uXDn17dtXgwYNUmpqqnx9fe39vLy8rhrLs88+qzfeeEPDhw9XrVq1su0zYMAAWSwWbdu2Td7e3vb68PBw9enTx9Q3L+cPAAAAADcabi/PI09PT124cCHX9v79+2vTpk06evToNc1x8OBBrVy5Uu7u7tcaZoE5evSovvzyS7m6usrV1TXfxzdp0kRt2rTRyJEjs20/fvy4VqxYoYEDB5oS7iz+/v75nvNyaWlpSk1NNRUAAAAAKGwk3VdhGIbWrFmjlStXqkWLFrn2rV69uqRLyXOWXbt2yWaz2cvdd99tOiar3dPTU5UrV9Yvv/yiESNGOIw9YsQI0zg2m03ff/+9qU/jxo1N7Tt27Mi13WazmdpTUlJks9nk7e2tsmXLau3atdkmxe+++67DOHPmzHGIOTY2VitWrHCIU5IOHDggwzDs39nV5OX8r5zbz8/PXoKCgvI0DwAAAAAUJG4vz8HSpUtls9l08eJFZWZm6vHHH9fYsWP1ww8/5HiMYRiSLt2mnSUsLExLliyxf7ZaraZjstrPnz+vTz/9VImJiXruueccxh4+fLjDJmTly5c3fZ43b55q1Khh/3xlonll+5V8fHz0008/6eLFi1q+fLnmzJmj1157zaFft27dNHr0aFNd2bJlHfrVrFlTPXr00MiRI7Vp0yZTW9Z3lVd5Of/LRUdHa+jQofbPqampJN4AAAAACh1Jdw6aN2+umTNnyt3dXYGBgXJzu/pXtWfPHkmXdtvOkrX7eU4ub584caJat26tcePGafz48aZ+pUqVynUc6VKSnVufq7W7uLjY22vUqKHff/9dzzzzjD755BNTPz8/v6vGkmXcuHEKDQ3V4sWLTfXVqlWTxWIxbZaWm7yc/+WsVqvDLzgAAAAAoLBxe3kOvL29FRISoooVK+Yp4T537pw++OADNW3aVKVLl77meV966SVNmTJFR44cueYxCsrIkSM1b948/fTTT9c8RlBQkJ599lmNGjVKGRkZ9voSJUooKipK77zzjs6cOeNw3MmTJ695TgAAAAC4UZB0X6OjR48qOTlZ+/fv1+eff64mTZrov//+08yZM69r3EaNGqlOnTqaMGGCqf7UqVNKTk42lfxuDnbs2DGHMc6fP59j/6CgILVv316vvPKKqf7s2bMO45w4cSLHcaKjo3XkyBGHV6e98847ysjI0N13362FCxdq//792rNnj2bMmGHfJb4gzx8AAAAAChtJ9zUKCwtTYGCgGjRooIkTJyoyMlK7d+9WzZo1r3vs559/Xh999JEOHz5sr3vllVdUrlw5U3nxxRfzNW5kZKTDGFfe9p1dLN988422bdtmr/vwww8dxunatWuOY5QoUUIjRoxwSPCrVKmin376Sc2bN9ewYcNUq1YtPfDAA/r2228dfnlREOcPAAAAAIXNYuR3RyvgJpSamio/Pz+lpKSY3jkOAAAAANcirzkGK90AAAAAADgJSTcAAAAAAE5C0g0AAAAAgJOQdAMAAAAA4CQk3QAAAAAAOAlJNwAAAAAATkLSDQAAAACAk5B0AwAAAADgJG5FHQBQGAzDkHTpBfYAAAAAcL2ycousXCMnJN24LRw7dkySFBQUVMSRAAAAALiVnDp1Sn5+fjm2k3TjtlCiRAlJ0p9//pnrXwjcPlJTUxUUFKTDhw/L19e3qMPBDYLrAlfimkB2uC5wJa6J25NhGDp16pQCAwNz7UfSjduCi8ul7Qv8/Pz4hxAmvr6+XBNwwHWBK3FNIDtcF7gS18TtJy8LemykBgAAAACAk5B0AwAAAADgJCTduC1YrVaNGTNGVqu1qEPBDYJrAtnhusCVuCaQHa4LXIlrArmxGFfb3xwAAAAAAFwTVroBAAAAAHASkm4AAAAAAJyEpBsAAAAAACch6cYt75133lFwcLA8PDzUsGFDbdu2rahDghNt2LBBbdu2VWBgoCwWixYvXmxqNwxDr7zyisqVKydPT09FRkZq//79pj7Hjx9Xt27d5OvrK39/f/Xt21enT58uxLNAQYqNjdVdd90lHx8flSlTRu3atdPevXtNfc6fP6+BAweqZMmSstls6tChg/755x9Tnz///FOtW7eWl5eXypQpo+HDhys9Pb0wTwUFZObMmapTp479fbqNGjXS8uXL7e1cD5g4caIsFouGDBlir+O6uP2MHTtWFovFVKpXr25v55pAXpF045Y2b948DR06VGPGjNFPP/2kunXrKioqSkePHi3q0OAkZ86cUd26dfXOO+9k2z558mTNmDFD7733nrZu3Spvb29FRUXp/Pnz9j7dunXTL7/8otWrV2vp0qXasGGDnnrqqcI6BRSw9evXa+DAgdqyZYtWr16tixcv6sEHH9SZM2fsfZ5//nl9/fXXmj9/vtavX68jR47o0UcftbdnZGSodevWunDhgv73v/9p9uzZSkhI0CuvvFIUp4TrVKFCBU2cOFE//vijtm/frhYtWuiRRx7RL7/8Ionr4Xb3ww8/6P3331edOnVM9VwXt6fw8HAlJSXZy8aNG+1tXBPIMwO4hd19993GwIED7Z8zMjKMwMBAIzY2tgijQmGRZHz55Zf2z5mZmUZAQIDx+uuv2+tOnjxpWK1W47PPPjMMwzB+/fVXQ5Lxww8/2PssX77csFgsxt9//11oscN5jh49akgy1q9fbxjGpWugWLFixvz58+199uzZY0gyNm/ebBiGYSxbtsxwcXExkpOT7X1mzpxp+Pr6GmlpaYV7AnCK4sWLGx999BHXw23u1KlTRrVq1YzVq1cbzZo1MwYPHmwYBv9O3K7GjBlj1K1bN9s2rgnkByvduGVduHBBP/74oyIjI+11Li4uioyM1ObNm4swMhSVP/74Q8nJyaZrws/PTw0bNrRfE5s3b5a/v7/uvPNOe5/IyEi5uLho69athR4zCl5KSookqUSJEpKkH3/8URcvXjRdF9WrV1fFihVN10Xt2rVVtmxZe5+oqCilpqbaV0dxc8rIyNDnn3+uM2fOqFGjRlwPt7mBAweqdevWpp+/xL8Tt7P9+/crMDBQVapUUbdu3fTnn39K4ppA/rgVdQCAs/z333/KyMgw/UMnSWXLltVvv/1WRFGhKCUnJ0tSttdEVltycrLKlCljandzc1OJEiXsfXDzyszM1JAhQ9SkSRPVqlVL0qWfubu7u/z9/U19r7wusrtustpw89m1a5caNWqk8+fPy2az6csvv1TNmjWVmJjI9XCb+vzzz/XTTz/phx9+cGjj34nbU8OGDZWQkKCwsDAlJSVp3Lhxuu+++7R7926uCeQLSTcA4LYxcOBA7d692/RMHm5PYWFhSkxMVEpKihYsWKCePXtq/fr1RR0Wisjhw4c1ePBgrV69Wh4eHkUdDm4QrVq1sv+5Tp06atiwoSpVqqQvvvhCnp6eRRgZbjbcXo5bVqlSpeTq6uqwi+Q///yjgICAIooKRSnr557bNREQEOCw0V56erqOHz/OdXOTe/bZZ7V06VKtXbtWFSpUsNcHBATowoULOnnypKn/lddFdtdNVhtuPu7u7goJCVGDBg0UGxurunXr6s033+R6uE39+OOPOnr0qO644w65ubnJzc1N69ev14wZM+Tm5qayZctyXUD+/v4KDQ3VgQMH+LcC+ULSjVuWu7u7GjRooG+//dZel5mZqW+//VaNGjUqwshQVCpXrqyAgADTNZGamqqtW7far4lGjRrp5MmT+vHHH+19vvvuO2VmZqphw4aFHjOun2EYevbZZ/Xll1/qu+++U+XKlU3tDRo0ULFixUzXxd69e/Xnn3+arotdu3aZfiGzevVq+fr6qmbNmoVzInCqzMxMpaWlcT3cpu6//37t2rVLiYmJ9nLnnXeqW7du9j9zXeD06dP6/fffVa5cOf6tQP4U9U5ugDN9/vnnhtVqNRISEoxff/3VeOqppwx/f3/TLpK4tZw6dcrYsWOHsWPHDkOS8cYbbxg7duwwDh06ZBiGYUycONHw9/c3vvrqK+Pnn382HnnkEaNy5crGuXPn7GO0bNnSqF+/vrF161Zj48aNRrVq1YyuXbsW1SnhOj3zzDOGn5+fsW7dOiMpKclezp49a+/Tv39/o2LFisZ3331nbN++3WjUqJHRqFEje3t6erpRq1Yt48EHHzQSExONFStWGKVLlzaio6OL4pRwnUaOHGmsX7/e+OOPP4yff/7ZGDlypGGxWIxVq1YZhsH1gEsu373cMLgubkfDhg0z1q1bZ/zxxx/Gpk2bjMjISKNUqVLG0aNHDcPgmkDekXTjlvfWW28ZFStWNNzd3Y27777b2LJlS1GHBCdau3atIcmh9OzZ0zCMS68Ne/nll42yZcsaVqvVuP/++429e/eaxjh27JjRtWtXw2azGb6+vkbv3r2NU6dOFcHZoCBkdz1IMuLj4+19zp07ZwwYMMAoXry44eXlZbRv395ISkoyjXPw4EGjVatWhqenp1GqVClj2LBhxsWLFwv5bFAQ+vTpY1SqVMlwd3c3Spcubdx///32hNswuB5wyZVJN9fF7adz585GuXLlDHd3d6N8+fJG586djQMHDtjbuSaQVxbDMIyiWWMHAAAAAODWxjPdAAAAAAA4CUk3AAAAAABOQtINAAAAAICTkHQDAAAAAOAkJN0AAAAAADgJSTcAAAAAAE5C0g0AAAAAgJOQdAMAAAAA4CQk3QAA4IZz8OBBWSwWJSYmFnUodr/99pvuueceeXh4qF69eoU+f0REhIYMGZJrn4SEBPn7+xdKPACAvCHpBgAADnr16iWLxaKJEyea6hcvXiyLxVJEURWtMWPGyNvbW3v37tW3336bbZ+s7+3K0rJlyzzPs27dOlksFp08edJUv2jRIo0fP97+OTg4WNOnTzf16dy5s/bt25fnuQAAzudW1AEAAIAbk4eHhyZNmqSnn35axYsXL+pwCsSFCxfk7u5+Tcf+/vvvat26tSpVqpRrv5YtWyo+Pt5UZ7Var2nOy5UoUeKqfTw9PeXp6XndcwEACg4r3QAAIFuRkZEKCAhQbGxsjn3Gjh3rcKv19OnTFRwcbP/cq1cvtWvXThMmTFDZsmXl7++vmJgYpaena/jw4SpRooQqVKjgkKhKl27pbty4sTw8PFSrVi2tX7/e1L579261atVKNptNZcuW1RNPPKH//vvP3h4REaFnn31WQ4YMUalSpRQVFZXteWRmZiomJkYVKlSQ1WpVvXr1tGLFCnu7xWLRjz/+qJiYGFksFo0dOzbH78RqtSogIMBULv+lhcVi0UcffaT27dvLy8tL1apV05IlSyRduq2+efPmkqTixYvLYrGoV69e9nPJur08IiJChw4d0vPPP29fTZeyv738q6++0h133CEPDw9VqVJF48aNU3p6uiTJMAyNHTtWFStWlNVqVWBgoAYNGpTjuQEA8o+kGwAAZMvV1VUTJkzQW2+9pb/++uu6xvruu+905MgRbdiwQW+88YbGjBmjNm3aqHjx4tq6dav69++vp59+2mGe4cOHa9iwYdqxY4caNWqktm3b6tixY5KkkydPqkWLFqpfv762b9+uFStW6J9//lGnTp1MY8yePVvu7u7atGmT3nvvvWzje/PNNzV16lRNmTJFP//8s6KiovTwww9r//79kqSkpCSFh4dr2LBhSkpK0gsvvHBd38e4cePUqVMn/fzzz3rooYfUrVs3HT9+XEFBQVq4cKEkae/evUpKStKbb77pcPyiRYtUoUIFxcTEKCkpSUlJSdnO8/3336tHjx4aPHiwfv31V73//vtKSEjQa6+9JklauHChpk2bpvfff1/79+/X4sWLVbt27es6NwCAGUk3AADIUfv27VWvXj2NGTPmusYpUaKEZsyYobCwMPXp00dhYWE6e/asRo0apWrVqik6Olru7u7auHGj6bhnn31WHTp0UI0aNTRz5kz5+fkpLi5OkvT222+rfv36mjBhgqpXr6769etr1qxZWrt2rem55mrVqmny5MkKCwtTWFhYtvFNmTJFI0aMUJcuXRQWFqZJkyapXr169memAwIC5ObmJpvNpoCAANlsthzPdenSpbLZbKYyYcIEU59evXqpa9euCgkJ0YQJE3T69Glt27ZNrq6u9tvIy5Qpo4CAAPn5+WX7fbq6usrHx8e+mp6dcePGaeTIkerZs6eqVKmiBx54QOPHj9f7778vSfrzzz8VEBCgyMhIVaxYUXfffbf69euX47kBAPKPZ7oBAECuJk2apBYtWlzX6m54eLhcXP7f7/rLli2rWrVq2T+7urqqZMmSOnr0qOm4Ro0a2f/s5uamO++8U3v27JEk7dy5U2vXrs02Af79998VGhoqSWrQoEGusaWmpurIkSNq0qSJqb5JkybauXNnHs/w/2nevLlmzpxpqrvyeew6derY/+zt7S1fX1+Hcy8IO3fu1KZNm+wr25KUkZGh8+fP6+zZs3rsscc0ffp0ValSRS1bttRDDz2ktm3bys2N/0QEgILCv6gAACBXTZs2VVRUlKKjo+3PF2dxcXGRYRimuosXLzqMUaxYMdNni8WSbV1mZmae4zp9+rTatm2rSZMmObSVK1fO/mdvb+88j1kQvL29FRISkmuf6z33vDp9+rTGjRunRx991KHNw8NDQUFB2rt3r9asWaPVq1drwIABev3117V+/XqHGAEA14akGwAAXNXEiRNVr149h9uzS5cureTkZBmGYd/MqyDfrb1lyxY1bdpUkpSenq4ff/xRzz77rCTpjjvu0MKFCxUcHHxdK7O+vr4KDAzUpk2b1KxZM3v9pk2bdPfdd1/fCVyDrN3VMzIyrtrvan3uuOMO7d27N9dfAnh6eqpt27Zq27atBg4cqOrVq2vXrl2644478h88AMABSTcAALiq2rVrq1u3bpoxY4apPiIiQv/++68mT56sjh07asWKFVq+fLl8fX0LZN533nlH1apVU40aNTRt2jSdOHFCffr0kSQNHDhQH374obp27aoXX3xRJUqU0IEDB/T555/ro48+kqura57nGT58uMaMGaOqVauqXr16io+PV2JioubMmZPvmNPS0pScnGyqc3NzU6lSpfJ0fKVKlWSxWLR06VI99NBD8vT0zPYW+uDgYG3YsEFdunSR1WrNdvxXXnlFbdq0UcWKFdWxY0e5uLho586d2r17t1599VUlJCQoIyNDDRs2lJeXlz799FN5enpe9bVoAIC8YyM1AACQJzExMQ63QNeoUUPvvvuu3nnnHdWtW1fbtm277p29Lzdx4kRNnDhRdevW1caNG7VkyRJ7cpm1Op2RkaEHH3xQtWvX1pAhQ+Tv7296fjwvBg0apKFDh2rYsGGqXbu2VqxYoSVLlqhatWr5jnnFihUqV66cqdx77715Pr58+fL2DdDKli1rX9m/UkxMjA4ePKiqVauqdOnS2faJiorS0qVLtWrVKt1111265557NG3aNHtS7e/vrw8//FBNmjRRnTp1tGbNGn399dcqWbJkvs8bAJA9i3Hlg1gAAAAAAKBAsNINAAAAAICTkHQDAAAAAOAkJN0AAAAAADgJSTcAAAAAAE5C0g0AAAAAgJOQdAMAAAAA4CQk3QAAAAAAOAlJNwAAAAAATkLSDQAAAACAk5B0AwAAAADgJCTdAAAAAAA4CUk3AAAAAABO8v8BEr8rmhjYA5kAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize Entity Type Distribution\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "# Bar Plot of Entity Types\n", "plt.figure(figsize=(10, 5))\n", "sns.barplot(data=entity_counts, x='Count', y='Entity Type', palette='viridis')\n", "plt.title(\"Distribution of Entity Types in CAD\")\n", "plt.xlabel(\"Number of Entities\")\n", "plt.ylabel(\"Entity Type\")\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Entity: INSERT\n", "{'CVT_Tracker 1x26 int', 'CVT_Tracker 1x26 ext', 'CVT_Tracker 1X52 Edge', 'CVT_Tracker 1x52 ext', 'SCS_Indicazione Cancelli', '0', 'CVT_Tracker 1x52 int'}\n", "\n", "Entity: LWPOLYLINE\n", "{'EGP - FOUND Predrilling', 'SCS_Fabbricato - Manufatto Stimato', 'SCS_STRADE', 'SCS_Legenda', 'TERRENO SOVRACONSOLIDATO', 'SCS_TC TIPO 1', 'nuova strada', 'EGP - FOUND pre-foro e compattazione', '0', 'CVT - FOUND pre-foro e compattazione', 'PVcase Offsets', 'TRINCEE 1-1.20M', 'SCS_DEMOLIZIONI', 'TRINCEE SUPERFICIALI', 'Pozzetto', 'SCS_FENCE', 'SCS_TC TIPO 2', 'PVcase AlignmentLine', 'SCS MV', 'SCS_Strade esistenti idonee', 'SCS_CABINA DI CONSEGNA', 'CVT - FOUND plinti', 'TRINCEE 1.60M', 'SCS_CABINA UTENTE', 'SCS_Strade esistenti da rimuovere', 'EGP - FOUND plinti', 'CVT - FOUND Plinti o pre-foro', 'SCS_Fabbricato - Manufatto Rilevato', 'SCS_MITIGAZIONE PERIMETRALE', 'PVcase PV Area'}\n", "\n", "Entity: LINE\n", "{'SCS_Legenda', 'Pozzetto'}\n", "\n", "Entity: MTEXT\n", "{'SCS_TEXT', 'SCS_Legenda'}\n"]}], "source": ["from collections import defaultdict\n", "\n", "layer_map = defaultdict(set)\n", "\n", "for e in modelspace:\n", "    layer = getattr(e.dxf, \"layer\", \"unknown\")\n", "    entity_type = e.dxftype()\n", "    layer_map[entity_type].add(layer)\n", "\n", "# See what layers are used in INSERTs, LWPOLYLINEs, etc.\n", "for etype in [\"INSERT\", \"LWPOLY<PERSON>IN<PERSON>\", \"LINE\", \"MTEXT\"]:\n", "    print(f\"\\nEntity: {etype}\")\n", "    print(layer_map[etype])"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["# Defined classification rules\n", "classification_rules = {\n", "    'pile': {\n", "        'layer_keywords': ['pile', 'palo', 'pali'],\n", "        'block_keywords': ['pile', 'palo', 'p_'],\n", "        'text_keywords': ['pile', 'palo', 'p-'],\n", "        'entity_types': ['CIRCLE', 'INSERT', 'POINT'],\n", "        'priority': 3\n", "    },\n", "    'panel': {\n", "        'layer_keywords': ['panel', 'pannello', 'pv', 'solar', 'solare', 'modulo', 'moduli'],\n", "        'block_keywords': ['panel', 'pannello', 'pv', 'solar', 'modulo'],\n", "        'text_keywords': ['panel', 'pv', 'solar', 'modulo'],\n", "        'entity_types': ['INSERT', 'LWPOLYLINE', 'POLYLINE'],\n", "        'priority': 2\n", "    },\n", "    'road': {\n", "        'layer_keywords': ['road', 'strada', 'strade', 'access', 'accesso', 'viabilita'],\n", "        'block_keywords': ['road', 'strada', 'access'],\n", "        'text_keywords': ['road', 'strada', 'access'],\n", "        'entity_types': ['LWPOLYLINE', 'POLYLINE', 'LINE', 'ARC'],\n", "        'priority': 2\n", "    },\n", "    'trench': {\n", "        'layer_keywords': ['trench', 'trincea', 'cable', 'cavo', 'cavidotto', 'cavidotti'],\n", "        'block_keywords': ['trench', 'cable', 'cavo', 'cavidotto'],\n", "        'text_keywords': ['trench', 'cable', 'cavo', 'cavidotto'],\n", "        'entity_types': ['LWPOLYLINE', 'POLYLINE', 'LINE'],\n", "        'priority': 2\n", "    },\n", "    'foundation': {\n", "        'layer_keywords': ['foundation', 'fondazione', 'base', 'cabin', 'cabina', 'cabine'],\n", "        'block_keywords': ['foundation', 'cabin', 'cabina', 'base'],\n", "        'text_keywords': ['foundation', 'cabin', 'cabina', 'base'],\n", "        'entity_types': ['LWPOLYLINE', 'POLYLINE', 'INSERT'],\n", "        'priority': 3\n", "    },\n", "    'electrical': {\n", "        'layer_keywords': ['electrical', 'elettrico', 'elettrica', 'power', 'energia', 'electric'],\n", "        'block_keywords': ['electrical', 'elettrico', 'power', 'electric'],\n", "        'text_keywords': ['electrical', 'power', 'kw', 'v', 'elettrico'],\n", "        'entity_types': ['LINE', 'LWPOLYLINE', 'INSERT'],\n", "        'priority': 1\n", "    },\n", "    'building': {\n", "        'layer_keywords': ['building', 'edificio', 'fabbricato', 'manufatto', 'costruzione'],\n", "        'block_keywords': ['building', 'edificio', 'fabbricato'],\n", "        'text_keywords': ['building', 'edificio'],\n", "        'entity_types': ['LWPOLYLINE', 'POLYLINE', 'INSERT', 'HATCH'],\n", "        'priority': 2\n", "    },\n", "    'annotation': {\n", "        'layer_keywords': ['text', 'label', 'annotation', 'quota', 'dimension', 'dim'],\n", "        'block_keywords': ['text', 'label', 'annotation'],\n", "        'text_keywords': [],\n", "        'entity_types': ['TEXT', 'MTEXT', 'DIMENSION'],\n", "        'priority': 1\n", "    }\n", "}\n"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:building: 80 entities\n", "INFO:__main__:unclassified: 891 entities\n", "INFO:__main__:annotation: 12 entities\n", "INFO:__main__:road: 43 entities\n", "INFO:__main__:panel: 30 entities\n", "INFO:__main__:foundation: 6 entities\n", "INFO:__main__:pile: 3 entities\n"]}], "source": ["# Classify entities\n", "def classify_entity_dxf(entity, rules):\n", "    etype = entity.dxftype()\n", "    layer = getattr(entity.dxf, \"layer\", \"\").lower()\n", "    block_name = getattr(entity.dxf, \"name\", \"\").lower() if hasattr(entity.dxf, \"name\") else \"\"\n", "    text = \"\"\n", "    if etype in [\"TEXT\", \"MTEXT\"]:\n", "        text = entity.text.lower() if hasattr(entity, \"text\") else \"\"\n", "\n", "    for tag, r in rules.items():\n", "        if etype not in r['entity_types']:\n", "            continue\n", "        if any(k in layer for k in r['layer_keywords']):\n", "            return tag\n", "        if any(k in block_name for k in r['block_keywords']):\n", "            return tag\n", "        if any(k in text for k in r['text_keywords']):\n", "            return tag\n", "    return 'unclassified'\n", "\n", "tagged_entities = defaultdict(list)\n", "\n", "for e in modelspace:\n", "    tag = classify_entity_dxf(e, classification_rules)\n", "    tagged_entities[tag].append(e)\n", "\n", "# Summary printout\n", "for tag, group in tagged_entities.items():\n", "    logger.info(f\"{tag}: {len(group)} entities\")"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Entity Type</th>\n", "      <th>Layer</th>\n", "      <th>Tag</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>HATCH</td>\n", "      <td>SCS_Fabbricato - Manufatto Stimato</td>\n", "      <td>building</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>LWPOLYLINE</td>\n", "      <td>SCS_Fabbricato - Manufatto Stimato</td>\n", "      <td>building</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>LWPOLYLINE</td>\n", "      <td>SCS_Fabbricato - Manufatto Stimato</td>\n", "      <td>building</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>HATCH</td>\n", "      <td>SCS_Fabbricato - Manufatto Rilevato</td>\n", "      <td>building</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>LWPOLYLINE</td>\n", "      <td>SCS_Fabbricato - Manufatto Stimato</td>\n", "      <td>building</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Entity Type                                Layer       Tag\n", "0       HATCH   SCS_Fabbricato - Manufatto Stimato  building\n", "1  LWPOLYLINE   SCS_Fabbricato - Manufatto Stimato  building\n", "2  LWPOLYLINE   SCS_Fabbricato - Manufatto Stimato  building\n", "3       HATCH  SCS_Fabbricato - Manufatto Rilevato  building\n", "4  LWPOLYLINE   SCS_Fabbricato - Manufatto Stimato  building"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["rows = []\n", "for tag, ents in tagged_entities.items():\n", "    for e in ents:\n", "        rows.append({\n", "            \"Entity Type\": e.dxftype(),\n", "            \"Layer\": getattr(e.dxf, \"layer\", \"\"),\n", "            \"Tag\": tag\n", "        })\n", "\n", "df = pd.DataFrame(rows)\n", "df.to_csv(\"entity_classification.csv\", index=False)\n", "df.head()\n"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== EXTRACTION SUMMARY ===\n", "Trackers extracted: 0\n", "Modules extracted: 13\n", "No tracker coordinates found.\n"]}], "source": ["# Extract centroids for specific tags\n", "from shapely.geometry import Polygon, Point\n", "import numpy as np\n", "\n", "def get_centroid(entity):\n", "    if entity.dxftype() == 'INSERT':\n", "        return Point(entity.dxf.insert.x, entity.dxf.insert.y)\n", "    elif entity.dxftype() in ['LWPOLYLINE', 'POLYLINE']:\n", "        points = [tuple(p[:2]) for p in entity.get_points()]\n", "        if len(points) >= 3:\n", "            return Poly<PERSON>(points).centroid\n", "    return None\n", "\n", "tracker_coords = []\n", "module_coords = []\n", "\n", "for tracker in tagged_entities.get('panel', []):\n", "    pt = get_centroid(tracker)\n", "    if pt:\n", "        module_coords.append({'x': pt.x, 'y': pt.y})\n", "\n", "for tracker in tagged_entities.get('pile', []):\n", "    pt = get_centroid(tracker)\n", "    if pt:\n", "        tracker_coords.append({'x': pt.x, 'y': pt.y})\n", "\n", "\n", "print(\"\\n=== EXTRACTION SUMMARY ===\")\n", "print(f\"Trackers extracted: {len(tracker_coords)}\")\n", "print(f\"Modules extracted: {len(module_coords)}\")\n", "\n", "if tracker_coords:\n", "    coords = np.array([[t['x'], t['y']] for t in tracker_coords])\n", "    print(\"\\nTracker coordinate ranges:\")\n", "    print(f\"  X: {coords[:, 0].min():.1f} to {coords[:, 0].max():.1f}\")\n", "    print(f\"  Y: {coords[:, 1].min():.1f} to {coords[:, 1].max():.1f}\")\n", "else:\n", "    print(\"No tracker coordinates found.\")\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== PROCESSING CAD FILES ===\n", "\n", "Processing: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\n", "  Trackers extracted: 548\n", "  Modules extracted: 0\n", "\n", "Skipping: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg (DWG format - convert to DXF first)\n", "\n", "=== EXTRACTION SUMMARY ===\n", "Total trackers: 548\n", "Total modules: 0\n", "\n", "Tracker coordinate ranges:\n", "  X: 706923.1 to 719370.1\n", "  Y: 4692611.3 to 4694131.4\n"]}], "source": ["print(\"\\n=== PROCESSING CAD FILES ===\")\n", "\n", "all_trackers = []\n", "all_modules = []\n", "\n", "for file_path in cad_files:\n", "    if file_path.suffix.lower() == '.dxf':  # Process DXF files only\n", "        print(f\"\\nProcessing: {file_path.name}\")\n", "        \n", "        trackers, modules = extract_coordinates_simple(file_path)\n", "        \n", "        # Add source file info\n", "        for tracker in trackers:\n", "            tracker['source_file'] = file_path.name\n", "        for module in modules:\n", "            module['source_file'] = file_path.name\n", "        \n", "        all_trackers.extend(trackers)\n", "        all_modules.extend(modules)\n", "        \n", "        print(f\"  Trackers extracted: {len(trackers)}\")\n", "        print(f\"  Modules extracted: {len(modules)}\")\n", "    else:\n", "        print(f\"\\nSkipping: {file_path.name} (DWG format - convert to DXF first)\")\n", "\n", "print(f\"\\n=== EXTRACTION SUMMARY ===\")\n", "print(f\"Total trackers: {len(all_trackers)}\")\n", "print(f\"Total modules: {len(all_modules)}\")\n", "\n", "# Quick coordinate analysis\n", "if all_trackers:\n", "    tracker_coords = np.array([[t['x'], t['y']] for t in all_trackers])\n", "    print(f\"\\nTracker coordinate ranges:\")\n", "    print(f\"  X: {tracker_coords[:, 0].min():.1f} to {tracker_coords[:, 0].max():.1f}\")\n", "    print(f\"  Y: {tracker_coords[:, 1].min():.1f} to {tracker_coords[:, 1].max():.1f}\")\n", "\n", "if all_modules:\n", "    module_coords = np.array([[m['x'], m['y']] for m in all_modules])\n", "    print(f\"\\nModule coordinate ranges:\")\n", "    print(f\"  X: {module_coords[:, 0].min():.1f} to {module_coords[:, 0].max():.1f}\")\n", "    print(f\"  Y: {module_coords[:, 1].min():.1f} to {module_coords[:, 1].max():.1f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Save Simple Outputs"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SAVING OUTPUTS ===\n", "Output directory: ../../../output_runs/cad_metadata/simple_solar_extraction_20250701_143449\n", "✅ Tracker coordinates: tracker_coordinates_20250701_143449.csv (548 points)\n", "✅ Summary: extraction_summary_20250701_143449.json\n", "\n", "🎯 Ready for alignment workflows!\n", "📁 Output directory: ../../../output_runs/cad_metadata/simple_solar_extraction_20250701_143449\n", "\n", "📅 Completed: 2025-07-01 14:34:49\n"]}], "source": ["if all_trackers or all_modules:\n", "    # Create output directory\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    output_path = Path(output_dir)\n", "    run_output_dir = output_path / f\"simple_solar_extraction_{timestamp}\"\n", "    run_output_dir.mkdir(parents=True, exist_ok=True)\n", "    \n", "    print(f\"\\n=== SAVING OUTPUTS ===\")\n", "    print(f\"Output directory: {run_output_dir}\")\n", "    \n", "    # Save tracker coordinates (primary alignment points)\n", "    if all_trackers:\n", "        trackers_df = pd.DataFrame(all_trackers)\n", "        tracker_file = run_output_dir / f\"tracker_coordinates_{timestamp}.csv\"\n", "        trackers_df.to_csv(tracker_file, index=False)\n", "        print(f\"✅ Tracker coordinates: {tracker_file.name} ({len(all_trackers)} points)\")\n", "    \n", "    # Save module boundaries (secondary alignment)\n", "    if all_modules:\n", "        modules_df = pd.DataFrame(all_modules)\n", "        module_file = run_output_dir / f\"module_boundaries_{timestamp}.csv\"\n", "        modules_df.to_csv(module_file, index=False)\n", "        print(f\"✅ Module boundaries: {module_file.name} ({len(all_modules)} areas)\")\n", "    \n", "    # Save simple summary\n", "    summary = {\n", "        'extraction_timestamp': timestamp,\n", "        'target_files': target_files,\n", "        'files_processed': [f.name for f in cad_files if f.suffix.lower() == '.dxf'],\n", "        'results': {\n", "            'tracker_count': len(all_trackers),\n", "            'module_count': len(all_modules)\n", "        },\n", "        'coordinate_ranges': {\n", "            'tracker_x_range': [float(tracker_coords[:, 0].min()), float(tracker_coords[:, 0].max())] if all_trackers else None,\n", "            'tracker_y_range': [float(tracker_coords[:, 1].min()), float(tracker_coords[:, 1].max())] if all_trackers else None,\n", "            'module_x_range': [float(module_coords[:, 0].min()), float(module_coords[:, 0].max())] if all_modules else None,\n", "            'module_y_range': [float(module_coords[:, 1].min()), float(module_coords[:, 1].max())] if all_modules else None\n", "        },\n", "        'usage': {\n", "            'primary_alignment': 'Use tracker_coordinates.csv for point cloud registration',\n", "            'area_validation': 'Use module_boundaries.csv for spatial validation'\n", "        }\n", "    }\n", "    \n", "    summary_file = run_output_dir / f\"extraction_summary_{timestamp}.json\"\n", "    with open(summary_file, 'w') as f:\n", "        json.dump(summary, f, indent=2)\n", "    print(f\"✅ Summary: {summary_file.name}\")\n", "    \n", "    print(f\"\\n🎯 Ready for alignment workflows!\")\n", "    print(f\"📁 Output directory: {run_output_dir}\")\n", "    \n", "else:\n", "    print(\"\\n❌ No data extracted - check file paths and formats\")\n", "\n", "print(f\"\\n📅 Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}