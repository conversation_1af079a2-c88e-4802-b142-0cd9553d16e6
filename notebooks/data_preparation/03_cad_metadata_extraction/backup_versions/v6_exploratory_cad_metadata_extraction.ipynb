{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Exploratory CAD Metadata Extraction\n", "\n", "Generic CAD metadata extraction from DXF files for solar project infrastructure.\n", "\n", "**Stage**: Data Preparation - CAD Metadata Extraction  \n", "**Input Data**: DXF/DWG files from CAD directory  \n", "**Output**: Structured metadata for alignment and spatial analysis  \n", "**Purpose**: Extract tracker layouts, module arrangements, and DC cable routes for point cloud alignment\n", "\n", "## Typical Solar Project CAD Content:\n", "- **Tracker layouts**: Solar tracking system positions and configurations\n", "- **Module arrangements**: Individual or grouped solar panel layouts\n", "- **DC cable routes**: Electrical infrastructure and cable routing\n", "- **Site boundaries**: Property lines and access roads\n", "- **Reference points**: Survey markers and coordinate references\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## DXF Entity Types Reference Guide\n", "\n", "Common entities found in solar project CAD files:\n", "\n", "### Geometric Entities\n", "- **CIRCLE**: Circular arcs and circles (piles, manholes, equipment)\n", "- **LINE**: Straight line segments (boundaries, connections, infrastructure)\n", "- **LWPOLYLINE**: Lightweight polylines (complex shapes, site boundaries, roads)\n", "- **POLYLINE**: 3D polylines with vertices (terrain contours, complex paths)\n", "- **ARC**: Circular arc segments (curved roads, rounded corners)\n", "- **POINT**: Single coordinate points (survey points, reference markers)\n", "\n", "### Block and Text Entities\n", "- **INSERT**: Block references (solar panels, equipment, symbols, trackers)\n", "- **TEXT**: Single-line text (labels, dimensions, annotations)\n", "- **MTEXT**: Multi-line text (descriptions, notes, specifications)\n", "- **ATTDEF**: Attribute definitions in blocks\n", "- **DIMENSION**: Dimension lines and text\n", "\n", "### Surface and Fill Entities\n", "- **HATCH**: Filled areas and patterns (buildings, water bodies, zones)\n", "- **SOLID**: Solid filled triangular areas\n", "- **TRACE**: Solid filled quadrilateral areas"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Papermill parameters\n", "project_type = \"generic_project\"  # Can be overridden for specific projects\n", "site_name = \"exploratory_extraction\"\n", "cad_data_path = \"../../../data/raw\"  # Generic path, will auto-discover\n", "output_dir = \"../../../output_runs/cad_metadata\"\n", "target_files = []  # Auto-discover DXF files if empty\n", "coordinate_system = \"auto\"  # Auto-detect or specify (e.g., EPSG:32633)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "import ezdxf\n", "import json\n", "from datetime import datetime\n", "from collections import defaultdict\n", "import logging\n", "import mlflow\n", "import matplotlib.pyplot as plt\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "print(f\"Exploratory CAD Metadata Extraction - {project_type.title()}\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(\"=\" * 60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. CAD File Discovery and Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def discover_cad_files(base_path):\n", "    \"\"\"Discover DXF and DWG files in the data directory.\"\"\"\n", "    base_path = Path(base_path)\n", "    cad_files = []\n", "    \n", "    # Search for CAD files\n", "    for pattern in ['**/*.dxf', '**/*.DXF', '**/*.dwg', '**/*.DWG']:\n", "        cad_files.extend(base_path.glob(pattern))\n", "    \n", "    return sorted(cad_files)\n", "\n", "def analyze_cad_file_structure(file_path):\n", "    \"\"\"Analyze the structure of a CAD file.\"\"\"\n", "    try:\n", "        doc = ezdxf.readfile(file_path)\n", "        msp = doc.modelspace()\n", "        \n", "        # Count entity types\n", "        entity_counts = defaultdict(int)\n", "        layer_counts = defaultdict(int)\n", "        \n", "        for entity in msp:\n", "            entity_counts[entity.dxftype()] += 1\n", "            layer_counts[getattr(entity.dxf, 'layer', 'unknown')] += 1\n", "        \n", "        return {\n", "            'entity_types': dict(entity_counts),\n", "            'layers': dict(layer_counts),\n", "            'total_entities': sum(entity_counts.values())\n", "        }\n", "    except Exception as e:\n", "        logger.warning(f\"Could not analyze {file_path}: {e}\")\n", "        return None\n", "\n", "# Discover CAD files\n", "print(\"=== CAD FILE DISCOVERY ===\")\n", "cad_files = discover_cad_files(cad_data_path)\n", "\n", "if not cad_files:\n", "    print(f\"No CAD files found in {cad_data_path}\")\n", "    print(\"Please check the path or place DXF/DWG files in the data directory\")\n", "else:\n", "    print(f\"Found {len(cad_files)} CAD files:\")\n", "    for i, file_path in enumerate(cad_files, 1):\n", "        print(f\"  {i}. {file_path.name} ({file_path.stat().st_size / 1024 / 1024:.1f} MB)\")\n", "    \n", "    # Analyze file structures\n", "    print(f\"\\n=== CAD FILE STRUCTURE ANALYSIS ===\")\n", "    file_analyses = {}\n", "    for file_path in cad_files[:3]:  # Analyze first 3 files\n", "        print(f\"\\nAnalyzing: {file_path.name}\")\n", "        analysis = analyze_cad_file_structure(file_path)\n", "        if analysis:\n", "            file_analyses[file_path.name] = analysis\n", "            print(f\"  Total entities: {analysis['total_entities']}\")\n", "            print(f\"  Entity types: {len(analysis['entity_types'])}\")\n", "            print(f\"  Layers: {len(analysis['layers'])}\")\n", "            \n", "            # Show top entity types\n", "            top_entities = sorted(analysis['entity_types'].items(), key=lambda x: x[1], reverse=True)[:5]\n", "            print(f\"  Top entity types: {dict(top_entities)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Generic Infrastructure Classification System"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class GenericInfrastructureClassifier:\n", "    \"\"\"Generic classifier that adapts to different CAD file structures.\"\"\"\n", "    \n", "    def __init__(self, custom_rules=None):\n", "        # Default classification rules (can be overridden)\n", "        self.classification_rules = custom_rules or {\n", "            # Structural elements\n", "            'pile': {\n", "                'layers': ['PILE', 'FOUNDATION', 'STRUCTURAL'],\n", "                'entity_types': ['LINE', 'CIRCLE', 'INSERT'],\n", "                'keywords': ['pile', 'foundation', 'structural', 'support']\n", "            },\n", "            \n", "            # Solar/tracking equipment\n", "            'tracker': {\n", "                'layers': ['TRACKER', 'SOLAR', 'PANEL'],\n", "                'entity_types': ['INSERT', 'BLOCK'],\n", "                'keywords': ['tracker', 'solar', 'panel', 'TRJ', 'CVT']\n", "            },\n", "            \n", "            # Infrastructure\n", "            'road': {\n", "                'layers': ['ROAD', 'ACCESS', 'STREET', 'PATH'],\n", "                'entity_types': ['POLYLINE', 'LWPOLYLINE', 'LINE'],\n", "                'keywords': ['road', 'access', 'street', 'path', 'drive']\n", "            },\n", "            \n", "            # Boundaries and fencing\n", "            'boundary': {\n", "                'layers': ['FENCE', 'BOUNDARY', 'PERIMETER'],\n", "                'entity_types': ['POLYLINE', 'LWPOLYLINE', 'LINE'],\n", "                'keywords': ['fence', 'boundary', 'perimeter', 'recinzione']\n", "            },\n", "            \n", "            # Electrical\n", "            'electrical': {\n", "                'layers': ['ELECTRICAL', 'CABLE', 'POWER', 'VOLTAGE'],\n", "                'entity_types': ['POLYLINE', 'LINE'],\n", "                'keywords': ['electrical', 'cable', 'power', 'voltage', 'wire']\n", "            },\n", "            \n", "            # Buildings and structures\n", "            'building': {\n", "                'layers': ['BUILDING', 'STRUCTURE', 'FACILITY'],\n", "                'entity_types': ['POLYLIN<PERSON>', 'LWPOLYLINE', 'INSERT', 'HATCH'],\n", "                'keywords': ['building', 'structure', 'facility', 'cabin', 'booth']\n", "            },\n", "            \n", "            # Text and annotations\n", "            'annotation': {\n", "                'layers': ['TEXT', 'ANNOTATION', 'LABEL'],\n", "                'entity_types': ['TEXT', 'MTEXT', 'DIMENSION'],\n", "                'keywords': ['text', 'label', 'annotation', 'note']\n", "            }\n", "        }\n", "    \n", "    def update_rules_from_analysis(self, file_analyses):\n", "        \"\"\"Update classification rules based on discovered layers.\"\"\"\n", "        discovered_layers = set()\n", "        for analysis in file_analyses.values():\n", "            discovered_layers.update(analysis['layers'].keys())\n", "        \n", "        print(f\"\\nDiscovered layers: {sorted(discovered_layers)}\")\n", "        \n", "        # Auto-enhance rules based on discovered layers\n", "        for layer in discovered_layers:\n", "            layer_upper = layer.upper()\n", "            \n", "            # Add to appropriate categories\n", "            if any(keyword in layer_upper for keyword in ['PILE', 'FOUNDATION']):\n", "                if layer not in self.classification_rules['pile']['layers']:\n", "                    self.classification_rules['pile']['layers'].append(layer)\n", "            \n", "            elif any(keyword in layer_upper for keyword in ['TRACKE<PERSON>', 'SOL<PERSON>', 'TRJ', 'CVT']):\n", "                if layer not in self.classification_rules['tracker']['layers']:\n", "                    self.classification_rules['tracker']['layers'].append(layer)\n", "            \n", "            elif any(keyword in layer_upper for keyword in ['ROAD', 'ACCESS', 'STREET']):\n", "                if layer not in self.classification_rules['road']['layers']:\n", "                    self.classification_rules['road']['layers'].append(layer)\n", "        \n", "        print(f\"Updated classification rules based on discovered layers\")\n", "    \n", "    def classify_entity(self, entity_data):\n", "        \"\"\"Classify entity based on flexible rules.\"\"\"\n", "        layer_name = entity_data.get('layer_name', '').upper()\n", "        entity_type = entity_data.get('entity_type', '')\n", "        text_content = str(entity_data.get('text_content', '')).lower()\n", "        block_name = str(entity_data.get('block_name', '')).lower()\n", "        \n", "        for classification, rules in self.classification_rules.items():\n", "            # Check exact layer match\n", "            if any(layer.upper() == layer_name for layer in rules['layers']):\n", "                return classification\n", "            \n", "            # Check partial layer match\n", "            if any(layer.upper() in layer_name for layer in rules['layers']):\n", "                return classification\n", "            \n", "            # Check entity type and keyword combination\n", "            if entity_type in rules['entity_types']:\n", "                if any(keyword in text_content or keyword in block_name for keyword in rules['keywords']):\n", "                    return classification\n", "        \n", "        return 'unknown'\n", "\n", "# Initialize classifier\n", "classifier = GenericInfrastructureClassifier()\n", "\n", "# Update rules based on discovered files\n", "if 'file_analyses' in locals() and file_analyses:\n", "    classifier.update_rules_from_analysis(file_analyses)\n", "\n", "print(\"\\nGeneric Infrastructure Classifier initialized\")\n", "print(f\"Classification categories: {list(classifier.classification_rules.keys())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Generic CAD Entity Extraction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_entity_data(entity):\n", "    \"\"\"Extract comprehensive entity data from any DXF entity.\"\"\"\n", "    try:\n", "        entity_type = entity.dxftype()\n", "        layer_name = getattr(entity.dxf, 'layer', 'unknown')\n", "        \n", "        # Base entity data\n", "        entity_data = {\n", "            'entity_id': str(entity.dxf.handle),\n", "            'entity_type': entity_type,\n", "            'layer_name': layer_name,\n", "            'color': getattr(entity.dxf, 'color', 256),\n", "            'linetype': getattr(entity.dxf, 'linetype', 'Continuous'),\n", "            'lineweight': getattr(entity.dxf, 'lineweight', -1)\n", "        }\n", "        \n", "        # Extract coordinates based on entity type\n", "        if hasattr(entity.dxf, 'insert'):\n", "            # INSERT entities (blocks, symbols, equipment)\n", "            insert_point = entity.dxf.insert\n", "            entity_data.update({\n", "                'x_coord': insert_point.x,\n", "                'y_coord': insert_point.y,\n", "                'z_coord': insert_point.z,\n", "                'block_name': getattr(entity.dxf, 'name', ''),\n", "                'rotation': getattr(entity.dxf, 'rotation', 0.0),\n", "                'x_scale': getattr(entity.dxf, 'xscale', 1.0),\n", "                'y_scale': getattr(entity.dxf, 'yscale', 1.0),\n", "                'z_scale': getattr(entity.dxf, 'zscale', 1.0)\n", "            })\n", "            \n", "        <PERSON><PERSON> hasattr(entity.dxf, 'start') and hasattr(entity.dxf, 'end'):\n", "            # LINE entities (connections, boundaries, infrastructure)\n", "            start_point = entity.dxf.start\n", "            end_point = entity.dxf.end\n", "            entity_data.update({\n", "                'x_coord': (start_point.x + end_point.x) / 2,\n", "                'y_coord': (start_point.y + end_point.y) / 2,\n", "                'z_coord': (start_point.z + end_point.z) / 2,\n", "                'start_x': start_point.x,\n", "                'start_y': start_point.y,\n", "                'start_z': start_point.z,\n", "                'end_x': end_point.x,\n", "                'end_y': end_point.y,\n", "                'end_z': end_point.z,\n", "                'length': start_point.distance(end_point),\n", "                'angle': np.degrees(np.arctan2(end_point.y - start_point.y, end_point.x - start_point.x))\n", "            })\n", "            \n", "        <PERSON><PERSON>(entity.dxf, 'center'):\n", "            # CIRCLE entities (piles, manholes, equipment)\n", "            center = entity.dxf.center\n", "            entity_data.update({\n", "                'x_coord': center.x,\n", "                'y_coord': center.y,\n", "                'z_coord': center.z,\n", "                'radius': getattr(entity.dxf, 'radius', 0.0),\n", "                'diameter': getattr(entity.dxf, 'radius', 0.0) * 2,\n", "                'area': np.pi * (getattr(entity.dxf, 'radius', 0.0) ** 2)\n", "            })\n", "            \n", "        elif hasattr(entity, 'vertices') or entity_type in ['POLYLINE', 'LWPOLYLINE']:\n", "            # POLYLINE entities (complex shapes, boundaries, roads)\n", "            try:\n", "                if hasattr(entity, 'vertices'):\n", "                    vertices = list(entity.vertices)\n", "                else:\n", "                    vertices = list(entity)\n", "                \n", "                if vertices:\n", "                    # Calculate centroid\n", "                    x_coords = [v.dxf.location.x for v in vertices if hasattr(v.dxf, 'location')]\n", "                    y_coords = [v.dxf.location.y for v in vertices if hasattr(v.dxf, 'location')]\n", "                    \n", "                    if x_coords and y_coords:\n", "                        entity_data.update({\n", "                            'x_coord': np.mean(x_coords),\n", "                            'y_coord': np.mean(y_coords),\n", "                            'z_coord': 0.0,\n", "                            'vertex_count': len(vertices),\n", "                            'x_min': min(x_coords),\n", "                            'x_max': max(x_coords),\n", "                            'y_min': min(y_coords),\n", "                            'y_max': max(y_coords)\n", "                        })\n", "                    else:\n", "                        entity_data.update({'x_coord': 0.0, 'y_coord': 0.0, 'z_coord': 0.0})\n", "                else:\n", "                    entity_data.update({'x_coord': 0.0, 'y_coord': 0.0, 'z_coord': 0.0})\n", "            except:\n", "                entity_data.update({'x_coord': 0.0, 'y_coord': 0.0, 'z_coord': 0.0})\n", "                \n", "        <PERSON><PERSON>(entity.dxf, 'location'):\n", "            # POINT entities and others with location\n", "            location = entity.dxf.location\n", "            entity_data.update({\n", "                'x_coord': location.x,\n", "                'y_coord': location.y,\n", "                'z_coord': location.z\n", "            })\n", "            \n", "        else:\n", "            # Default coordinates for unknown entity types\n", "            entity_data.update({\n", "                'x_coord': 0.0,\n", "                'y_coord': 0.0,\n", "                'z_coord': 0.0\n", "            })\n", "        \n", "        # Extract text content\n", "        if entity_type in ['TEXT', 'MTEXT']:\n", "            entity_data['text_content'] = getattr(entity.dxf, 'text', '')\n", "            if hasattr(entity.dxf, 'height'):\n", "                entity_data['text_height'] = entity.dxf.height\n", "        \n", "        # Extract dimension information\n", "        if 'DIMENSION' in entity_type:\n", "            entity_data['dimension_type'] = entity_type\n", "            if hasattr(entity.dxf, 'text'):\n", "                entity_data['dimension_text'] = entity.dxf.text\n", "        \n", "        # Classification\n", "        entity_data['classification'] = classifier.classify_entity(entity_data)\n", "        entity_data['extraction_timestamp'] = datetime.now().isoformat()\n", "        \n", "        return entity_data\n", "        \n", "    except Exception as e:\n", "        logger.warning(f\"Error extracting entity data: {e}\")\n", "        return None\n", "\n", "print(\"Generic entity extraction function defined\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Select CAD file(s) to process\n", "if target_files:\n", "    # Use specified files\n", "    files_to_process = []\n", "    for target in target_files:\n", "        matching_files = [f for f in cad_files if f.name == target]\n", "        files_to_process.extend(matching_files)\n", "else:\n", "    # Process all discovered files (or first few for exploration)\n", "    files_to_process = cad_files[:2]  # Limit to first 2 files for exploration\n", "\n", "if not files_to_process:\n", "    print(\"No CAD files to process\")\n", "    entities_df = pd.DataFrame()\n", "else:\n", "    print(f\"\\n=== PROCESSING CAD FILES ===\")\n", "    print(f\"Files to process: {[f.name for f in files_to_process]}\")\n", "    \n", "    all_entities = []\n", "    processing_stats = defaultdict(int)\n", "    file_stats = {}\n", "    \n", "    for file_path in files_to_process:\n", "        print(f\"\\nProcessing: {file_path.name}\")\n", "        \n", "        try:\n", "            # Load and process DXF file\n", "            doc = ezdxf.readfile(file_path)\n", "            msp = doc.modelspace()\n", "            \n", "            file_entities = []\n", "            file_processing_stats = defaultdict(int)\n", "            \n", "            for entity in msp:\n", "                file_processing_stats['total_entities'] += 1\n", "                processing_stats['total_entities'] += 1\n", "                \n", "                entity_data = extract_entity_data(entity)\n", "                if entity_data:\n", "                    # Add source file information\n", "                    entity_data['source_file'] = file_path.name\n", "                    entity_data['source_path'] = str(file_path)\n", "                    \n", "                    file_entities.append(entity_data)\n", "                    all_entities.append(entity_data)\n", "                    \n", "                    file_processing_stats['successful_extractions'] += 1\n", "                    processing_stats['successful_extractions'] += 1\n", "                    \n", "                    classification = entity_data['classification']\n", "                    file_processing_stats[f\"classification_{classification}\"] += 1\n", "                    processing_stats[f\"classification_{classification}\"] += 1\n", "                else:\n", "                    file_processing_stats['failed_extractions'] += 1\n", "                    processing_stats['failed_extractions'] += 1\n", "            \n", "            file_stats[file_path.name] = dict(file_processing_stats)\n", "            \n", "            print(f\"  Entities: {file_processing_stats['total_entities']}\")\n", "            print(f\"  Extracted: {file_processing_stats['successful_extractions']}\")\n", "            print(f\"  Failed: {file_processing_stats['failed_extractions']}\")\n", "            \n", "        except Exception as e:\n", "            logger.error(f\"Error processing {file_path.name}: {e}\")\n", "            continue\n", "    \n", "    print(f\"\\n=== OVERALL PROCESSING SUMMARY ===\")\n", "    print(f\"Files processed: {len([f for f in file_stats.keys()])}\")\n", "    print(f\"Total entities: {processing_stats['total_entities']}\")\n", "    print(f\"Successful extractions: {processing_stats['successful_extractions']}\")\n", "    print(f\"Failed extractions: {processing_stats['failed_extractions']}\")\n", "    \n", "    # Convert to DataFrame\n", "    if all_entities:\n", "        entities_df = pd.DataFrame(all_entities)\n", "        print(f\"\\nExtracted entities DataFrame: {len(entities_df)} rows, {len(entities_df.columns)} columns\")\n", "        \n", "        # Show classification summary\n", "        classification_counts = entities_df['classification'].value_counts()\n", "        print(f\"\\nClassification Summary:\")\n", "        for classification, count in classification_counts.items():\n", "            print(f\"  {classification}: {count}\")\n", "    else:\n", "        print(\"No entities extracted\")\n", "        entities_df = pd.DataFrame()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Exploratory Data Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not entities_df.empty:\n", "    print(\"\\n=== EXPLORATORY DATA ANALYSIS ===\")\n", "    \n", "    # Basic statistics\n", "    print(f\"\\nDataset Overview:\")\n", "    print(f\"  Total entities: {len(entities_df)}\")\n", "    print(f\"  Unique layers: {entities_df['layer_name'].nunique()}\")\n", "    print(f\"  Unique entity types: {entities_df['entity_type'].nunique()}\")\n", "    print(f\"  Source files: {entities_df['source_file'].nunique() if 'source_file' in entities_df.columns else 1}\")\n", "    \n", "    # Entity type distribution\n", "    print(f\"\\nEntity Type Distribution:\")\n", "    entity_type_counts = entities_df['entity_type'].value_counts()\n", "    for entity_type, count in entity_type_counts.head(10).items():\n", "        percentage = (count / len(entities_df)) * 100\n", "        print(f\"  {entity_type}: {count} ({percentage:.1f}%)\")\n", "    \n", "    # Layer distribution\n", "    print(f\"\\nTop 10 Layers:\")\n", "    layer_counts = entities_df['layer_name'].value_counts()\n", "    for layer, count in layer_counts.head(10).items():\n", "        percentage = (count / len(entities_df)) * 100\n", "        print(f\"  {layer}: {count} ({percentage:.1f}%)\")\n", "    \n", "    # Classification distribution\n", "    print(f\"\\nClassification Distribution:\")\n", "    classification_counts = entities_df['classification'].value_counts()\n", "    for classification, count in classification_counts.items():\n", "        percentage = (count / len(entities_df)) * 100\n", "        print(f\"  {classification}: {count} ({percentage:.1f}%)\")\n", "    \n", "    # Coordinate analysis\n", "    print(f\"\\nSpatial Extent:\")\n", "    valid_coords = entities_df[(entities_df['x_coord'] != 0) | (entities_df['y_coord'] != 0)]\n", "    if not valid_coords.empty:\n", "        print(f\"  X range: {valid_coords['x_coord'].min():.2f} to {valid_coords['x_coord'].max():.2f}\")\n", "        print(f\"  Y range: {valid_coords['y_coord'].min():.2f} to {valid_coords['y_coord'].max():.2f}\")\n", "        print(f\"  Entities with coordinates: {len(valid_coords)} ({len(valid_coords)/len(entities_df)*100:.1f}%)\")\n", "        \n", "        # Coordinate system detection\n", "        x_magnitude = max(abs(valid_coords['x_coord'].min()), abs(valid_coords['x_coord'].max()))\n", "        y_magnitude = max(abs(valid_coords['y_coord'].min()), abs(valid_coords['y_coord'].max()))\n", "        \n", "        if x_magnitude > 100000 and y_magnitude > 100000:\n", "            coord_system_guess = \"UTM or similar projected coordinate system\"\n", "        elif x_magnitude < 1000 and y_magnitude < 1000:\n", "            coord_system_guess = \"Local coordinate system or relative coordinates\"\n", "        else:\n", "            coord_system_guess = \"Unknown coordinate system\"\n", "        \n", "        print(f\"  Likely coordinate system: {coord_system_guess}\")\n", "    else:\n", "        print(f\"  No valid coordinates found\")\n", "    \n", "    # Infrastructure-specific analysis\n", "    print(f\"\\n=== INFRASTRUCTURE ANALYSIS ===\")\n", "    \n", "    for classification in classification_counts.index:\n", "        if classification != 'unknown':\n", "            subset = entities_df[entities_df['classification'] == classification]\n", "            print(f\"\\n{classification.title()} Analysis:\")\n", "            print(f\"  Count: {len(subset)}\")\n", "            \n", "            # Entity types within this classification\n", "            entity_types = subset['entity_type'].value_counts()\n", "            print(f\"  Entity types: {dict(entity_types)}\")\n", "            \n", "            # Layers within this classification\n", "            layers = subset['layer_name'].value_counts()\n", "            print(f\"  Layers: {dict(layers)}\")\n", "            \n", "            # Spatial distribution if coordinates available\n", "            valid_subset = subset[(subset['x_coord'] != 0) | (subset['y_coord'] != 0)]\n", "            if not valid_subset.empty:\n", "                print(f\"  Spatial extent: X({valid_subset['x_coord'].min():.1f} to {valid_subset['x_coord'].max():.1f}), Y({valid_subset['y_coord'].min():.1f} to {valid_subset['y_coord'].max():.1f})\")\n", "    \n", "    # Data quality assessment\n", "    print(f\"\\n=== DATA QUALITY ASSESSMENT ===\")\n", "    \n", "    # Missing coordinates\n", "    missing_coords = len(entities_df[(entities_df['x_coord'] == 0) & (entities_df['y_coord'] == 0)])\n", "    print(f\"  Entities without coordinates: {missing_coords} ({missing_coords/len(entities_df)*100:.1f}%)\")\n", "    \n", "    # Unknown classifications\n", "    unknown_count = classification_counts.get('unknown', 0)\n", "    print(f\"  Unknown classifications: {unknown_count} ({unknown_count/len(entities_df)*100:.1f}%)\")\n", "    \n", "    # Layer diversity\n", "    layer_diversity = entities_df['layer_name'].nunique() / len(entities_df)\n", "    print(f\"  Layer diversity: {layer_diversity:.3f} (higher = more diverse)\")\n", "    \n", "    # Classification success rate\n", "    classification_rate = (len(entities_df) - unknown_count) / len(entities_df) * 100\n", "    print(f\"  Classification success rate: {classification_rate:.1f}%\")\n", "    \n", "else:\n", "    print(\"No entities to analyze\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Export Results and Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not entities_df.empty:\n", "    # Create output directory\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    output_path = Path(output_dir)\n", "    run_output_dir = output_path / f\"{project_type}_{site_name}_{timestamp}\"\n", "    run_output_dir.mkdir(parents=True, exist_ok=True)\n", "    \n", "    print(f\"\\n=== EXPORTING RESULTS ===\")\n", "    print(f\"Output directory: {run_output_dir}\")\n", "    \n", "    # Main entities file\n", "    main_file = run_output_dir / f\"exploratory_cad_extraction_{timestamp}.csv\"\n", "    entities_df.to_csv(main_file, index=False)\n", "    print(f\"Main entities file: {main_file.name} ({len(entities_df)} entities)\")\n", "    \n", "    # Classification-specific files\n", "    for classification in entities_df['classification'].unique():\n", "        if classification != 'unknown':\n", "            subset_df = entities_df[entities_df['classification'] == classification]\n", "            if not subset_df.empty:\n", "                subset_file = run_output_dir / f\"{classification}_entities_{timestamp}.csv\"\n", "                subset_df.to_csv(subset_file, index=False)\n", "                print(f\"{classification.title()} file: {subset_file.name} ({len(subset_df)} entities)\")\n", "    \n", "    # Layer-specific files (for major layers)\n", "    major_layers = entities_df['layer_name'].value_counts().head(5)\n", "    for layer_name, count in major_layers.items():\n", "        if count > 10:  # Only export layers with significant entity counts\n", "            layer_df = entities_df[entities_df['layer_name'] == layer_name]\n", "            safe_layer_name = layer_name.replace(' ', '_').replace('-', '_')\n", "            layer_file = run_output_dir / f\"layer_{safe_layer_name}_{timestamp}.csv\"\n", "            layer_df.to_csv(layer_file, index=False)\n", "            print(f\"Layer file: {layer_file.name} ({count} entities)\")\n", "    \n", "    # Analysis summary\n", "    summary_data = {\n", "        'extraction_metadata': {\n", "            'timestamp': timestamp,\n", "            'project': project_type,\n", "            'site': site_name,\n", "            'source_files': list(entities_df['source_file'].unique()) if 'source_file' in entities_df.columns else ['unknown'],\n", "            'coordinate_system': coordinate_system\n", "        },\n", "        'processing_statistics': dict(processing_stats),\n", "        'file_statistics': file_stats if 'file_stats' in locals() else {},\n", "        'data_summary': {\n", "            'total_entities': len(entities_df),\n", "            'unique_layers': entities_df['layer_name'].nunique(),\n", "            'unique_entity_types': entities_df['entity_type'].nunique(),\n", "            'classification_distribution': entities_df['classification'].value_counts().to_dict(),\n", "            'entity_type_distribution': entities_df['entity_type'].value_counts().to_dict(),\n", "            'layer_distribution': entities_df['layer_name'].value_counts().head(10).to_dict()\n", "        },\n", "        'spatial_analysis': {\n", "            'coordinate_system_guess': coord_system_guess if 'coord_system_guess' in locals() else 'unknown',\n", "            'entities_with_coordinates': len(valid_coords) if 'valid_coords' in locals() else 0,\n", "            'spatial_extent': {\n", "                'x_min': float(valid_coords['x_coord'].min()) if 'valid_coords' in locals() and not valid_coords.empty else None,\n", "                'x_max': float(valid_coords['x_coord'].max()) if 'valid_coords' in locals() and not valid_coords.empty else None,\n", "                'y_min': float(valid_coords['y_coord'].min()) if 'valid_coords' in locals() and not valid_coords.empty else None,\n", "                'y_max': float(valid_coords['y_coord'].max()) if 'valid_coords' in locals() and not valid_coords.empty else None\n", "            }\n", "        },\n", "        'quality_metrics': {\n", "            'classification_success_rate': classification_rate if 'classification_rate' in locals() else 0,\n", "            'entities_without_coordinates': missing_coords if 'missing_coords' in locals() else 0,\n", "            'unknown_classifications': unknown_count if 'unknown_count' in locals() else 0\n", "        }\n", "    }\n", "    \n", "    summary_file = run_output_dir / f\"extraction_summary_{timestamp}.json\"\n", "    with open(summary_file, 'w') as f:\n", "        json.dump(summary_data, f, indent=2, default=str)\n", "    print(f\"Summary file: {summary_file.name}\")\n", "    \n", "    print(f\"\\n✅ Export completed successfully\")\n", "    print(f\"📁 All files saved to: {run_output_dir}\")\n", "    \n", "    print(f\"\\n🎯 Recommended Next Steps:\")\n", "    print(f\"  1. Review classification results and adjust rules if needed\")\n", "    print(f\"  2. Use extracted coordinates for point cloud alignment\")\n", "    print(f\"  3. Validate coordinate system and apply transformations if needed\")\n", "    print(f\"  4. Run validation analysis (03_cad_extraction_accuracy_analysis.ipynb)\")\n", "    print(f\"  5. Integrate with downstream workflows (pile detection, alignment)\")\n", "    \n", "else:\n", "    print(\"No entities to export\")\n", "\n", "print(f\"\\n📅 Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}