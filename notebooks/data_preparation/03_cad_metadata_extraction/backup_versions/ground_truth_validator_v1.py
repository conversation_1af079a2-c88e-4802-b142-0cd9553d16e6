#!/usr/bin/env python3
"""
Ground Truth Validation Script for Montalto di Castro CAD Extraction

This script validates CAD extraction results against the ground truth specifications
and provides detailed analysis of infrastructure element detection accuracy.

Author: Preeta<PERSON> Balijepalli
Date: July 2025
Project: As-Built Foundation Analysis - Castro
"""

import pandas as pd
import numpy as np
from pathlib import Path
import json
from datetime import datetime
from collections import defaultdict

def load_ground_truth():
    """Load ground truth specifications."""
    ground_truth_path = Path("../../../data/reference/montalto_di_castro_ground_truth.csv")
    
    if not ground_truth_path.exists():
        print(f"Ground truth file not found: {ground_truth_path}")
        return {}
    
    ground_truth_df = pd.read_csv(ground_truth_path)
    
    # Convert to dictionary for easy lookup
    ground_truth = {}
    for _, row in ground_truth_df.iterrows():
        key = row['Features'].lower().replace(' ', '_')
        ground_truth[key] = {
            'planned': row['Planned'],
            'unit': row['Unit'],
            'category': row['Categories'],
            'notes': row.get('Notes', '')
        }
    
    return ground_truth

def analyze_cad_extraction(cad_file_path):
    """Analyze existing CAD extraction results."""
    cad_file = Path(cad_file_path)
    
    if not cad_file.exists():
        print(f"CAD extraction file not found: {cad_file}")
        return {}
    
    # Load CAD extraction data
    cad_df = pd.read_csv(cad_file)
    
    print(f"Loaded {len(cad_df)} entities from CAD extraction")
    
    # Analyze by layer and entity type
    analysis = {
        'total_entities': len(cad_df),
        'layer_analysis': {},
        'entity_type_analysis': {},
        'coordinate_analysis': {},
        'infrastructure_estimates': {}
    }
    
    # Layer analysis
    layer_counts = cad_df['layer_name'].value_counts()
    analysis['layer_analysis'] = layer_counts.to_dict()
    
    # Entity type analysis
    entity_type_counts = cad_df['entity_type'].value_counts()
    analysis['entity_type_analysis'] = entity_type_counts.to_dict()
    
    # Coordinate analysis
    entities_with_coords = cad_df.dropna(subset=['x_coord', 'y_coord'])
    if len(entities_with_coords) > 0:
        analysis['coordinate_analysis'] = {
            'entities_with_coordinates': len(entities_with_coords),
            'coordinate_completeness': len(entities_with_coords) / len(cad_df),
            'x_range': [entities_with_coords['x_coord'].min(), entities_with_coords['x_coord'].max()],
            'y_range': [entities_with_coords['y_coord'].min(), entities_with_coords['y_coord'].max()],
            'z_range': [entities_with_coords['z_coord'].min(), entities_with_coords['z_coord'].max()]
        }
    
    # Infrastructure estimates based on current extraction
    infrastructure_estimates = estimate_infrastructure_quantities(cad_df)
    analysis['infrastructure_estimates'] = infrastructure_estimates
    
    return analysis

def estimate_infrastructure_quantities(cad_df):
    """Estimate infrastructure quantities from CAD data."""
    estimates = {}

    # Tracker analysis
    tracker_entities = cad_df[cad_df['layer_name'].str.contains('CVT_Tracker', na=False)]
    tracker_types = tracker_entities['layer_name'].value_counts()

    estimates['trackers'] = {
        'total_trackers': len(tracker_entities),
        'tracker_types': tracker_types.to_dict()
    }

    # Foundation pile analysis (discovered layers)
    foundation_pile_layers = ['CVT - PILE LATERAL', 'CVT - PILE DRIVE', 'CVT - Pile END']
    foundation_pile_entities = cad_df[
        (cad_df['layer_name'].isin(foundation_pile_layers)) &
        (cad_df['entity_type'] == 'LINE')
    ]

    foundation_breakdown = {}
    for layer in foundation_pile_layers:
        layer_piles = foundation_pile_entities[foundation_pile_entities['layer_name'] == layer]
        foundation_breakdown[layer] = len(layer_piles)

    estimates['foundation_piles'] = {
        'total_foundation_piles': len(foundation_pile_entities),
        'breakdown_by_layer': foundation_breakdown,
        'representation': 'LINE entities in specific layers'
    }
    
    # Calculate expected piles from trackers
    tracker_pile_mapping = {
        'CVT_Tracker 1x52 int': 52,
        'CVT_Tracker 1x52 ext': 52,
        'CVT_Tracker 1X52 Edge': 52,
        'CVT_Tracker 1x26 int': 26,
        'CVT_Tracker 1x26 ext': 26
    }
    
    total_expected_module_support_piles = 0
    for tracker_type, count in tracker_types.items():
        piles_per_tracker = tracker_pile_mapping.get(tracker_type, 0)
        total_expected_module_support_piles += count * piles_per_tracker
    
    estimates['module_support_piles'] = {
        'estimated_from_trackers': total_expected_module_support_piles,
        'breakdown_by_tracker': {
            tracker_type: count * tracker_pile_mapping.get(tracker_type, 0)
            for tracker_type, count in tracker_types.items()
        }
    }
    
    # Additional pile entities (general classification)
    general_pile_entities = cad_df[
        (cad_df['layer_name'].str.lower().str.contains('pile', na=False)) &
        (~cad_df['layer_name'].isin(foundation_pile_layers))  # Exclude already counted foundation piles
    ]
    estimates['general_pile_entities'] = len(general_pile_entities)
    
    # Roads and linear infrastructure
    road_keywords = ['road', 'strada', 'access']
    road_entities = cad_df[
        cad_df['layer_name'].str.lower().str.contains('|'.join(road_keywords), na=False)
    ]
    estimates['roads'] = {
        'entities': len(road_entities),
        'estimated_length': calculate_linear_length(road_entities) if len(road_entities) > 0 else 0
    }
    
    # Fencing
    fence_keywords = ['fence', 'recinzione', 'boundary']
    fence_entities = cad_df[
        cad_df['layer_name'].str.lower().str.contains('|'.join(fence_keywords), na=False)
    ]
    estimates['fencing'] = {
        'entities': len(fence_entities),
        'estimated_length': calculate_linear_length(fence_entities) if len(fence_entities) > 0 else 0
    }
    
    # Electrical infrastructure
    electrical_keywords = ['electrical', 'voltage', 'trench', 'cable']
    electrical_entities = cad_df[
        cad_df['layer_name'].str.lower().str.contains('|'.join(electrical_keywords), na=False)
    ]
    estimates['electrical'] = {
        'entities': len(electrical_entities),
        'estimated_length': calculate_linear_length(electrical_entities) if len(electrical_entities) > 0 else 0
    }
    
    return estimates

def calculate_linear_length(entities_df):
    """Calculate total linear length from line entities."""
    total_length = 0.0
    
    for _, entity in entities_df.iterrows():
        if entity.get('entity_type') == 'LINE' and 'length' in entity:
            total_length += entity.get('length', 0.0)
    
    return total_length

def validate_against_ground_truth(analysis, ground_truth):
    """Validate CAD analysis against ground truth."""
    validation_results = {}
    
    print(f"\n=== GROUND TRUTH VALIDATION RESULTS ===")
    print(f"{'Infrastructure Type':<25} {'Extracted':<12} {'Expected':<12} {'Match %':<10} {'Status':<15}")
    print(f"{'-'*25} {'-'*12} {'-'*12} {'-'*10} {'-'*15}")
    
    # Validation mappings
    validations = [
        ('trackers', 'tracker', analysis['infrastructure_estimates']['trackers']['total_trackers']),
        ('module_support_piles', 'modules', analysis['infrastructure_estimates']['module_support_piles']['estimated_from_trackers']),
        ('foundation_piles', 'piling', analysis['infrastructure_estimates']['foundation_piles']['total_foundation_piles']),
        ('roads', 'road', analysis['infrastructure_estimates']['roads']['estimated_length']),
        ('fencing', 'fence_chainlink', analysis['infrastructure_estimates']['fencing']['estimated_length']),
        ('electrical', 'low_voltage_trenching', analysis['infrastructure_estimates']['electrical']['estimated_length'])
    ]
    
    for validation_key, ground_truth_key, extracted_value in validations:
        if ground_truth_key in ground_truth:
            expected_value = ground_truth[ground_truth_key]['planned']
            
            if expected_value > 0:
                match_percentage = (extracted_value / expected_value) * 100
            else:
                match_percentage = 0
            
            # Determine status
            if match_percentage >= 95:
                status = '✅ Excellent'
            elif match_percentage >= 80:
                status = '✅ Good'
            elif match_percentage >= 50:
                status = '⚠️  Partial'
            else:
                status = '❌ Poor'
            
            validation_results[validation_key] = {
                'extracted': extracted_value,
                'expected': expected_value,
                'match_percentage': match_percentage,
                'status': status
            }
            
            print(f"{validation_key:<25} {extracted_value:<12.1f} {expected_value:<12.1f} {match_percentage:<10.1f} {status:<15}")
        else:
            print(f"{validation_key:<25} {extracted_value:<12.1f} {'N/A':<12} {'N/A':<10} {'No GT':<15}")
    
    return validation_results

def generate_validation_report(analysis, ground_truth, validation_results):
    """Generate comprehensive validation report."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    report = {
        'validation_metadata': {
            'timestamp': timestamp,
            'project': 'montalto_di_castro',
            'validation_type': 'ground_truth_comparison'
        },
        'cad_analysis_summary': analysis,
        'ground_truth_specifications': ground_truth,
        'validation_results': validation_results,
        'summary_statistics': {
            'total_validations': len(validation_results),
            'excellent_matches': sum(1 for v in validation_results.values() if 'Excellent' in v['status']),
            'good_matches': sum(1 for v in validation_results.values() if 'Good' in v['status']),
            'partial_matches': sum(1 for v in validation_results.values() if 'Partial' in v['status']),
            'poor_matches': sum(1 for v in validation_results.values() if 'Poor' in v['status'])
        }
    }
    
    # Calculate overall score
    total_validations = len(validation_results)
    if total_validations > 0:
        excellent_count = report['summary_statistics']['excellent_matches']
        good_count = report['summary_statistics']['good_matches']
        partial_count = report['summary_statistics']['partial_matches']
        poor_count = report['summary_statistics']['poor_matches']
        
        overall_score = ((excellent_count * 100 + good_count * 80 + partial_count * 60 + poor_count * 20) / total_validations)
        report['summary_statistics']['overall_validation_score'] = overall_score
    else:
        report['summary_statistics']['overall_validation_score'] = 0
    
    return report

def main():
    """Main validation function."""
    print("Ground Truth Validation for Montalto di Castro CAD Extraction")
    print("=" * 60)
    
    # Load ground truth
    ground_truth = load_ground_truth()
    if not ground_truth:
        print("Failed to load ground truth specifications")
        return
    
    print(f"Loaded {len(ground_truth)} ground truth specifications")
    
    # Analyze existing CAD extraction
    cad_file_path = "../../../data/raw/motali_de_castro/cad/enhanced_output/cad_extraction_pile_20250630_143601.csv"
    analysis = analyze_cad_extraction(cad_file_path)
    
    if not analysis:
        print("Failed to analyze CAD extraction")
        return
    
    # Perform validation
    validation_results = validate_against_ground_truth(analysis, ground_truth)
    
    # Generate comprehensive report
    report = generate_validation_report(analysis, ground_truth, validation_results)
    
    # Save report
    output_dir = Path("../../../output_runs/validation")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    report_file = output_dir / f"ground_truth_validation_{report['validation_metadata']['timestamp']}.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    print(f"\n=== VALIDATION SUMMARY ===")
    print(f"Overall Validation Score: {report['summary_statistics']['overall_validation_score']:.1f}/100")
    print(f"Excellent matches: {report['summary_statistics']['excellent_matches']}")
    print(f"Good matches: {report['summary_statistics']['good_matches']}")
    print(f"Partial matches: {report['summary_statistics']['partial_matches']}")
    print(f"Poor matches: {report['summary_statistics']['poor_matches']}")
    
    print(f"\nValidation report saved to: {report_file}")
    
    # Recommendations
    overall_score = report['summary_statistics']['overall_validation_score']
    print(f"\n=== RECOMMENDATIONS ===")
    if overall_score >= 80:
        print("✅ Excellent validation results! CAD extraction closely matches ground truth.")
    elif overall_score >= 60:
        print("⚠️  Good validation with some gaps. Consider enhancing extraction for partial matches.")
    else:
        print("❌ Validation shows significant gaps. CAD extraction needs improvement.")
    
    print("\nNext steps:")
    print("1. Review detailed validation report for specific infrastructure gaps")
    print("2. Enhance CAD extraction classification for poor/partial matches")
    print("3. Consider additional CAD files for missing infrastructure elements")
    print("4. Update pile area assignment system with improved classifications")

if __name__ == "__main__":
    main()
