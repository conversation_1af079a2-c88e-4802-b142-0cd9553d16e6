{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Pile Area Optimized CAD Extraction\n", "\n", "Enhanced CAD metadata extraction specifically optimized for pile area assignment system.\n", "\n", "## Key Features:\n", "- Optimized tracker entity extraction and classification\n", "- Enhanced coordinate validation and quality checks\n", "- Pile area assignment compatibility\n", "- Comprehensive validation against expected quantities\n", "\n", "**Expected Quantities for Montalto di Castro:**\n", "- Piling: 4,199\n", "- Trackers: 543\n", "- Modules: 23,764\n", "- Pile Holes: 4,199"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "import ezdxf\n", "import json\n", "from datetime import datetime\n", "from collections import defaultdict\n", "import logging\n", "import mlflow\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "# Configuration\n", "project_type = \"motali_de_castro\"\n", "site_name = \"main_site\"\n", "coordinate_system = \"UTM_33N_WGS84\"\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "# Expected quantities for validation\n", "EXPECTED_QUANTITIES = {\n", "    'piling': 4199,\n", "    'trackers': 543,\n", "    'modules': 23764,\n", "    'pile_holes': 4199\n", "}\n", "\n", "# Tracker type configurations\n", "TRACKER_CONFIGS = {\n", "    'CVT_Tracker 1x52 int': {'piles_per_tracker': 52, 'type': 'internal'},\n", "    'CVT_Tracker 1x52 ext': {'piles_per_tracker': 52, 'type': 'external'},\n", "    'CVT_Tracker 1X52 Edge': {'piles_per_tracker': 52, 'type': 'edge'},\n", "    'CVT_Tracker 1x26 int': {'piles_per_tracker': 26, 'type': 'internal'},\n", "    'CVT_Tracker 1x26 ext': {'piles_per_tracker': 26, 'type': 'external'}\n", "}\n", "\n", "print(f\"Pile Area Optimized CAD Extraction - {project_type.title()}\")\n", "print(f\"Timestamp: {timestamp}\")\n", "print(\"=\"*60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Enhanced Entity Classification"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class PileAreaCADClassifier:\n", "    \"\"\"Enhanced CAD entity classifier optimized for pile area assignment.\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.tracker_patterns = list(TRACKER_CONFIGS.keys())\n", "        self.pile_keywords = ['pile', 'piling', 'foundation', 'found']\n", "        self.electrical_keywords = ['electrical', 'voltage', 'trench', 'cable']\n", "        self.fence_keywords = ['fence', 'recinzione', 'boundary']\n", "        \n", "    def classify_entity(self, entity, layer_name, block_name, text_content):\n", "        \"\"\"Enhanced classification with tracker-specific logic.\"\"\"\n", "        layer_lower = layer_name.lower()\n", "        block_lower = block_name.lower()\n", "        text_lower = text_content.lower()\n", "        \n", "        # Tracker classification (highest priority)\n", "        for tracker_pattern in self.tracker_patterns:\n", "            if tracker_pattern in layer_name:\n", "                return 'tracker'\n", "        \n", "        # Pile/foundation classification\n", "        if any(keyword in layer_lower for keyword in self.pile_keywords):\n", "            return 'pile'\n", "        \n", "        if any(keyword in block_lower for keyword in self.pile_keywords):\n", "            return 'pile'\n", "        \n", "        # Electrical infrastructure\n", "        if any(keyword in layer_lower for keyword in self.electrical_keywords):\n", "            return 'electrical'\n", "        \n", "        # Fencing\n", "        if any(keyword in layer_lower for keyword in self.fence_keywords):\n", "            return 'fence'\n", "        \n", "        # Text annotations\n", "        if entity.dxftype() in ['TEXT', 'MTEXT'] and text_content:\n", "            if any(keyword in text_lower for keyword in self.pile_keywords):\n", "                return 'annotation_pile'\n", "            return 'annotation'\n", "        \n", "        return 'unknown'\n", "    \n", "    def get_tracker_type(self, layer_name):\n", "        \"\"\"Get specific tracker type from layer name.\"\"\"\n", "        for tracker_pattern in self.tracker_patterns:\n", "            if tracker_pattern in layer_name:\n", "                return tracker_pattern\n", "        return None\n", "    \n", "    def validate_tracker_entity(self, entity_data):\n", "        \"\"\"Validate tracker entity data quality.\"\"\"\n", "        issues = []\n", "        \n", "        # Check coordinates\n", "        if pd.isna(entity_data.get('x_coord')) or pd.isna(entity_data.get('y_coord')):\n", "            issues.append('missing_coordinates')\n", "        \n", "        # Check if it's an INSERT entity (expected for trackers)\n", "        if entity_data.get('entity_type') != 'INSERT':\n", "            issues.append('not_insert_entity')\n", "        \n", "        # Check if block name is present\n", "        if not entity_data.get('block_name'):\n", "            issues.append('missing_block_name')\n", "        \n", "        return issues\n", "\n", "# Initialize classifier\n", "classifier = PileAreaCADClassifier()\n", "print(\"Enhanced CAD classifier initialized\")\n", "print(f\"Tracking {len(TRACKER_CONFIGS)} tracker types\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Enhanced Entity Extraction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_enhanced_entity_data(entity, classifier):\n", "    \"\"\"Extract comprehensive entity data with enhanced validation.\"\"\"\n", "    try:\n", "        entity_type = entity.dxftype()\n", "        layer_name = getattr(entity.dxf, 'layer', 'unknown')\n", "        \n", "        # Base entity data\n", "        entity_data = {\n", "            'entity_id': str(entity.dxf.handle),\n", "            'entity_type': entity_type,\n", "            'layer_name': layer_name,\n", "            'color': getattr(entity.dxf, 'color', 256),\n", "            'linetype': getattr(entity.dxf, 'linetype', 'Continuous'),\n", "            'lineweight': getattr(entity.dxf, 'lineweight', -1)\n", "        }\n", "        \n", "        # Extract coordinates based on entity type\n", "        if entity_type == 'INSERT':\n", "            # INSERT entities (trackers)\n", "            insert_point = getattr(entity.dxf, 'insert', (0, 0, 0))\n", "            entity_data.update({\n", "                'x_coord': float(insert_point[0]),\n", "                'y_coord': float(insert_point[1]),\n", "                'z_coord': float(insert_point[2]),\n", "                'geometry_type': 'insert',\n", "                'block_name': getattr(entity.dxf, 'name', ''),\n", "                'rotation': getattr(entity.dxf, 'rotation', 0.0),\n", "                'scale_x': getattr(entity.dxf, 'xscale', 1.0),\n", "                'scale_y': getattr(entity.dxf, 'yscale', 1.0),\n", "                'scale_z': getattr(entity.dxf, 'zscale', 1.0)\n", "            })\n", "            \n", "        elif entity_type == 'CIRCLE':\n", "            # CIRCLE entities (potential piles)\n", "            center = getattr(entity.dxf, 'center', (0, 0, 0))\n", "            entity_data.update({\n", "                'x_coord': float(center[0]),\n", "                'y_coord': float(center[1]),\n", "                'z_coord': float(center[2]),\n", "                'geometry_type': 'circle',\n", "                'radius': getattr(entity.dxf, 'radius', 0.0)\n", "            })\n", "            \n", "        elif entity_type == 'LINE':\n", "            # LINE entities\n", "            start = getattr(entity.dxf, 'start', (0, 0, 0))\n", "            end = getattr(entity.dxf, 'end', (0, 0, 0))\n", "            # Use midpoint as representative coordinate\n", "            entity_data.update({\n", "                'x_coord': float((start[0] + end[0]) / 2),\n", "                'y_coord': float((start[1] + end[1]) / 2),\n", "                'z_coord': float((start[2] + end[2]) / 2),\n", "                'geometry_type': 'line',\n", "                'start_x': float(start[0]),\n", "                'start_y': float(start[1]),\n", "                'start_z': float(start[2]),\n", "                'end_x': float(end[0]),\n", "                'end_y': float(end[1]),\n", "                'end_z': float(end[2]),\n", "                'length': float(np.linalg.norm(np.array(end) - np.array(start)))\n", "            })\n", "            \n", "        elif entity_type in ['TEXT', 'MTEXT']:\n", "            # Text entities\n", "            insert_point = getattr(entity.dxf, 'insert', (0, 0, 0))\n", "            entity_data.update({\n", "                'x_coord': float(insert_point[0]),\n", "                'y_coord': float(insert_point[1]),\n", "                'z_coord': float(insert_point[2]),\n", "                'geometry_type': 'text',\n", "                'text_content': getattr(entity.dxf, 'text', ''),\n", "                'text_height': getattr(entity.dxf, 'height', 0.0),\n", "                'text_rotation': getattr(entity.dxf, 'rotation', 0.0)\n", "            })\n", "            \n", "        else:\n", "            # Default handling for other entity types\n", "            entity_data.update({\n", "                'x_coord': 0.0,\n", "                'y_coord': 0.0,\n", "                'z_coord': 0.0,\n", "                'geometry_type': entity_type.lower()\n", "            })\n", "        \n", "        # Classify entity\n", "        block_name = entity_data.get('block_name', '')\n", "        text_content = entity_data.get('text_content', '')\n", "        classification = classifier.classify_entity(entity, layer_name, block_name, text_content)\n", "        entity_data['classification'] = classification\n", "        \n", "        # Add tracker-specific information\n", "        if classification == 'tracker':\n", "            tracker_type = classifier.get_tracker_type(layer_name)\n", "            if tracker_type:\n", "                entity_data['tracker_type'] = tracker_type\n", "                entity_data['expected_piles'] = TRACKER_CONFIGS[tracker_type]['piles_per_tracker']\n", "                entity_data['tracker_category'] = TRACKER_CONFIGS[tracker_type]['type']\n", "                \n", "                # Validate tracker data quality\n", "                validation_issues = classifier.validate_tracker_entity(entity_data)\n", "                entity_data['validation_issues'] = ','.join(validation_issues) if validation_issues else 'none'\n", "                entity_data['data_quality'] = 'good' if not validation_issues else 'needs_review'\n", "        \n", "        # Add extraction metadata\n", "        entity_data['extraction_timestamp'] = datetime.now().isoformat()\n", "        \n", "        return entity_data\n", "        \n", "    except Exception as e:\n", "        logger.warning(f\"Error extracting entity data: {e}\")\n", "        return {\n", "            'entity_id': 'error',\n", "            'entity_type': getattr(entity, 'dxftype', lambda: 'unknown')(),\n", "            'error': str(e),\n", "            'extraction_timestamp': datetime.now().isoformat()\n", "        }\n", "\n", "print(\"Enhanced entity extraction function defined\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Process CAD Files"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define input and output paths\n", "cad_input_dir = Path(\"../../../data/raw/motali_de_castro/cad\")\n", "output_base_dir = Path(\"../../../output_runs/cad_metadata\")\n", "run_output_dir = output_base_dir / f\"{project_type}_{site_name}_{timestamp}\"\n", "run_output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Find CAD files\n", "dxf_files = list(cad_input_dir.glob(\"**/*.dxf\"))\n", "main_dxf_file = cad_input_dir / \"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\"\n", "\n", "print(f\"Found {len(dxf_files)} DXF files\")\n", "print(f\"Main DXF file: {main_dxf_file.name}\")\n", "print(f\"Output directory: {run_output_dir}\")\n", "\n", "# Process main DXF file\n", "if main_dxf_file.exists():\n", "    print(f\"\\nProcessing main DXF file: {main_dxf_file.name}\")\n", "    \n", "    try:\n", "        doc = ezdxf.readfile(main_dxf_file)\n", "        msp = doc.modelspace()\n", "        \n", "        print(f\"DXF version: {doc.dxfversion}\")\n", "        print(f\"Total entities in modelspace: {len(msp)}\")\n", "        print(f\"Total layers: {len(doc.layers)}\")\n", "        print(f\"Total blocks: {len(doc.blocks)}\")\n", "        \n", "        # Extract all entities\n", "        all_entities = []\n", "        processing_stats = {\n", "            'total_entities': 0,\n", "            'successful_extractions': 0,\n", "            'failed_extractions': 0,\n", "            'classification_counts': defaultdict(int),\n", "            'entity_type_counts': defaultdict(int),\n", "            'tracker_type_counts': defaultdict(int)\n", "        }\n", "        \n", "        # Process modelspace entities\n", "        print(\"\\nProcessing modelspace entities...\")\n", "        for entity in msp:\n", "            entity_data = extract_enhanced_entity_data(entity, classifier)\n", "            entity_data['source_file'] = main_dxf_file.name\n", "            entity_data['source_space'] = 'modelspace'\n", "            all_entities.append(entity_data)\n", "            \n", "            # Update statistics\n", "            processing_stats['total_entities'] += 1\n", "            if 'error' not in entity_data:\n", "                processing_stats['successful_extractions'] += 1\n", "                processing_stats['classification_counts'][entity_data.get('classification', 'unknown')] += 1\n", "                processing_stats['entity_type_counts'][entity_data.get('entity_type', 'unknown')] += 1\n", "                \n", "                if entity_data.get('classification') == 'tracker':\n", "                    tracker_type = entity_data.get('tracker_type', 'unknown')\n", "                    processing_stats['tracker_type_counts'][tracker_type] += 1\n", "            else:\n", "                processing_stats['failed_extractions'] += 1\n", "        \n", "        print(f\"Processed {processing_stats['total_entities']} entities\")\n", "        print(f\"Successful: {processing_stats['successful_extractions']}\")\n", "        print(f\"Failed: {processing_stats['failed_extractions']}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error processing DXF file: {e}\")\n", "        all_entities = []\n", "        processing_stats = {}\n", "        \n", "else:\n", "    print(f\"Main DXF file not found: {main_dxf_file}\")\n", "    all_entities = []\n", "    processing_stats = {}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Analysis and Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create DataFrame and analyze results\n", "if all_entities:\n", "    entities_df = pd.DataFrame(all_entities)\n", "    \n", "    print(\"\\n=== EXTRACTION ANALYSIS ===\")\n", "    print(f\"Total entities extracted: {len(entities_df)}\")\n", "    \n", "    # Classification summary\n", "    print(f\"\\nClassification Summary:\")\n", "    for classification, count in processing_stats['classification_counts'].items():\n", "        percentage = (count / processing_stats['successful_extractions']) * 100\n", "        print(f\"  {classification}: {count} ({percentage:.1f}%)\")\n", "    \n", "    # Tracker analysis\n", "    tracker_entities = entities_df[entities_df['classification'] == 'tracker']\n", "    print(f\"\\n=== TRACKER ANALYSIS ===\")\n", "    print(f\"Total tracker entities: {len(tracker_entities)}\")\n", "    \n", "    if len(tracker_entities) > 0:\n", "        print(f\"\\nTracker Type Distribution:\")\n", "        total_expected_piles = 0\n", "        \n", "        for tracker_type, count in processing_stats['tracker_type_counts'].items():\n", "            if tracker_type in TRACKER_CONFIGS:\n", "                piles_per_tracker = TRACKER_CONFIGS[tracker_type]['piles_per_tracker']\n", "                expected_piles = count * piles_per_tracker\n", "                total_expected_piles += expected_piles\n", "                \n", "                print(f\"  {tracker_type}:\")\n", "                print(f\"    Count: {count}\")\n", "                print(f\"    Piles per tracker: {piles_per_tracker}\")\n", "                print(f\"    Expected total piles: {expected_piles:,}\")\n", "        \n", "        print(f\"\\nTotal Expected Piles from Trackers: {total_expected_piles:,}\")\n", "        print(f\"Project Expected Piles: {EXPECTED_QUANTITIES['piling']:,}\")\n", "        print(f\"Project Expected Trackers: {EXPECTED_QUANTITIES['trackers']:,}\")\n", "        \n", "        # Validation against expected quantities\n", "        tracker_count_match = (len(tracker_entities) / EXPECTED_QUANTITIES['trackers']) * 100\n", "        pile_count_match = (total_expected_piles / EXPECTED_QUANTITIES['piling']) * 100\n", "        \n", "        print(f\"\\n=== VALIDATION RESULTS ===\")\n", "        print(f\"Tracker count match: {tracker_count_match:.1f}% ({len(tracker_entities)}/{EXPECTED_QUANTITIES['trackers']})\")\n", "        print(f\"Pile count match: {pile_count_match:.1f}% ({total_expected_piles:,}/{EXPECTED_QUANTITIES['piling']:,})\")\n", "        \n", "        # Data quality assessment\n", "        quality_good = len(tracker_entities[tracker_entities['data_quality'] == 'good'])\n", "        quality_issues = len(tracker_entities[tracker_entities['data_quality'] == 'needs_review'])\n", "        \n", "        print(f\"\\nData Quality Assessment:\")\n", "        print(f\"  Good quality: {quality_good} ({(quality_good/len(tracker_entities))*100:.1f}%)\")\n", "        print(f\"  Needs review: {quality_issues} ({(quality_issues/len(tracker_entities))*100:.1f}%)\")\n", "        \n", "        if quality_issues > 0:\n", "            print(f\"\\nCommon validation issues:\")\n", "            issue_counts = defaultdict(int)\n", "            for _, row in tracker_entities[tracker_entities['data_quality'] == 'needs_review'].iterrows():\n", "                issues = row['validation_issues'].split(',')\n", "                for issue in issues:\n", "                    if issue.strip() != 'none':\n", "                        issue_counts[issue.strip()] += 1\n", "            \n", "            for issue, count in issue_counts.items():\n", "                print(f\"    {issue}: {count} entities\")\n", "    \n", "    # Coordinate analysis\n", "    entities_with_coords = entities_df.dropna(subset=['x_coord', 'y_coord'])\n", "    print(f\"\\n=== COORDINATE ANALYSIS ===\")\n", "    print(f\"Entities with valid coordinates: {len(entities_with_coords)}/{len(entities_df)} ({(len(entities_with_coords)/len(entities_df))*100:.1f}%)\")\n", "    \n", "    if len(entities_with_coords) > 0:\n", "        print(f\"\\nCoordinate bounds:\")\n", "        print(f\"  X: {entities_with_coords['x_coord'].min():.2f} to {entities_with_coords['x_coord'].max():.2f}\")\n", "        print(f\"  Y: {entities_with_coords['y_coord'].min():.2f} to {entities_with_coords['y_coord'].max():.2f}\")\n", "        print(f\"  Z: {entities_with_coords['z_coord'].min():.2f} to {entities_with_coords['z_coord'].max():.2f}\")\n", "        \n", "        # Tracker coordinate analysis\n", "        tracker_coords = tracker_entities.dropna(subset=['x_coord', 'y_coord'])\n", "        if len(tracker_coords) > 0:\n", "            print(f\"\\nTracker coordinate bounds:\")\n", "            print(f\"  X: {tracker_coords['x_coord'].min():.2f} to {tracker_coords['x_coord'].max():.2f}\")\n", "            print(f\"  Y: {tracker_coords['y_coord'].min():.2f} to {tracker_coords['y_coord'].max():.2f}\")\n", "            print(f\"  Z: {tracker_coords['z_coord'].min():.2f} to {tracker_coords['z_coord'].max():.2f}\")\n", "\n", "else:\n", "    print(\"No entities extracted - cannot perform analysis\")\n", "    entities_df = pd.DataFrame()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Export Optimized Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export results in pile area assignment compatible format\n", "if not entities_df.empty:\n", "    print(\"\\n=== EXPORTING RESULTS ===\")\n", "    \n", "    # Main entities file (compatible with pile area assignment)\n", "    main_output_file = run_output_dir / f\"cad_extraction_pile_area_optimized_{timestamp}.csv\"\n", "    entities_df.to_csv(main_output_file, index=False)\n", "    print(f\"Main entities file: {main_output_file.name}\")\n", "    \n", "    # Tracker-specific file for pile area assignment\n", "    if len(tracker_entities) > 0:\n", "        tracker_output_file = run_output_dir / f\"tracker_entities_{timestamp}.csv\"\n", "        tracker_entities.to_csv(tracker_output_file, index=False)\n", "        print(f\"Tracker entities file: {tracker_output_file.name}\")\n", "        \n", "        # Create pile area assignment compatible format\n", "        pile_area_format = tracker_entities[[\n", "            'entity_id', 'entity_type', 'layer_name', 'x_coord', 'y_coord', 'z_coord',\n", "            'tracker_type', 'expected_piles', 'data_quality', 'extraction_timestamp'\n", "        ]].copy()\n", "        \n", "        pile_area_output_file = run_output_dir / f\"pile_area_assignment_ready_{timestamp}.csv\"\n", "        pile_area_format.to_csv(pile_area_output_file, index=False)\n", "        print(f\"Pile area assignment ready file: {pile_area_output_file.name}\")\n", "    \n", "    # Summary report\n", "    summary_report = {\n", "        'extraction_metadata': {\n", "            'timestamp': timestamp,\n", "            'project_type': project_type,\n", "            'site_name': site_name,\n", "            'coordinate_system': coordinate_system,\n", "            'source_file': main_dxf_file.name\n", "        },\n", "        'processing_statistics': dict(processing_stats),\n", "        'validation_results': {\n", "            'expected_quantities': EXPECTED_QUANTITIES,\n", "            'extracted_tracker_count': len(tracker_entities) if 'tracker_entities' in locals() else 0,\n", "            'tracker_count_match_pct': tracker_count_match if 'tracker_count_match' in locals() else 0,\n", "            'pile_count_match_pct': pile_count_match if 'pile_count_match' in locals() else 0\n", "        },\n", "        'data_quality': {\n", "            'entities_with_coordinates': len(entities_with_coords) if 'entities_with_coords' in locals() else 0,\n", "            'coordinate_completeness_pct': (len(entities_with_coords)/len(entities_df))*100 if 'entities_with_coords' in locals() and len(entities_df) > 0 else 0,\n", "            'tracker_data_quality_good': quality_good if 'quality_good' in locals() else 0,\n", "            'tracker_data_quality_issues': quality_issues if 'quality_issues' in locals() else 0\n", "        },\n", "        'output_files': {\n", "            'main_entities': str(main_output_file),\n", "            'tracker_entities': str(tracker_output_file) if 'tracker_output_file' in locals() else None,\n", "            'pile_area_ready': str(pile_area_output_file) if 'pile_area_output_file' in locals() else None\n", "        }\n", "    }\n", "    \n", "    summary_file = run_output_dir / f\"extraction_summary_{timestamp}.json\"\n", "    with open(summary_file, 'w') as f:\n", "        json.dump(summary_report, f, indent=2, default=str)\n", "    print(f\"Summary report: {summary_file.name}\")\n", "    \n", "    print(f\"\\nAll files saved to: {run_output_dir}\")\n", "    \n", "    # Instructions for pile area assignment\n", "    print(f\"\\n=== PILE AREA ASSIGNMENT INTEGRATION ===\")\n", "    print(f\"To use with pile area assignment system:\")\n", "    print(f\"1. Update pile_area_assignment.py with path: {main_output_file}\")\n", "    print(f\"2. Or use the optimized file: {pile_area_output_file if 'pile_area_output_file' in locals() else 'N/A'}\")\n", "    print(f\"3. Run pile detection notebooks with area assignment enabled\")\n", "    \n", "    if 'tracker_count_match' in locals() and tracker_count_match > 95:\n", "        print(f\"\\n✅ Tracker extraction quality: EXCELLENT ({tracker_count_match:.1f}% match)\")\n", "    elif 'tracker_count_match' in locals() and tracker_count_match > 80:\n", "        print(f\"\\n⚠️  Tracker extraction quality: GOOD ({tracker_count_match:.1f}% match)\")\n", "    else:\n", "        print(f\"\\n❌ Tracker extraction quality: NEEDS REVIEW ({tracker_count_match:.1f}% match)\")\n", "\n", "else:\n", "    print(\"No entities to export\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This enhanced CAD extraction notebook provides:\n", "\n", "1. **Optimized tracker detection** with specific classification for all 5 tracker types\n", "2. **Enhanced data validation** with quality checks for pile area assignment compatibility\n", "3. **Comprehensive validation** against expected project quantities\n", "4. **Multiple output formats** including pile area assignment ready files\n", "5. **Detailed quality metrics** for troubleshooting and optimization\n", "\n", "The extracted data is fully compatible with the pile area assignment system and provides the spatial context needed for proper pile-to-tracker assignment validation."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}