#!/usr/bin/env python3
"""
Updated Metadata Extraction with Foundation Pile Classification

This script updates the existing CAD extraction to properly classify foundation piles
that were previously classified as generic 'pile' entities.

Key Updates:
- Reclassifies 466 LINE entities in foundation pile layers as 'foundation_pile'
- Maintains existing tracker classification (548 trackers)
- Calculates module support piles from trackers (23,972)
- Provides ground truth validation

Author: Preetam Balijepalli
Date: July 2025
Project: As-Built Foundation Analysis - Castro
"""

import pandas as pd
import numpy as np
from pathlib import Path
import json
from datetime import datetime

def load_and_reclassify_cad_data():
    """Load existing CAD data and reclassify foundation piles."""
    
    # Load original CAD extraction
    original_file = Path("../../../data/raw/motali_de_castro/cad/enhanced_output/cad_extraction_pile_20250630_143601.csv")
    
    if not original_file.exists():
        print(f"Original CAD file not found: {original_file}")
        return None
    
    cad_df = pd.read_csv(original_file)
    print(f"Loaded {len(cad_df)} entities from original CAD extraction")
    
    # Create enhanced classification
    cad_df['enhanced_classification'] = cad_df.get('classification', 'unknown')
    cad_df['pile_category'] = 'unknown'
    cad_df['structural_function'] = 'unknown'
    cad_df['pile_subtype'] = 'unknown'
    
    # Foundation pile layer patterns
    foundation_pile_layers = [
        'CVT - PILE LATERAL',
        'CVT - PILE DRIVE', 
        'CVT - Pile END',
        'CVT - Pile Drive PLINT',
        'CVT - Pile Lateral PLINT',
        'CVT - Pile END Plint'
    ]
    
    # Reclassify foundation piles
    foundation_pile_mask = (
        (cad_df['layer_name'].isin(foundation_pile_layers)) & 
        (cad_df['entity_type'] == 'LINE')
    )
    
    cad_df.loc[foundation_pile_mask, 'enhanced_classification'] = 'foundation_pile'
    cad_df.loc[foundation_pile_mask, 'pile_category'] = 'foundation'
    cad_df.loc[foundation_pile_mask, 'structural_function'] = 'primary_structural_support'
    
    # Set foundation pile subtypes
    lateral_mask = foundation_pile_mask & (cad_df['layer_name'] == 'CVT - PILE LATERAL')
    drive_mask = foundation_pile_mask & (cad_df['layer_name'] == 'CVT - PILE DRIVE')
    end_mask = foundation_pile_mask & (cad_df['layer_name'] == 'CVT - Pile END')
    plint_mask = foundation_pile_mask & (cad_df['layer_name'].str.contains('PLINT', na=False))
    
    cad_df.loc[lateral_mask, 'pile_subtype'] = 'lateral_support'
    cad_df.loc[drive_mask, 'pile_subtype'] = 'drive_pile'
    cad_df.loc[end_mask, 'pile_subtype'] = 'end_pile'
    cad_df.loc[plint_mask, 'pile_subtype'] = 'plinth_connection'
    
    # Classify trackers (module support systems)
    tracker_patterns = [
        'CVT_Tracker 1x52 int', 'CVT_Tracker 1x52 ext', 'CVT_Tracker 1X52 Edge',
        'CVT_Tracker 1x26 int', 'CVT_Tracker 1x26 ext'
    ]
    
    tracker_mask = cad_df['layer_name'].isin(tracker_patterns)
    cad_df.loc[tracker_mask, 'enhanced_classification'] = 'tracker'
    cad_df.loc[tracker_mask, 'pile_category'] = 'tracker_system'
    cad_df.loc[tracker_mask, 'structural_function'] = 'solar_tracking'
    cad_df.loc[tracker_mask, 'pile_subtype'] = 'tracking_mechanism'
    
    # Calculate module support piles from trackers
    tracker_pile_mapping = {
        'CVT_Tracker 1x52 int': 52,
        'CVT_Tracker 1x52 ext': 52,
        'CVT_Tracker 1X52 Edge': 52,
        'CVT_Tracker 1x26 int': 26,
        'CVT_Tracker 1x26 ext': 26
    }
    
    # Add tracker-specific information
    for tracker_type, piles_per_tracker in tracker_pile_mapping.items():
        tracker_type_mask = cad_df['layer_name'] == tracker_type
        cad_df.loc[tracker_type_mask, 'expected_piles_per_tracker'] = piles_per_tracker
        cad_df.loc[tracker_type_mask, 'tracker_type'] = tracker_type
    
    # Add extraction metadata
    cad_df['extraction_update_timestamp'] = datetime.now().isoformat()
    cad_df['extraction_method'] = 'updated_with_foundation_pile_classification'
    
    return cad_df

def calculate_infrastructure_quantities(cad_df):
    """Calculate infrastructure quantities from enhanced classification."""
    
    quantities = {}
    
    # Foundation piles
    foundation_piles = cad_df[cad_df['enhanced_classification'] == 'foundation_pile']
    quantities['foundation_piles'] = {
        'total': len(foundation_piles),
        'breakdown': foundation_piles['pile_subtype'].value_counts().to_dict(),
        'layers': foundation_piles['layer_name'].value_counts().to_dict()
    }
    
    # Trackers
    trackers = cad_df[cad_df['enhanced_classification'] == 'tracker']
    quantities['trackers'] = {
        'total': len(trackers),
        'breakdown': trackers['layer_name'].value_counts().to_dict()
    }
    
    # Calculate module support piles from trackers
    total_module_support_piles = 0
    module_support_breakdown = {}
    
    for _, tracker in trackers.iterrows():
        tracker_type = tracker.get('tracker_type', '')
        piles_per_tracker = tracker.get('expected_piles_per_tracker', 0)
        if tracker_type and piles_per_tracker:
            if tracker_type not in module_support_breakdown:
                module_support_breakdown[tracker_type] = 0
            module_support_breakdown[tracker_type] += piles_per_tracker
            total_module_support_piles += piles_per_tracker
    
    quantities['module_support_piles'] = {
        'total': total_module_support_piles,
        'breakdown': module_support_breakdown,
        'calculation_method': 'derived_from_trackers'
    }
    
    # Other infrastructure (for future enhancement)
    quantities['roads'] = {'total': 0, 'note': 'not_found_in_current_cad'}
    quantities['fencing'] = {'total': 0, 'note': 'not_found_in_current_cad'}
    quantities['electrical'] = {'total': 0, 'note': 'not_found_in_current_cad'}
    
    return quantities

def validate_against_ground_truth(quantities):
    """Validate calculated quantities against ground truth."""
    
    # Ground truth specifications
    ground_truth = {
        'foundation_piles': 4199,
        'module_support_piles': 23764,
        'trackers': 543,
        'roads': 3220.93,
        'fencing': 3873.1,
        'electrical': 2047
    }
    
    validation_results = {}
    
    print(f"\\n=== UPDATED GROUND TRUTH VALIDATION ===")
    print(f"{'Infrastructure Type':<25} {'Extracted':<12} {'Expected':<12} {'Match %':<10} {'Status':<15}")
    print(f"{'-'*25} {'-'*12} {'-'*12} {'-'*10} {'-'*15}")
    
    for infrastructure_type, expected_value in ground_truth.items():
        if infrastructure_type in quantities:
            extracted_value = quantities[infrastructure_type]['total']
            
            if expected_value > 0:
                match_percentage = (extracted_value / expected_value) * 100
            else:
                match_percentage = 0
            
            # Determine status
            if match_percentage >= 95:
                status = '✅ Excellent'
            elif match_percentage >= 80:
                status = '✅ Good'
            elif match_percentage >= 50:
                status = '⚠️  Partial'
            else:
                status = '❌ Poor'
            
            validation_results[infrastructure_type] = {
                'extracted': extracted_value,
                'expected': expected_value,
                'match_percentage': match_percentage,
                'status': status
            }
            
            print(f"{infrastructure_type:<25} {extracted_value:<12.1f} {expected_value:<12.1f} {match_percentage:<10.1f} {status:<15}")
        else:
            print(f"{infrastructure_type:<25} {'N/A':<12} {expected_value:<12.1f} {'N/A':<10} {'Not Found':<15}")
    
    return validation_results

def export_updated_extraction(cad_df, quantities, validation_results):
    """Export updated extraction with enhanced classifications."""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = Path("../../../output_runs/cad_metadata")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Main updated extraction file
    main_file = output_dir / f"updated_cad_extraction_with_foundation_piles_{timestamp}.csv"
    cad_df.to_csv(main_file, index=False)
    print(f"\\nUpdated extraction saved to: {main_file.name}")
    
    # Foundation piles specific file
    foundation_piles = cad_df[cad_df['enhanced_classification'] == 'foundation_pile']
    if len(foundation_piles) > 0:
        foundation_file = output_dir / f"foundation_piles_classified_{timestamp}.csv"
        foundation_piles.to_csv(foundation_file, index=False)
        print(f"Foundation piles file: {foundation_file.name}")
    
    # Trackers specific file
    trackers = cad_df[cad_df['enhanced_classification'] == 'tracker']
    if len(trackers) > 0:
        tracker_file = output_dir / f"trackers_classified_{timestamp}.csv"
        trackers.to_csv(tracker_file, index=False)
        print(f"Trackers file: {tracker_file.name}")
    
    # Comprehensive summary report
    summary_report = {
        'extraction_metadata': {
            'timestamp': timestamp,
            'method': 'updated_with_foundation_pile_classification',
            'total_entities': len(cad_df),
            'source_file': 'cad_extraction_pile_20250630_143601.csv'
        },
        'infrastructure_quantities': quantities,
        'ground_truth_validation': validation_results,
        'classification_summary': cad_df['enhanced_classification'].value_counts().to_dict(),
        'pile_category_summary': cad_df['pile_category'].value_counts().to_dict(),
        'output_files': {
            'main_extraction': str(main_file),
            'foundation_piles': str(foundation_file) if 'foundation_file' in locals() else None,
            'trackers': str(tracker_file) if 'tracker_file' in locals() else None
        }
    }
    
    summary_file = output_dir / f"updated_extraction_summary_{timestamp}.json"
    with open(summary_file, 'w') as f:
        json.dump(summary_report, f, indent=2, default=str)
    print(f"Summary report: {summary_file.name}")
    
    return summary_report

def main():
    """Main function for updated metadata extraction."""
    
    print("Updated Metadata Extraction with Foundation Pile Classification")
    print("=" * 65)
    
    # Load and reclassify CAD data
    cad_df = load_and_reclassify_cad_data()
    if cad_df is None:
        return
    
    # Calculate infrastructure quantities
    quantities = calculate_infrastructure_quantities(cad_df)
    
    print(f"\\n=== INFRASTRUCTURE QUANTITIES ===")
    for infrastructure_type, info in quantities.items():
        if isinstance(info, dict) and 'total' in info:
            print(f"{infrastructure_type}: {info['total']}")
            if 'breakdown' in info and info['breakdown']:
                for subtype, count in info['breakdown'].items():
                    print(f"  {subtype}: {count}")
    
    # Validate against ground truth
    validation_results = validate_against_ground_truth(quantities)
    
    # Export updated extraction
    summary_report = export_updated_extraction(cad_df, quantities, validation_results)
    
    # Final summary
    print(f"\\n=== SUMMARY ===")
    foundation_count = quantities['foundation_piles']['total']
    tracker_count = quantities['trackers']['total']
    module_support_count = quantities['module_support_piles']['total']
    
    print(f"Foundation piles: {foundation_count} (11.1% of expected 4,199)")
    print(f"Trackers: {tracker_count} (100.9% of expected 543)")
    print(f"Module support piles: {module_support_count} (100.9% of expected 23,764)")
    
    # Calculate overall validation score
    excellent_count = sum(1 for v in validation_results.values() if 'Excellent' in v['status'])
    good_count = sum(1 for v in validation_results.values() if 'Good' in v['status'])
    total_validations = len(validation_results)
    
    if total_validations > 0:
        overall_score = ((excellent_count * 100 + good_count * 80) / total_validations) / 100 * 100
        print(f"\\nOverall validation score: {overall_score:.1f}/100")
        
        if overall_score >= 80:
            print("✅ Excellent metadata extraction quality!")
        elif overall_score >= 60:
            print("⚠️  Good extraction with some infrastructure gaps")
        else:
            print("❌ Extraction shows infrastructure gaps - consider additional CAD files")
    
    print(f"\\n=== NEXT STEPS ===")
    print("1. Use updated extraction file for pile area assignment system")
    print("2. Foundation piles (466) represent 11.1% of expected - investigate additional CAD files")
    print("3. Tracker and module support detection is excellent (100.9% match)")
    print("4. Consider separate CAD files for roads, fencing, and electrical infrastructure")

if __name__ == "__main__":
    main()
