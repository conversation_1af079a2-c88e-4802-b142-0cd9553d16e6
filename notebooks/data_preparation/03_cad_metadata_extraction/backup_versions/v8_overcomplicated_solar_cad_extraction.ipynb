{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Solar Project CAD Extraction for Alignment\n", "\n", "Focused extraction of solar project infrastructure from CAD files for spatial alignment workflows.\n", "\n", "**Purpose**: Extract tracker positions, module layouts, and infrastructure for point cloud alignment  \n", "**Input**: DXF/DWG files containing solar project layouts  \n", "**Output**: Structured coordinates and metadata for alignment workflows  \n", "\n", "## Multi-Site Compatible:\n", "- **Generic classification rules** that adapt to different solar project naming conventions\n", "- **Auto-discovery** of CAD files and layer structures\n", "- **Site-specific adaptation** based on discovered layers\n", "- **Executes on specific files**: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf/dwg\n", "\n", "## Based on Actual CAD Analysis:\n", "From executing CAD extraction on Castro project, we discovered:\n", "- **Tracker Systems**: INSERT entities (CVT_Tracker layers) with precise coordinates\n", "- **Module Layouts**: LWPOLYLINE entities (PVcase layers) defining solar panel areas\n", "- **DC Cable Routes**: Electrical infrastructure entities\n", "- **Site Infrastructure**: Roads, buildings, boundaries\n", "- **No Foundation Piles**: Solar CAD contains tracker/module layouts, not structural foundations\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Papermill parameters\n", "project_type = \"solar_project\"  # Generic for any solar project\n", "site_name = \"cad_extraction\"\n", "cad_data_path = \"../../../data/raw\"  # Auto-discover CAD files\n", "output_dir = \"../../../output_runs/cad_metadata\"\n", "coordinate_system = \"auto\"  # Auto-detect or specify (e.g., EPSG:32633)\n", "focus_on_alignment = True  # Extract only alignment-relevant data\n", "target_files = [\"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\", \"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg\"]  # Specific files to process"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Solar CAD Extraction for Alignment - Solar_Project\n", "Timestamp: 2025-07-01 14:09:36\n", "Target files: ['GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf', 'GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg']\n", "============================================================\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "import ezdxf\n", "import json\n", "from datetime import datetime\n", "from collections import defaultdict\n", "import logging\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "print(f\"Solar CAD Extraction for Alignment - {project_type.title()}\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(f\"Target files: {target_files}\")\n", "print(\"=\" * 60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. CAD File Discovery and Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check if ogrinfo is available\n", "try:\n", "    !which ogrinfo\n", "    ogrinfo_available = True\n", "    print(\"✅ ogrinfo is available for advanced CAD analysis\")\n", "except:\n", "    ogrinfo_available = False\n", "    print(\"⚠️ ogrinfo not found. Install GDAL tools for enhanced analysis:\")\n", "    print(\"  macOS: brew install gdal\")\n", "    print(\"  Ubuntu: sudo apt-get install gdal-bin\")\n", "    print(\"  Will use basic analysis only\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:__main__:Could not analyze ../../../data/raw/motali_de_castro/cad/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg: File '../../../data/raw/motali_de_castro/cad/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg' is not a DXF file.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== CAD FILE DISCOVERY ===\n", "Found 4 target files out of 2 requested\n", "Found 4 CAD files:\n", "  1. GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg (4.6 MB)\n", "  2. GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf (15.5 MB)\n", "  3. GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf (15.5 MB)\n", "  4. GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg (4.6 MB)\n", "\n", "=== SOLAR CONTENT ANALYSIS ===\n", "\n", "Analyzing: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg\n", "\n", "Analyzing: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\n", "  Total entities: 1065\n", "  Solar-relevant entities: 589\n", "  Solar layers found: 15\n", "  Top solar layers: {'CVT_Tracker 1x52 int': 248, 'CVT_Tracker 1x26 int': 106, 'CVT_Tracker 1X52 Edge': 97, 'CVT_Tracker 1x26 ext': 68, 'CVT_Tracker 1x52 ext': 29}\n", "\n", "Analyzing: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:__main__:Could not analyze ../../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg: File '../../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg' is not a DXF file.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Total entities: 1065\n", "  Solar-relevant entities: 589\n", "  Solar layers found: 15\n", "  Top solar layers: {'CVT_Tracker 1x52 int': 248, 'CVT_Tracker 1x26 int': 106, 'CVT_Tracker 1X52 Edge': 97, 'CVT_Tracker 1x26 ext': 68, 'CVT_Tracker 1x52 ext': 29}\n", "\n", "Analyzing: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg\n"]}], "source": ["def discover_cad_files(base_path, target_files=None):\n", "    \"\"\"Discover DXF and DWG files, prioritizing target files.\"\"\"\n", "    base_path = Path(base_path)\n", "    cad_files = []\n", "    \n", "    # Search for CAD files\n", "    for pattern in ['**/*.dxf', '**/*.DXF', '**/*.dwg', '**/*.DWG']:\n", "        cad_files.extend(base_path.glob(pattern))\n", "    \n", "    # Filter for target files if specified\n", "    if target_files:\n", "        target_files_found = []\n", "        for target in target_files:\n", "            matching = [f for f in cad_files if f.name == target]\n", "            target_files_found.extend(matching)\n", "        \n", "        if target_files_found:\n", "            print(f\"Found {len(target_files_found)} target files out of {len(target_files)} requested\")\n", "            return sorted(target_files_found)\n", "        else:\n", "            print(f\"Target files not found, using all discovered files\")\n", "    \n", "    return sorted(cad_files)\n", "\n", "def analyze_solar_layers(file_path):\n", "    \"\"\"Analyze CAD file for solar-specific layers.\"\"\"\n", "    try:\n", "        doc = ezdxf.readfile(file_path)\n", "        msp = doc.modelspace()\n", "        \n", "        # Count solar-relevant entities\n", "        solar_layers = defaultdict(int)\n", "        entity_types = defaultdict(int)\n", "        all_layers = defaultdict(int)\n", "        \n", "        for entity in msp:\n", "            layer = getattr(entity.dxf, 'layer', 'unknown')\n", "            entity_type = entity.dxftype()\n", "            \n", "            all_layers[layer] += 1\n", "            entity_types[entity_type] += 1\n", "            \n", "            # Focus on solar-relevant layers (broader patterns)\n", "            layer_upper = layer.upper()\n", "            if any(keyword in layer_upper for keyword in ['TRACKER', 'CVT', 'TRJ', 'PVCASE', 'PV', 'SOLAR', 'MODULE', 'PANEL']):\n", "                solar_layers[layer] += 1\n", "        \n", "        return {\n", "            'solar_layers': dict(solar_layers),\n", "            'all_layers': dict(all_layers),\n", "            'entity_types': dict(entity_types),\n", "            'total_entities': sum(entity_types.values()),\n", "            'solar_entities': sum(solar_layers.values())\n", "        }\n", "    except Exception as e:\n", "        logger.warning(f\"Could not analyze {file_path}: {e}\")\n", "        return None\n", "\n", "# Discover and analyze CAD files\n", "print(\"=== CAD FILE DISCOVERY ===\")\n", "cad_files = discover_cad_files(cad_data_path, target_files)\n", "\n", "if not cad_files:\n", "    print(f\"No CAD files found in {cad_data_path}\")\n", "    print(f\"Looking for: {target_files}\")\n", "else:\n", "    print(f\"Found {len(cad_files)} CAD files:\")\n", "    for i, file_path in enumerate(cad_files, 1):\n", "        print(f\"  {i}. {file_path.name} ({file_path.stat().st_size / 1024 / 1024:.1f} MB)\")\n", "    \n", "    # Analyze solar content\n", "    print(f\"\\n=== SOLAR CONTENT ANALYSIS ===\")\n", "    file_analyses = {}\n", "    for file_path in cad_files:\n", "        print(f\"\\nAnalyzing: {file_path.name}\")\n", "        analysis = analyze_solar_layers(file_path)\n", "        if analysis:\n", "            file_analyses[file_path.name] = analysis\n", "            print(f\"  Total entities: {analysis['total_entities']}\")\n", "            print(f\"  Solar-relevant entities: {analysis['solar_entities']}\")\n", "            print(f\"  Solar layers found: {len(analysis['solar_layers'])}\")\n", "            \n", "            # Show top solar layers\n", "            if analysis['solar_layers']:\n", "                top_solar = sorted(analysis['solar_layers'].items(), key=lambda x: x[1], reverse=True)[:5]\n", "                print(f\"  Top solar layers: {dict(top_solar)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Advanced CAD Analysis with ogrinfo"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def parse_ogrinfo_output(output_text):\n", "    \"\"\"Parse ogrinfo output to extract useful information.\"\"\"\n", "    import re\n", "    \n", "    info = {\n", "        'layers': [],\n", "        'extent': None,\n", "        'feature_counts': {},\n", "        'coordinate_system': None\n", "    }\n", "    \n", "    lines = output_text.split('\\n')\n", "    current_layer = None\n", "    \n", "    for line in lines:\n", "        line = line.strip()\n", "        \n", "        # Extract layer information\n", "        if line.startswith('Layer name:'):\n", "            current_layer = line.split(':', 1)[1].strip()\n", "            info['layers'].append(current_layer)\n", "        \n", "        # Extract feature count\n", "        elif line.startswith('Feature Count:') and current_layer:\n", "            count_str = line.split(':', 1)[1].strip()\n", "            try:\n", "                info['feature_counts'][current_layer] = int(count_str)\n", "            except ValueError:\n", "                pass\n", "        \n", "        # Extract extent\n", "        elif line.startswith('Extent:'):\n", "            extent_pattern = r'Extent: \\(([0-9.-]+), ([0-9.-]+)\\) - \\(([0-9.-]+), ([0-9.-]+)\\)'\n", "            match = re.search(extent_pattern, line)\n", "            if match:\n", "                x_min, y_min, x_max, y_max = map(float, match.groups())\n", "                info['extent'] = {\n", "                    'x_min': x_min, 'y_min': y_min,\n", "                    'x_max': x_max, 'y_max': y_max,\n", "                    'x_range': x_max - x_min,\n", "                    'y_range': y_max - y_min\n", "                }\n", "        \n", "        # Extract coordinate system info\n", "        elif 'PROJCS' in line or 'GEOGCS' in line:\n", "            info['coordinate_system'] = line\n", "    \n", "    return info\n", "\n", "# Perform ogrinfo analysis on DXF files\n", "ogrinfo_results = {}\n", "\n", "if ogrinfo_available:\n", "    print(\"\\n=== OGRINFO ADVANCED ANALYSIS ===\")\n", "    \n", "    for file_path in cad_files:\n", "        if file_path.suffix.lower() == '.dxf':  # ogrinfo works best with DXF\n", "            print(f\"\\n📊 Analyzing {file_path.name} with ogrinfo:\")\n", "            \n", "            # Run ogrinfo command\n", "            result = !ogrinfo -so \"{file_path}\"\n", "            \n", "            if result:\n", "                output_text = '\\n'.join(result)\n", "                parsed_info = parse_ogrinfo_output(output_text)\n", "                ogrinfo_results[file_path.name] = parsed_info\n", "                \n", "                # Display key information\n", "                print(f\"  Layers found: {len(parsed_info['layers'])}\")\n", "                if parsed_info['extent']:\n", "                    ext = parsed_info['extent']\n", "                    print(f\"  Spatial extent:\")\n", "                    print(f\"    X: {ext['x_min']:.1f} to {ext['x_max']:.1f} (range: {ext['x_range']:.1f})\")\n", "                    print(f\"    Y: {ext['y_min']:.1f} to {ext['y_max']:.1f} (range: {ext['y_range']:.1f})\")\n", "                \n", "                if parsed_info['feature_counts']:\n", "                    print(f\"  Feature counts by layer:\")\n", "                    for layer, count in sorted(parsed_info['feature_counts'].items(), key=lambda x: x[1], reverse=True)[:5]:\n", "                        print(f\"    {layer}: {count} features\")\n", "                \n", "                if parsed_info['coordinate_system']:\n", "                    print(f\"  Coordinate system detected: {parsed_info['coordinate_system'][:100]}...\")\n", "            else:\n", "                print(f\"  ❌ ogrinfo failed for {file_path.name}\")\n", "        else:\n", "            print(f\"  ⏭️ Skipping {file_path.name} (DWG files need conversion to DXF for ogrinfo)\")\n", "else:\n", "    print(\"\\n⚠️ Skipping ogrinfo analysis - tool not available\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Automated Site Configuration Generation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def detect_coordinate_system_type(x_coords, y_coords):\n", "    \"\"\"Detect likely coordinate system type from coordinate ranges.\"\"\"\n", "    x_min, x_max = np.min(x_coords), np.max(x_coords)\n", "    y_min, y_max = np.min(y_coords), np.max(y_coords)\n", "    \n", "    # Remove obvious invalid coordinates for analysis\n", "    valid_mask = ~((x_coords == 0) & (y_coords == 0))\n", "    if np.sum(valid_mask) > 0:\n", "        x_clean = x_coords[valid_mask]\n", "        y_clean = y_coords[valid_mask]\n", "        x_min, x_max = np.min(x_clean), np.max(x_clean)\n", "        y_min, y_max = np.min(y_clean), np.max(y_clean)\n", "    \n", "    # UTM coordinates (6-7 digits, specific ranges)\n", "    if (100000 <= x_min < 1000000 and 1000000 <= y_min < 10000000):\n", "        return \"UTM\"\n", "    \n", "    # Geographic coordinates (lat/lon)\n", "    elif (abs(x_min) <= 180 and abs(x_max) <= 180 and abs(y_min) <= 90 and abs(y_max) <= 90):\n", "        return \"Geographic\"\n", "    \n", "    # State Plane or other projected (large numbers)\n", "    elif x_min > 1000000 or y_min > 1000000:\n", "        return \"State_Plane_or_Projected\"\n", "    \n", "    # Local/Project coordinates\n", "    else:\n", "        return \"Local_Project\"\n", "\n", "def generate_site_configuration(file_analyses, ogrinfo_results, site_name):\n", "    \"\"\"Generate comprehensive site configuration from all analyses.\"\"\"\n", "    print(f\"\\n=== GENERATING SITE CONFIGURATION ===\")\n", "    \n", "    # Combine information from multiple sources\n", "    all_extents = []\n", "    coordinate_systems = []\n", "    total_entities = 0\n", "    \n", "    # From ogrinfo results\n", "    for filename, ogrinfo in ogrinfo_results.items():\n", "        if ogrinfo.get('extent'):\n", "            all_extents.append(ogrinfo['extent'])\n", "        if ogrinfo.get('coordinate_system'):\n", "            if 'UTM' in ogrinfo['coordinate_system']:\n", "                coordinate_systems.append('UTM')\n", "            elif 'GEOGCS' in ogrinfo['coordinate_system']:\n", "                coordinate_systems.append('Geographic')\n", "    \n", "    # From file analyses\n", "    for filename, analysis in file_analyses.items():\n", "        if analysis:\n", "            total_entities += analysis.get('total_entities', 0)\n", "    \n", "    # Determine overall bounds\n", "    if all_extents:\n", "        overall_extent = {\n", "            'x_min': min(ext['x_min'] for ext in all_extents),\n", "            'x_max': max(ext['x_max'] for ext in all_extents),\n", "            'y_min': min(ext['y_min'] for ext in all_extents),\n", "            'y_max': max(ext['y_max'] for ext in all_extents)\n", "        }\n", "        \n", "        # Add safety buffer (20% of range)\n", "        x_range = overall_extent['x_max'] - overall_extent['x_min']\n", "        y_range = overall_extent['y_max'] - overall_extent['y_min']\n", "        \n", "        validation_bounds = {\n", "            'x_min': overall_extent['x_min'] - x_range * 0.2,\n", "            'x_max': overall_extent['x_max'] + x_range * 0.2,\n", "            'y_min': overall_extent['y_min'] - y_range * 0.2,\n", "            'y_max': overall_extent['y_max'] + y_range * 0.2\n", "        }\n", "    else:\n", "        print(\"⚠️ No spatial extent information available\")\n", "        overall_extent = None\n", "        validation_bounds = None\n", "    \n", "    # Determine coordinate system\n", "    if coordinate_systems:\n", "        coord_system = max(set(coordinate_systems), key=coordinate_systems.count)\n", "    else:\n", "        coord_system = \"Unknown\"\n", "    \n", "    # Generate configuration\n", "    config = {\n", "        'site_name': site_name,\n", "        'generation_timestamp': datetime.now().isoformat(),\n", "        'analysis_source': {\n", "            'ogrinfo_available': len(ogrinfo_results) > 0,\n", "            'files_analyzed': list(file_analyses.keys()),\n", "            'total_entities_discovered': total_entities\n", "        },\n", "        'coordinate_system': {\n", "            'detected_type': coord_system,\n", "            'confidence': 'high' if len(coordinate_systems) > 0 else 'low',\n", "            'epsg_suggestion': 'EPSG:32633' if coord_system == 'UTM' else 'auto'\n", "        },\n", "        'spatial_bounds': {\n", "            'raw_extent': overall_extent,\n", "            'validation_bounds': validation_bounds,\n", "            'buffer_percentage': 20.0\n", "        } if overall_extent else None,\n", "        'validation_rules': {\n", "            'skip_zero_coordinates': True,\n", "            'use_bounds_validation': validation_bounds is not None,\n", "            'use_statistical_outlier_detection': True,\n", "            'outlier_threshold_iqr': 2.5,\n", "            'minimum_valid_coordinates_required': 5\n", "        },\n", "        'ogrinfo_summary': ogrinfo_results,\n", "        'file_analysis_summary': file_analyses\n", "    }\n", "    \n", "    # Display configuration summary\n", "    print(f\"✅ Configuration generated for site: {site_name}\")\n", "    print(f\"📍 Coordinate system: {coord_system} (confidence: {config['coordinate_system']['confidence']})\")\n", "    \n", "    if validation_bounds:\n", "        print(f\"📐 Validation bounds:\")\n", "        print(f\"   X: {validation_bounds['x_min']:.1f} to {validation_bounds['x_max']:.1f}\")\n", "        print(f\"   Y: {validation_bounds['y_min']:.1f} to {validation_bounds['y_max']:.1f}\")\n", "    else:\n", "        print(f\"⚠️ No spatial bounds available - will use statistical validation only\")\n", "    \n", "    print(f\"📊 Total entities discovered: {total_entities}\")\n", "    \n", "    return config\n", "\n", "# Generate site configuration\n", "site_config = generate_site_configuration(file_analyses, ogrinfo_results, site_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Adaptive Coordinate Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_adaptive_validator(site_config):\n", "    \"\"\"Create coordinate validation function based on site configuration.\"\"\"\n", "    \n", "    if not site_config:\n", "        print(\"⚠️ No site configuration available - using basic validation only\")\n", "        def basic_validator(entity_data):\n", "            x, y = entity_data.get('x_coord', 0), entity_data.get('y_coord', 0)\n", "            return not (x == 0.0 and y == 0.0)  # Just skip zero coordinates\n", "        return basic_validator\n", "    \n", "    validation_rules = site_config.get('validation_rules', {})\n", "    spatial_bounds = site_config.get('spatial_bounds')\n", "    \n", "    print(f\"\\n=== CREATING ADAPTIVE VALIDATOR ===\")\n", "    print(f\"Site: {site_config.get('site_name', 'unknown')}\")\n", "    print(f\"Coordinate system: {site_config.get('coordinate_system', {}).get('detected_type', 'unknown')}\")\n", "    \n", "    # Validation components\n", "    skip_zeros = validation_rules.get('skip_zero_coordinates', True)\n", "    use_bounds = validation_rules.get('use_bounds_validation', False) and spatial_bounds\n", "    use_statistical = validation_rules.get('use_statistical_outlier_detection', True)\n", "    \n", "    print(f\"Validation methods:\")\n", "    print(f\"  ✓ Skip zero coordinates: {skip_zeros}\")\n", "    print(f\"  ✓ Bounds validation: {use_bounds}\")\n", "    print(f\"  ✓ Statistical outlier detection: {use_statistical}\")\n", "    \n", "    if use_bounds:\n", "        bounds = spatial_bounds['validation_bounds']\n", "        print(f\"  📐 Validation bounds: X({bounds['x_min']:.1f}, {bounds['x_max']:.1f}), Y({bounds['y_min']:.1f}, {bounds['y_max']:.1f})\")\n", "    \n", "    def adaptive_validator(entity_data):\n", "        x, y = entity_data.get('x_coord', 0), entity_data.get('y_coord', 0)\n", "        \n", "        # Skip zero coordinates\n", "        if skip_zeros and x == 0.0 and y == 0.0:\n", "            return False\n", "        \n", "        # Bounds validation\n", "        if use_bounds:\n", "            bounds = spatial_bounds['validation_bounds']\n", "            if not (bounds['x_min'] <= x <= bounds['x_max'] and bounds['y_min'] <= y <= bounds['y_max']):\n", "                return False\n", "        \n", "        # Additional validation can be added here (statistical outlier detection)\n", "        # For now, if it passes the above tests, it's valid\n", "        return True\n", "    \n", "    return adaptive_validator\n", "\n", "# Create validator based on site configuration\n", "coordinate_validator = create_adaptive_validator(site_config)\n", "\n", "# Test validator with sample coordinates if available\n", "if 'file_analyses' in locals() and file_analyses:\n", "    print(f\"\\n=== TESTING COORDINATE VALIDATOR ===\")\n", "    test_coords = [\n", "        {'x_coord': 0.0, 'y_coord': 0.0},  # Should be rejected\n", "        {'x_coord': 707500.0, 'y_coord': 4693000.0},  # Should be accepted (typical UTM)\n", "        {'x_coord': 999999.0, 'y_coord': 999999.0},  # May be rejected if bounds validation active\n", "    ]\n", "    \n", "    for i, test_coord in enumerate(test_coords, 1):\n", "        result = coordinate_validator(test_coord)\n", "        status = \"✅ VALID\" if result else \"❌ INVALID\"\n", "        print(f\"  Test {i}: ({test_coord['x_coord']}, {test_coord['y_coord']}) → {status}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Enhanced Solar Classification System"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== ADAPTING CLASSIFICATION TO SITE ===\n", "Total discovered layers: 43\n", "Site-specific mappings created: 16\n", "  tracker: 12 layers\n", "    - CVT -Plant\n", "    - CVT - Pile END Plint\n", "    - CVT_Tracker 1x26 ext\n", "    ... and 9 more\n", "  module: 3 layers\n", "    - PVcase Offsets\n", "    - PVcase AlignmentLine\n", "    - PVcase PV Area\n", "  infrastructure: 1 layers\n", "    - SCS_FENCE\n", "\n", "Generic Solar Classifier initialized and adapted\n", "Focus classifications: ['tracker', 'module', 'electrical', 'infrastructure']\n"]}], "source": ["class GenericSolarClassifier:\n", "    \"\"\"Generic classifier for solar infrastructure across different sites.\"\"\"\n", "    \n", "    def __init__(self):\n", "        # Generic classification rules for any solar project site\n", "        self.classification_rules = {\n", "            # Tracker systems (primary alignment references)\n", "            'tracker': {\n", "                'layer_patterns': ['TRACKER', 'CVT', 'TRJ', 'SOLAR_TRACKER'],\n", "                'entity_types': ['INSERT', 'BLOCK'],\n", "                'keywords': ['tracker', 'cvt', 'trj', '1p52', '1p26', 'solar', 'track'],\n", "                'block_patterns': ['1P', '1X', 'TRACKER', 'SOLAR'],\n", "                'alignment_value': 'high'  # Primary reference points\n", "            },\n", "            \n", "            # Module layouts (secondary alignment references)\n", "            'module': {\n", "                'layer_patterns': ['PVCASE', 'PV', 'MODULE', 'PANEL', 'SOLAR'],\n", "                'entity_types': ['LWPOLYLINE', 'POLYLINE', 'HATCH'],\n", "                'keywords': ['pvcase', 'pv', 'module', 'panel', 'solar', 'photovoltaic'],\n", "                'block_patterns': ['PV', 'MODULE', 'PANEL'],\n", "                'alignment_value': 'medium'  # Area boundaries\n", "            },\n", "            \n", "            # DC electrical (infrastructure context)\n", "            'electrical': {\n", "                'layer_patterns': ['DC', 'ELECTRICAL', 'CABLE', 'POWER', 'WIRE'],\n", "                'entity_types': ['POLYLINE', 'LWPOLYLINE', 'LINE'],\n", "                'keywords': ['dc', 'electrical', 'cable', 'power', 'wire', 'voltage'],\n", "                'block_patterns': ['ELECTRICAL', 'POWER'],\n", "                'alignment_value': 'low'  # Context only\n", "            },\n", "            \n", "            # Site infrastructure (context)\n", "            'infrastructure': {\n", "                'layer_patterns': ['ROAD', 'BUILDING', 'BOUNDARY', 'FENCE', 'ACCESS'],\n", "                'entity_types': ['P<PERSON>YLIN<PERSON>', 'LWPOLYLINE', 'HATCH', 'LINE'],\n", "                'keywords': ['road', 'building', 'boundary', 'fence', 'access', 'site'],\n", "                'block_patterns': ['BUILDING', 'STRUCTURE'],\n", "                'alignment_value': 'low'  # Context only\n", "            }\n", "        }\n", "        \n", "        # Dynamic layer discovery for site-specific adaptation\n", "        self.discovered_layers = set()\n", "        self.site_specific_rules = {}\n", "    \n", "    def adapt_to_site(self, file_analyses):\n", "        \"\"\"Adapt classification rules based on discovered layers.\"\"\"\n", "        print(\"\\n=== ADAPTING CLASSIFICATION TO SITE ===\")\n", "        \n", "        # Collect all discovered layers\n", "        for analysis in file_analyses.values():\n", "            self.discovered_layers.update(analysis.get('all_layers', {}).keys())\n", "        \n", "        print(f\"Total discovered layers: {len(self.discovered_layers)}\")\n", "        \n", "        # Auto-enhance rules based on discovered layers\n", "        for layer in self.discovered_layers:\n", "            layer_upper = layer.upper()\n", "            \n", "            # Add to appropriate categories based on patterns\n", "            for classification, rules in self.classification_rules.items():\n", "                if any(pattern in layer_upper for pattern in rules['layer_patterns']):\n", "                    if layer not in self.site_specific_rules:\n", "                        self.site_specific_rules[layer] = classification\n", "        \n", "        print(f\"Site-specific mappings created: {len(self.site_specific_rules)}\")\n", "        \n", "        # Show mappings by category\n", "        for classification in self.classification_rules.keys():\n", "            mapped_layers = [layer for layer, cls in self.site_specific_rules.items() if cls == classification]\n", "            if mapped_layers:\n", "                print(f\"  {classification}: {len(mapped_layers)} layers\")\n", "                for layer in mapped_layers[:3]:  # Show first 3\n", "                    print(f\"    - {layer}\")\n", "                if len(mapped_layers) > 3:\n", "                    print(f\"    ... and {len(mapped_layers) - 3} more\")\n", "    \n", "    def classify_entity(self, entity_data):\n", "        \"\"\"Classify entity using generic and site-specific rules.\"\"\"\n", "        layer_name = entity_data.get('layer_name', '')\n", "        entity_type = entity_data.get('entity_type', '')\n", "        block_name = str(entity_data.get('block_name', '')).lower()\n", "        \n", "        # Check site-specific rules first (exact layer match)\n", "        if layer_name in self.site_specific_rules:\n", "            return self.site_specific_rules[layer_name]\n", "        \n", "        # Fall back to generic pattern matching\n", "        layer_upper = layer_name.upper()\n", "        \n", "        for classification, rules in self.classification_rules.items():\n", "            # Check layer patterns\n", "            if any(pattern in layer_upper for pattern in rules['layer_patterns']):\n", "                if entity_type in rules['entity_types']:\n", "                    return classification\n", "            \n", "            # Check block name patterns\n", "            if entity_type in rules['entity_types']:\n", "                if any(pattern.lower() in block_name for pattern in rules['block_patterns']):\n", "                    return classification\n", "        \n", "        return 'other'\n", "    \n", "    def get_alignment_value(self, classification):\n", "        \"\"\"Get alignment value for a classification.\"\"\"\n", "        if classification in self.classification_rules:\n", "            return self.classification_rules[classification]['alignment_value']\n", "        return 'none'\n", "\n", "# Initialize classifier\n", "classifier = GenericSolarClassifier()\n", "\n", "# Adapt to discovered site layers\n", "if 'file_analyses' in locals() and file_analyses:\n", "    classifier.adapt_to_site(file_analyses)\n", "\n", "print(f\"\\nGeneric Solar Classifier initialized and adapted\")\n", "print(f\"Focus classifications: {list(classifier.classification_rules.keys())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Alignment-Focused Entity Extraction"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Alignment-focused entity extraction function defined\n"]}], "source": ["def extract_alignment_entity(entity):\n", "    \"\"\"Extract entity data focused on alignment needs.\"\"\"\n", "    try:\n", "        entity_type = entity.dxftype()\n", "        layer_name = getattr(entity.dxf, 'layer', 'unknown')\n", "        \n", "        # Base entity data\n", "        entity_data = {\n", "            'entity_id': str(entity.dxf.handle),\n", "            'entity_type': entity_type,\n", "            'layer_name': layer_name\n", "        }\n", "        \n", "        # Extract coordinates based on entity type\n", "        if hasattr(entity.dxf, 'insert'):\n", "            # INSERT entities (trackers) - PRIMARY ALIGNMENT POINTS\n", "            insert_point = entity.dxf.insert\n", "            entity_data.update({\n", "                'x_coord': insert_point.x,\n", "                'y_coord': insert_point.y,\n", "                'z_coord': insert_point.z,\n", "                'block_name': getattr(entity.dxf, 'name', ''),\n", "                'rotation': getattr(entity.dxf, 'rotation', 0.0),\n", "                'geometry_type': 'point'\n", "            })\n", "            \n", "        <PERSON><PERSON> hasattr(entity.dxf, 'start') and hasattr(entity.dxf, 'end'):\n", "            # LINE entities\n", "            start_point = entity.dxf.start\n", "            end_point = entity.dxf.end\n", "            entity_data.update({\n", "                'x_coord': (start_point.x + end_point.x) / 2,\n", "                'y_coord': (start_point.y + end_point.y) / 2,\n", "                'z_coord': (start_point.z + end_point.z) / 2,\n", "                'start_x': start_point.x,\n", "                'start_y': start_point.y,\n", "                'end_x': end_point.x,\n", "                'end_y': end_point.y,\n", "                'length': start_point.distance(end_point),\n", "                'geometry_type': 'line'\n", "            })\n", "            \n", "        elif entity_type in ['POLYLINE', 'LWPOLYLINE']:\n", "            # POLYLINE entities (module areas) - AREA BOUNDARIES\n", "            try:\n", "                # Get bounding box for polylines\n", "                bbox = entity.bbox()\n", "                if bbox:\n", "                    entity_data.update({\n", "                        'x_coord': (bbox[0].x + bbox[1].x) / 2,\n", "                        'y_coord': (bbox[0].y + bbox[1].y) / 2,\n", "                        'z_coord': (bbox[0].z + bbox[1].z) / 2,\n", "                        'x_min': bbox[0].x,\n", "                        'y_min': bbox[0].y,\n", "                        'x_max': bbox[1].x,\n", "                        'y_max': bbox[1].y,\n", "                        'geometry_type': 'area'\n", "                    })\n", "                else:\n", "                    entity_data.update({'x_coord': 0.0, 'y_coord': 0.0, 'z_coord': 0.0, 'geometry_type': 'area'})\n", "            except:\n", "                entity_data.update({'x_coord': 0.0, 'y_coord': 0.0, 'z_coord': 0.0, 'geometry_type': 'area'})\n", "                \n", "        else:\n", "            # Skip entities without useful coordinates for alignment\n", "            return None\n", "        \n", "        # Classification and alignment value\n", "        classification = classifier.classify_entity(entity_data)\n", "        entity_data['classification'] = classification\n", "        entity_data['alignment_value'] = classifier.get_alignment_value(classification)\n", "        entity_data['extraction_timestamp'] = datetime.now().isoformat()\n", "        \n", "        # Only return entities useful for alignment\n", "        if focus_on_alignment and entity_data['alignment_value'] == 'none':\n", "            return None\n", "            \n", "        return entity_data\n", "        \n", "    except Exception as e:\n", "        logger.warning(f\"Error extracting entity data: {e}\")\n", "        return None\n", "\n", "print(\"Alignment-focused entity extraction function defined\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_alignment_entity_with_validation(entity):\n", "    \"\"\"Enhanced extraction with adaptive coordinate validation.\"\"\"\n", "    # First extract entity data\n", "    entity_data = extract_alignment_entity(entity)\n", "    \n", "    if not entity_data:\n", "        return None\n", "    \n", "    # Apply coordinate validation\n", "    if not coordinate_validator(entity_data):\n", "        return None  # Filter out invalid coordinates\n", "    \n", "    # Add validation metadata\n", "    entity_data['coordinate_validated'] = True\n", "    entity_data['validation_timestamp'] = datetime.now().isoformat()\n", "    \n", "    return entity_data\n", "\n", "print(\"Enhanced entity extraction with adaptive validation ready\")\n", "print(f\"Validation method: {'Adaptive (bounds + rules)' if site_config else 'Basic (zero-coordinate filtering)'}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Process CAD Files with Enhanced Validation"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["ERROR:__main__:Error processing GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg: File '../../../data/raw/motali_de_castro/cad/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg' is not a DXF file.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== PROCESSING CAD FILES FOR ALIGNMENT DATA ===\n", "\n", "Processing: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg\n", "\n", "Processing: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\n", "  Total entities: 1065\n", "  Extracted for alignment: 591\n", "  Skipped (not alignment-relevant): 474\n", "  Classifications: module(30), infrastructure(6), tracker(555)\n", "\n", "Processing: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:__main__:Error processing GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg: File '../../../data/raw/motali_de_castro/cad/OneDrive_2025-02-19/03. TRACKER/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg' is not a DXF file.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Total entities: 1065\n", "  Extracted for alignment: 591\n", "  Skipped (not alignment-relevant): 474\n", "  Classifications: module(30), infrastructure(6), tracker(555)\n", "\n", "Processing: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg\n", "\n", "=== OVERALL PROCESSING SUMMARY ===\n", "Files processed: 1\n", "Total entities: 2130\n", "Extracted for alignment: 1182\n", "Extraction rate: 55.5%\n", "\n", "Alignment DataFrame: 1182 entities\n", "\n", "Alignment Value Distribution:\n", "  high: 1110 entities\n", "  medium: 60 entities\n", "  low: 12 entities\n", "\n", "Classification Distribution:\n", "  tracker: 1110 entities\n", "  module: 60 entities\n", "  infrastructure: 12 entities\n"]}], "source": ["# Process CAD files\n", "if not cad_files:\n", "    print(\"No CAD files to process\")\n", "    alignment_entities_df = pd.DataFrame()\n", "else:\n", "    print(f\"\\n=== PROCESSING CAD FILES FOR ALIGNMENT DATA ===\")\n", "    \n", "    all_alignment_entities = []\n", "    overall_stats = defaultdict(int)\n", "    file_stats = {}\n", "    \n", "    for file_path in cad_files:\n", "        print(f\"\\nProcessing: {file_path.name}\")\n", "        \n", "        try:\n", "            doc = ezdxf.readfile(file_path)\n", "            msp = doc.modelspace()\n", "            \n", "            file_entities = []\n", "            file_processing_stats = defaultdict(int)\n", "            \n", "            for entity in msp:\n", "                file_processing_stats['total_entities'] += 1\n", "                overall_stats['total_entities'] += 1\n", "                \n", "                entity_data = extract_alignment_entity_with_validation(entity)\n", "                if entity_data:\n", "                    entity_data['source_file'] = file_path.name\n", "                    file_entities.append(entity_data)\n", "                    all_alignment_entities.append(entity_data)\n", "                    \n", "                    file_processing_stats['extracted_entities'] += 1\n", "                    overall_stats['extracted_entities'] += 1\n", "                    \n", "                    classification = entity_data['classification']\n", "                    alignment_value = entity_data['alignment_value']\n", "                    \n", "                    file_processing_stats[f\"classification_{classification}\"] += 1\n", "                    file_processing_stats[f\"alignment_{alignment_value}\"] += 1\n", "                    overall_stats[f\"classification_{classification}\"] += 1\n", "                    overall_stats[f\"alignment_{alignment_value}\"] += 1\n", "                else:\n", "                    file_processing_stats['skipped_entities'] += 1\n", "                    overall_stats['skipped_entities'] += 1\n", "            \n", "            file_stats[file_path.name] = dict(file_processing_stats)\n", "            \n", "            print(f\"  Total entities: {file_processing_stats['total_entities']}\")\n", "            print(f\"  Extracted for alignment: {file_processing_stats['extracted_entities']}\")\n", "            print(f\"  Skipped (not alignment-relevant): {file_processing_stats['skipped_entities']}\")\n", "            \n", "            # Show classification breakdown for this file\n", "            classifications = [k.replace('classification_', '') for k in file_processing_stats.keys() if k.startswith('classification_')]\n", "            if classifications:\n", "                print(\"  Classifications: \" + ', '.join([f\"{c}({file_processing_stats[f'classification_{c}']})\" for c in classifications]))\n", "\n", "        except Exception as e:\n", "            logger.error(f\"Error processing {file_path.name}: {e}\")\n", "            continue\n", "    \n", "    print(f\"\\n=== OVERALL PROCESSING SUMMARY ===\")\n", "    print(f\"Files processed: {len([f for f in file_stats.keys()])}\")\n", "    print(f\"Total entities: {overall_stats['total_entities']}\")\n", "    print(f\"Extracted for alignment: {overall_stats['extracted_entities']}\")\n", "    print(f\"Extraction rate: {(overall_stats['extracted_entities'] / overall_stats['total_entities'] * 100):.1f}%\")\n", "    \n", "    # Convert to DataFrame\n", "    if all_alignment_entities:\n", "        alignment_entities_df = pd.DataFrame(all_alignment_entities)\n", "        print(f\"\\nAlignment DataFrame: {len(alignment_entities_df)} entities\")\n", "        \n", "        # Show alignment value distribution\n", "        alignment_dist = alignment_entities_df['alignment_value'].value_counts()\n", "        print(f\"\\nAlignment Value Distribution:\")\n", "        for value, count in alignment_dist.items():\n", "            print(f\"  {value}: {count} entities\")\n", "        \n", "        # Show classification distribution\n", "        class_dist = alignment_entities_df['classification'].value_counts()\n", "        print(f\"\\nClassification Distribution:\")\n", "        for classification, count in class_dist.items():\n", "            print(f\"  {classification}: {count} entities\")\n", "    else:\n", "        print(\"No alignment-relevant entities extracted\")\n", "        alignment_entities_df = pd.DataFrame()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Generate Multi-Site Compatible Outputs"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== GENERATING MULTI-SITE COMPATIBLE OUTPUTS ===\n", "Output directory: ../../../output_runs/cad_metadata/solar_project_alignment_20250701_141156\n", "Tracker alignment points: tracker_alignment_points_20250701_141156.csv (1110 points)\n", "Tracker coordinates: tracker_coordinates_20250701_141156.csv (1110 coordinates)\n", "Module boundaries: module_boundaries_20250701_141156.csv (60 areas)\n", "Complete alignment data: solar_alignment_data_20250701_141156.csv (1182 entities)\n", "Classification mapping: site_classification_mapping_20250701_141156.json (for reuse on similar sites)\n", "Alignment summary: alignment_summary_20250701_141156.json\n", "\n", "✅ Multi-site compatible alignment data extraction completed\n", "📁 Files ready for alignment workflows: ../../../output_runs/cad_metadata/solar_project_alignment_20250701_141156\n", "\n", "🎯 Recommended Usage:\n", "  1. Use tracker coordinates for initial point cloud registration\n", "  2. Use module boundaries for spatial extent validation\n", "  3. Apply coordinate transformations if needed (EPSG:32633)\n", "  4. Reuse classification mapping for similar solar project sites\n", "\n", "📅 Completed: 2025-07-01 14:11:56\n"]}], "source": ["if not alignment_entities_df.empty:\n", "    # Create output directory\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    output_path = Path(output_dir)\n", "    run_output_dir = output_path / f\"{project_type}_alignment_{timestamp}\"\n", "    run_output_dir.mkdir(parents=True, exist_ok=True)\n", "    \n", "    print(f\"\\n=== GENERATING MULTI-SITE COMPATIBLE OUTPUTS ===\")\n", "    print(f\"Output directory: {run_output_dir}\")\n", "    \n", "    # 1. High-value alignment points (trackers)\n", "    high_value = alignment_entities_df[alignment_entities_df['alignment_value'] == 'high']\n", "    if not high_value.empty:\n", "        tracker_file = run_output_dir / f\"tracker_alignment_points_{timestamp}.csv\"\n", "        high_value.to_csv(tracker_file, index=False)\n", "        print(f\"Tracker alignment points: {tracker_file.name} ({len(high_value)} points)\")\n", "        \n", "        # Create simplified coordinate file for alignment algorithms\n", "        coords_file = run_output_dir / f\"tracker_coordinates_{timestamp}.csv\"\n", "        coords_df = high_value[['x_coord', 'y_coord', 'z_coord', 'block_name', 'rotation', 'source_file']].copy()\n", "        coords_df.to_csv(coords_file, index=False)\n", "        print(f\"Tracker coordinates: {coords_file.name} ({len(coords_df)} coordinates)\")\n", "    \n", "    # 2. Module area boundaries\n", "    medium_value = alignment_entities_df[alignment_entities_df['alignment_value'] == 'medium']\n", "    if not medium_value.empty:\n", "        module_file = run_output_dir / f\"module_boundaries_{timestamp}.csv\"\n", "        medium_value.to_csv(module_file, index=False)\n", "        print(f\"Module boundaries: {module_file.name} ({len(medium_value)} areas)\")\n", "    \n", "    # 3. Complete alignment dataset\n", "    complete_file = run_output_dir / f\"solar_alignment_data_{timestamp}.csv\"\n", "    alignment_entities_df.to_csv(complete_file, index=False)\n", "    print(f\"Complete alignment data: {complete_file.name} ({len(alignment_entities_df)} entities)\")\n", "    \n", "    # 4. Site-specific classification mapping (for reuse)\n", "    classification_mapping = {\n", "        'site_specific_rules': classifier.site_specific_rules,\n", "        'discovered_layers': list(classifier.discovered_layers),\n", "        'classification_stats': alignment_entities_df['classification'].value_counts().to_dict(),\n", "        'files_processed': list(file_stats.keys()) if 'file_stats' in locals() else []\n", "    }\n", "    \n", "    mapping_file = run_output_dir / f\"site_classification_mapping_{timestamp}.json\"\n", "    with open(mapping_file, 'w') as f:\n", "        json.dump(classification_mapping, f, indent=2)\n", "    print(f\"Classification mapping: {mapping_file.name} (for reuse on similar sites)\")\n", "    \n", "    # 5. Comprehensive site configuration (enhanced)\n", "    if site_config:\n", "        # Update config with processing results\n", "        site_config['processing_results'] = {\n", "            'total_entities_processed': len(alignment_entities_df),\n", "            'high_value_points': len(high_value) if not high_value.empty else 0,\n", "            'medium_value_areas': len(medium_value) if not medium_value.empty else 0,\n", "            'coordinate_validation_applied': True,\n", "            'validation_success_rate': 'calculated_during_processing',\n", "            'files_successfully_processed': list(file_stats.keys()) if 'file_stats' in locals() else []\n", "        }\n", "        \n", "        comprehensive_config_file = run_output_dir / f\"comprehensive_site_config_{timestamp}.json\"\n", "        with open(comprehensive_config_file, 'w') as f:\n", "            json.dump(site_config, f, indent=2, default=str)\n", "        print(f\"Comprehensive site config: {comprehensive_config_file.name} (full reusable configuration)\")\n", "    \n", "    # 6. Alignment summary for workflows\n", "    alignment_summary = {\n", "        'extraction_metadata': {\n", "            'timestamp': timestamp,\n", "            'project': project_type,\n", "            'target_files': target_files,\n", "            'files_processed': list(file_stats.keys()) if 'file_stats' in locals() else [],\n", "            'coordinate_system': coordinate_system,\n", "            'focus': 'alignment_ready_data',\n", "            'multi_site_compatible': True\n", "        },\n", "        'processing_statistics': dict(overall_stats) if 'overall_stats' in locals() else {},\n", "        'file_statistics': file_stats if 'file_stats' in locals() else {},\n", "        'alignment_statistics': {\n", "            'total_entities': len(alignment_entities_df),\n", "            'high_value_points': len(high_value) if not high_value.empty else 0,\n", "            'medium_value_areas': len(medium_value) if not medium_value.empty else 0,\n", "            'coordinate_range': {\n", "                'x_min': float(alignment_entities_df['x_coord'].min()),\n", "                'x_max': float(alignment_entities_df['x_coord'].max()),\n", "                'y_min': float(alignment_entities_df['y_coord'].min()),\n", "                'y_max': float(alignment_entities_df['y_coord'].max())\n", "            }\n", "        },\n", "        'classification_summary': alignment_entities_df['classification'].value_counts().to_dict(),\n", "        'alignment_value_summary': alignment_entities_df['alignment_value'].value_counts().to_dict(),\n", "        'recommended_usage': {\n", "            'primary_alignment': 'Use tracker_coordinates.csv for initial point cloud registration',\n", "            'area_validation': 'Use module_boundaries.csv for spatial extent validation',\n", "            'complete_context': 'Use solar_alignment_data.csv for full spatial context',\n", "            'site_adaptation': 'Use site_classification_mapping.json for similar sites'\n", "        }\n", "    }\n", "    \n", "    summary_file = run_output_dir / f\"alignment_summary_{timestamp}.json\"\n", "    with open(summary_file, 'w') as f:\n", "        json.dump(alignment_summary, f, indent=2, default=str)\n", "    print(f\"Alignment summary: {summary_file.name}\")\n", "    \n", "    print(f\"\\n✅ Multi-site compatible alignment data extraction completed\")\n", "    print(f\"📁 Files ready for alignment workflows: {run_output_dir}\")\n", "    \n", "    print(f\"\\n🎯 Recommended Usage:\")\n", "    print(f\"  1. Use tracker coordinates for initial point cloud registration\")\n", "    print(f\"  2. Use module boundaries for spatial extent validation\")\n", "    print(f\"  3. Apply coordinate transformations if needed (EPSG:32633)\")\n", "    print(f\"  4. Reuse classification mapping for similar solar project sites\")\n", "    \n", "else:\n", "    print(\"No alignment data to export\")\n", "\n", "print(f\"\\n📅 Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "\n", "# Final summary of enhancements\n", "print(f\"\\n\" + \"=\" * 60)\n", "print(f\"ENHANCED SOLAR CAD EXTRACTION SUMMARY\")\n", "print(f\"=\" * 60)\n", "print(f\"\\n🔧 Enhancements Applied:\")\n", "print(f\"  ✅ ogrinfo integration for advanced CAD analysis\")\n", "print(f\"  ✅ Automated site configuration generation\")\n", "print(f\"  ✅ Adaptive coordinate validation (no hard-coding)\")\n", "print(f\"  ✅ Multi-site compatible classification rules\")\n", "print(f\"  ✅ Comprehensive configuration export for reuse\")\n", "\n", "if site_config:\n", "    coord_system = site_config.get('coordinate_system', {}).get('detected_type', 'unknown')\n", "    bounds_available = site_config.get('spatial_bounds') is not None\n", "    print(f\"\\n📊 Site Analysis Results:\")\n", "    print(f\"  Coordinate system detected: {coord_system}\")\n", "    print(f\"  Spatial bounds available: {'Yes' if bounds_available else 'No'}\")\n", "    print(f\"  Validation method: {'Bounds + Statistical' if bounds_available else 'Statistical only'}\")\n", "    \n", "print(f\"\\n🚀 Ready for multi-site deployment!\")\n", "print(f\"=\" * 60)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}