{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Solar CAD Metadata Extraction\n", "\n", "This notebook extracts solar tracker and module information from CAD files in a simple, easy-to-understand way.\n", "\n", "## What this notebook does:\n", "1. **Finds CAD files** using simple commands\n", "2. **Analyzes CAD structure** with basic tools\n", "3. **Extracts coordinates** of solar trackers and modules\n", "4. **Saves clean data** for alignment workflows\n", "\n", "## What you'll get:\n", "- **tracker_coordinates.csv**: Solar tracker positions (for alignment)\n", "- **module_boundaries.csv**: Solar panel area boundaries\n", "- **summary.json**: Simple summary of what was found\n", "\n", "**Target Files**: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf/dwg  \n", "**Author**: <PERSON><PERSON><PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Setup - Import what we need"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "import ezdxf\n", "import json\n", "from datetime import datetime\n", "from collections import defaultdict\n", "import logging\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(\"All tools imported successfully!\")\n", "logger.info(f\"Starting extraction at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Configuration - Tell the notebook what to look for"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Papermill parameters - these will be injected by Papermill\n", "site_name = \"Castro\"  # Site name for output file naming\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "\n", "target_files = [\"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\"]\n", "                #, \"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg\"]\n", "search_path = \"../../../data/raw\"\n", "output_dir = \"../../../output_runs/cad_metadata\"\n", "\n", "logger.info(\"Configuration:\")\n", "logger.info(f\"   Looking for: {target_files}\")\n", "logger.info(f\"   Search in: {search_path}\")\n", "logger.info(f\"   Save to: {output_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Find CAD Files - Use simple commands to locate files"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(\"STEP 3: Finding CAD files...\")\n", "logger.info(\"=\"*50)\n", "\n", "# List to store files we find\n", "found_files = []\n", "\n", "# Look for each target file\n", "for target_file in target_files:\n", "    print(f\"\\nLooking for: {target_file}\")\n", "    \n", "    # Use the 'find' command to search for the file\n", "    result = !find {search_path} -name \"{target_file}\" 2>/dev/null\n", "    \n", "    if result:  # If we found the file\n", "        file_path = Path(result[0])  # Get the first result\n", "        file_size = file_path.stat().st_size / 1024 / 1024  # Size in MB\n", "        \n", "        print(f\"-  Found: {file_path}\")\n", "        print(f\"-  Size: {file_size:.1f} MB\")\n", "        \n", "        found_files.append(file_path)\n", "    else:\n", "        print(f\"-  Not found: {target_file}\")\n", "\n", "logger.info(f\"\\nSummary: Found {len(found_files)} out of {len(target_files)} target files\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Quick Analysis - Look at CAD file structure (optional)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"STEP 4: Quick CAD analysis...\")\n", "print(\"=\" * 50)\n", "\n", "# Check if 'ogrinfo' tool is available\n", "ogrinfo_check = !which ogrinfo 2>/dev/null\n", "\n", "if ogrinfo_check:\n", "    print(\"ogrinfo tool is available – showing structure of DXF files...\\n\")\n", "    \n", "    # Analyze each DXF file using ogrinfo\n", "    for file_path in found_files:\n", "        if file_path.suffix.lower() == '.dxf':\n", "            print(f\"\\nAnalyzing: {file_path.name}\")\n", "            print(\"-\" * 40)\n", "            !ogrinfo -so \"{file_path}\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Extract Data - Get tracker and module coordinates"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_solar_data(file_path):\n", "    \"\"\"\n", "    Extract solar tracker and module data from a CAD file.\n", "    \n", "    This function looks for:\n", "    - Solar trackers (INSERT entities in tracker layers)\n", "    - Solar modules (POLYLINE entities in module layers)\n", "    \"\"\"\n", "    print(f\"\\n🔧 Processing: {file_path.name}\")\n", "    \n", "    try:\n", "        # Open the CAD file\n", "        doc = ezdxf.readfile(file_path)\n", "        modelspace = doc.modelspace()  # Get the main drawing space\n", "        \n", "        # Lists to store what we find\n", "        trackers = []  # Solar tracker positions\n", "        modules = []   # Solar module boundaries\n", "        \n", "        print(\"   🔍 Scanning entities...\")\n", "        \n", "        # Look at each entity (object) in the CAD file\n", "        for entity in modelspace:\n", "            # Get basic info about this entity\n", "            layer_name = getattr(entity.dxf, 'layer', '').upper()  # Layer name in uppercase\n", "            entity_type = entity.dxftype()  # Type of entity (INSERT, LINE, etc.)\n", "            \n", "            # Look for SOLAR TRACKERS\n", "            # These are usually INSERT entities in layers with 'TRACKER' or 'CVT' in the name\n", "            if (entity_type == 'INSERT' and \n", "                any(keyword in layer_name for keyword in ['TRACKER', 'CVT'])):\n", "                \n", "                if hasattr(entity.dxf, 'insert'):  # Make sure it has coordinates\n", "                    point = entity.dxf.insert\n", "                    \n", "                    # Skip invalid coordinates (0,0,0 usually means no real position)\n", "                    if not (point.x == 0.0 and point.y == 0.0):\n", "                        tracker_data = {\n", "                            'x': point.x,\n", "                            'y': point.y,\n", "                            'z': point.z,\n", "                            'layer': layer_name,\n", "                            'block_name': getattr(entity.dxf, 'name', ''),\n", "                            'type': 'tracker',\n", "                            'source_file': file_path.name\n", "                        }\n", "                        trackers.append(tracker_data)\n", "            \n", "            # Look for SOLAR MODULES\n", "            # These are usually POLYLINE entities in layers with 'PVCASE', 'PV', or 'MODULE' in the name\n", "            elif (entity_type in ['LWPOLYLINE', 'POLYLINE'] and \n", "                  any(keyword in layer_name for keyword in ['PVCASE', 'PV', 'MODULE'])):\n", "                \n", "                try:\n", "                    # Get the boundary box of this polyline\n", "                    bbox = entity.bbox()\n", "                    if bbox:\n", "                        # Calculate center point\n", "                        center_x = (bbox[0].x + bbox[1].x) / 2\n", "                        center_y = (bbox[0].y + bbox[1].y) / 2\n", "                        center_z = (bbox[0].z + bbox[1].z) / 2\n", "                        \n", "                        # Skip invalid coordinates\n", "                        if not (center_x == 0.0 and center_y == 0.0):\n", "                            module_data = {\n", "                                'x': center_x,\n", "                                'y': center_y,\n", "                                'z': center_z,\n", "                                'layer': layer_name,\n", "                                'x_min': bbox[0].x,\n", "                                'y_min': bbox[0].y,\n", "                                'x_max': bbox[1].x,\n", "                                'y_max': bbox[1].y,\n", "                                'type': 'module',\n", "                                'source_file': file_path.name\n", "                            }\n", "                            modules.append(module_data)\n", "                except:\n", "                    # If we can't get the boundary, skip this entity\n", "                    pass\n", "        \n", "        print(f\"   ✅ Found {len(trackers)} trackers and {len(modules)} modules\")\n", "        return trackers, modules\n", "        \n", "    except Exception as e:\n", "        print(f\"   ❌ Error processing {file_path.name}: {e}\")\n", "        return [], []\n", "\n", "print(\"🔧 Data extraction function ready!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Process All Files - Extract data from each CAD file"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"⚙️ STEP 6: Processing all CAD files...\")\n", "print(\"=\"*50)\n", "\n", "# Lists to store all data from all files\n", "all_trackers = []\n", "all_modules = []\n", "\n", "# Process each file we found\n", "for file_path in found_files:\n", "    # Only process DXF files (ezdxf works best with DXF)\n", "    if file_path.suffix.lower() == '.dxf':\n", "        # Extract data from this file\n", "        trackers, modules = extract_solar_data(file_path)\n", "        \n", "        # Add to our master lists\n", "        all_trackers.extend(trackers)\n", "        all_modules.extend(modules)\n", "        \n", "    else:\n", "        print(f\"\\n⏭️ Skipping: {file_path.name}\")\n", "        print(\"   (DWG files need to be converted to DXF first)\")\n", "        print(\"   (You can use AutoCAD, FreeCAD, or online converters)\")\n", "\n", "# Show summary of what we found\n", "print(f\"\\n📊 EXTRACTION COMPLETE!\")\n", "print(f\"   Total trackers found: {len(all_trackers)}\")\n", "print(f\"   Total modules found: {len(all_modules)}\")\n", "\n", "# Show coordinate ranges if we found data\n", "if all_trackers:\n", "    tracker_coords = np.array([[t['x'], t['y']] for t in all_trackers])\n", "    print(f\"\\n📍 Tracker coordinate ranges:\")\n", "    print(f\"   X: {tracker_coords[:, 0].min():.1f} to {tracker_coords[:, 0].max():.1f}\")\n", "    print(f\"   Y: {tracker_coords[:, 1].min():.1f} to {tracker_coords[:, 1].max():.1f}\")\n", "\n", "if all_modules:\n", "    module_coords = np.array([[m['x'], m['y']] for m in all_modules])\n", "    print(f\"\\n📍 Module coordinate ranges:\")\n", "    print(f\"   X: {module_coords[:, 0].min():.1f} to {module_coords[:, 0].max():.1f}\")\n", "    print(f\"   Y: {module_coords[:, 1].min():.1f} to {module_coords[:, 1].max():.1f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Save Results - Create clean files for alignment workflows"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"💾 STEP 7: Saving results...\")\n", "print(\"=\"*50)\n", "\n", "if all_trackers or all_modules:\n", "    # Create output directory with timestamp\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    output_path = Path(output_dir)\n", "    run_output_dir = output_path / f\"solar_extraction_{timestamp}\"\n", "    run_output_dir.mkdir(parents=True, exist_ok=True)\n", "    \n", "    print(f\"📁 Output directory: {run_output_dir}\")\n", "    \n", "    # Save tracker coordinates (most important for alignment)\n", "    if all_trackers:\n", "        trackers_df = pd.DataFrame(all_trackers)\n", "        tracker_file = run_output_dir / f\"tracker_coordinates_{timestamp}.csv\"\n", "        trackers_df.to_csv(tracker_file, index=False)\n", "        print(f\"✅ Saved tracker coordinates: {tracker_file.name}\")\n", "        print(f\"   📊 {len(all_trackers)} tracker positions\")\n", "    \n", "    # Save module boundaries (useful for validation)\n", "    if all_modules:\n", "        modules_df = pd.DataFrame(all_modules)\n", "        module_file = run_output_dir / f\"module_boundaries_{timestamp}.csv\"\n", "        modules_df.to_csv(module_file, index=False)\n", "        print(f\"✅ Saved module boundaries: {module_file.name}\")\n", "        print(f\"   📊 {len(all_modules)} module areas\")\n", "    \n", "    # Create a simple summary\n", "    summary = {\n", "        'extraction_info': {\n", "            'timestamp': timestamp,\n", "            'target_files': target_files,\n", "            'files_processed': [f.name for f in found_files if f.suffix.lower() == '.dxf']\n", "        },\n", "        'results': {\n", "            'tracker_count': len(all_trackers),\n", "            'module_count': len(all_modules)\n", "        },\n", "        'coordinate_info': {},\n", "        'next_steps': {\n", "            'for_alignment': 'Use tracker_coordinates.csv as reference points for point cloud alignment',\n", "            'for_validation': 'Use module_boundaries.csv to validate spatial coverage',\n", "            'coordinate_system': 'Coordinates appear to be in UTM format (suitable for alignment)'\n", "        }\n", "    }\n", "    \n", "    # Add coordinate ranges to summary\n", "    if all_trackers:\n", "        summary['coordinate_info']['tracker_ranges'] = {\n", "            'x_min': float(tracker_coords[:, 0].min()),\n", "            'x_max': float(tracker_coords[:, 0].max()),\n", "            'y_min': float(tracker_coords[:, 1].min()),\n", "            'y_max': float(tracker_coords[:, 1].max())\n", "        }\n", "    \n", "    if all_modules:\n", "        summary['coordinate_info']['module_ranges'] = {\n", "            'x_min': float(module_coords[:, 0].min()),\n", "            'x_max': float(module_coords[:, 0].max()),\n", "            'y_min': float(module_coords[:, 1].min()),\n", "            'y_max': float(module_coords[:, 1].max())\n", "        }\n", "    \n", "    # Save summary\n", "    summary_file = run_output_dir / f\"extraction_summary_{timestamp}.json\"\n", "    with open(summary_file, 'w') as f:\n", "        json.dump(summary, f, indent=2)\n", "    print(f\"✅ Saved summary: {summary_file.name}\")\n", "    \n", "    print(f\"\\n🎯 SUCCESS! All files saved to: {run_output_dir}\")\n", "    print(f\"\\n📋 What to do next:\")\n", "    print(f\"   1. Use tracker_coordinates.csv for point cloud alignment\")\n", "    print(f\"   2. Use module_boundaries.csv for spatial validation\")\n", "    print(f\"   3. Check extraction_summary.json for details\")\n", "    \n", "else:\n", "    print(\"❌ No data was extracted!\")\n", "    print(\"\\n🔍 Troubleshooting:\")\n", "    print(\"   1. Check that DXF files were found\")\n", "    print(\"   2. Verify CAD files contain tracker/module layers\")\n", "    print(\"   3. Look for layers with names like 'TRACKER', 'CVT', 'PVCASE', 'PV'\")\n", "\n", "print(f\"\\n📅 Extraction completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}