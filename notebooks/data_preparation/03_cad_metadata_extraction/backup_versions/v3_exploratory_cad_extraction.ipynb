{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Exploratory CAD Metadata Extraction for Alignment and Labeling\n", "\n", "This notebook provides step-by-step CAD metadata extraction from DXF files using simple functions and exploratory analysis.\n", "\n", "**Stage**: Data Preparation - CAD Metadata Extraction  \n", "**Input Data**: DXF/DWG files from CAD directory  \n", "**Output**: Structured metadata in CSV/JSON format following metadata schema  \n", "**Purpose**: Extract geometric entities, coordinates, and annotations for point cloud alignment and labeling  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## DXF Entity Types Reference Guide\n", "\n", "Based on AutoCAD DXF Reference and common entities found in solar project CAD files:\n", "\n", "### Geometric Entities\n", "- **CIRCLE**: Circular arcs and circles (often used for piles, manholes)\n", "- **LINE**: Straight line segments (boundaries, connections, infrastructure)\n", "- **LWPOLYLINE**: Lightweight polylines (complex shapes, site boundaries, roads)\n", "- **POLYLINE**: 3D polylines with vertices (terrain contours, complex paths)\n", "- **ARC**: Circular arc segments (curved roads, rounded corners)\n", "- **POINT**: Single coordinate points (survey points, reference markers)\n", "\n", "### Block and Text Entities\n", "- **INSERT**: Block references (solar panels, equipment, symbols)\n", "- **TEXT**: Single-line text (labels, dimensions, annotations)\n", "- **MTEXT**: Multi-line text (descriptions, notes, specifications)\n", "- **ATTDEF**: Attribute definitions in blocks\n", "- **DIMENSION**: Dimension lines and text\n", "\n", "### Surface and Fill Entities\n", "- **HATCH**: Filled areas and patterns (buildings, water bodies, zones)\n", "- **SOLID**: Solid filled triangular areas\n", "- **TRACE**: Solid filled quadrilateral areas\n", "\n", "### Advanced Entities\n", "- **SPLINE**: Smooth curves (organic shapes, terrain features)\n", "- **ELLIPSE**: Elliptical shapes\n", "- **VIEWPORT**: Layout viewports\n", "- **IMAGE**: <PERSON><PERSON> image references\n", "- **OLE2FRAME**: Embedded OLE objects\n", "- **PDFREFERENCE**: PDF underlay references\n", "\n", "**Reference**: [AutoCAD DXF Reference](https://help.autodesk.com/view/OARX/2023/ENU/?guid=GUID-235B22E0-A567-4CF6-92D3-38A2306D73F3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Libraries and Setup"]}, {"cell_type": "code", "execution_count": 45, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters\n", "project_type = \"motali_de_castro\"\n", "site_name = \"main_site\"\n", "cad_data_path = \"../../../data/raw/motali_de_castro/cad\"\n", "output_dir = \"../../../output_runs/cad_metadata\"\n", "target_files = [\"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\", \"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg\"]\n", "coordinate_system = \"EPSG:32633\"  # UTM Zone 33N for Italy\n", "classification_rules_file = None  # Optional custom classification rules"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enhanced CAD Metadata Extraction - Starting...\n", "Timestamp: 2025-06-30 17:41:29\n", "Project: motali_de_castro/main_site\n"]}], "source": ["import os\n", "import sys\n", "import logging\n", "import json\n", "import csv\n", "from pathlib import Path\n", "from datetime import datetime\n", "from typing import Dict, List, Tuple, Optional, Any\n", "from collections import defaultdict\n", "\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import ezdxf\n", "from ezdxf import recover\n", "import mlflow\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"Enhanced CAD Metadata Extraction - Starting...\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(f\"Project: {project_type}/{site_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup Paths and Directories"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CAD data path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/motali_de_castro/cad\n", "Output directory: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/output_runs/cad_metadata/motali_de_castro_main_site_20250630_174129\n", "Setup completed - Output directory: ../../../output_runs/cad_metadata/motali_de_castro_main_site_20250630_174129\n"]}], "source": ["# Setup paths and directories\n", "cad_path = Path(cad_data_path)\n", "output_path = Path(output_dir)\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# Create timestamped output directory\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "run_output_dir = output_path / f\"{project_type}_{site_name}_{timestamp}\"\n", "run_output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"CAD data path: {cad_path.resolve()}\")\n", "print(f\"Output directory: {run_output_dir.resolve()}\")\n", "\n", "# Verify CAD directory exists\n", "if not cad_path.exists():\n", "    raise FileNotFoundError(f\"CAD directory not found: {cad_path}\")\n", "\n", "print(f\"Setup completed - Output directory: {run_output_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define Classification Rules\n", "\n", "Set up classification rules for different entity types based on layer names, block names, and text content."]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Defined classification rules for 8 categories:\n", "  pile: 3 layer keywords, priority 3\n", "  panel: 7 layer keywords, priority 2\n", "  road: 6 layer keywords, priority 2\n", "  trench: 6 layer keywords, priority 2\n", "  foundation: 6 layer keywords, priority 3\n", "  electrical: 6 layer keywords, priority 1\n", "  building: 5 layer keywords, priority 2\n", "  annotation: 6 layer keywords, priority 1\n"]}], "source": ["# Define classification rules for CAD entities\n", "# Each category has keywords for layers, blocks, text, and preferred entity types\n", "\n", "classification_rules = {\n", "    'pile': {\n", "        'layer_keywords': ['pile', 'palo', 'pali'],\n", "        'block_keywords': ['pile', 'palo', 'p_'],\n", "        'text_keywords': ['pile', 'palo', 'p-'],\n", "        'entity_types': ['CIRCLE', 'INSERT', 'POINT'],\n", "        'priority': 3  # High priority for alignment\n", "    },\n", "    'panel': {\n", "        'layer_keywords': ['panel', 'pannello', 'pv', 'solar', 'solare', 'modulo', 'moduli'],\n", "        'block_keywords': ['panel', 'pannello', 'pv', 'solar', 'modulo'],\n", "        'text_keywords': ['panel', 'pv', 'solar', 'modulo'],\n", "        'entity_types': ['INSERT', 'LWPOLYLINE', 'POLYLINE'],\n", "        'priority': 2\n", "    },\n", "    'road': {\n", "        'layer_keywords': ['road', 'strada', 'strade', 'access', 'accesso', 'viabilita'],\n", "        'block_keywords': ['road', 'strada', 'access'],\n", "        'text_keywords': ['road', 'strada', 'access'],\n", "        'entity_types': ['LWPOLYLINE', 'POLYLINE', 'LINE', 'ARC'],\n", "        'priority': 2\n", "    },\n", "    'trench': {\n", "        'layer_keywords': ['trench', 'trincea', 'cable', 'cavo', 'cavidotto', 'cavidotti'],\n", "        'block_keywords': ['trench', 'cable', 'cavo', 'cavidotto'],\n", "        'text_keywords': ['trench', 'cable', 'cavo', 'cavidotto'],\n", "        'entity_types': ['LWPOLYLINE', 'POLYLINE', 'LINE'],\n", "        'priority': 2\n", "    },\n", "    'foundation': {\n", "        'layer_keywords': ['foundation', 'fondazione', 'base', 'cabin', 'cabina', 'cabine'],\n", "        'block_keywords': ['foundation', 'cabin', 'cabina', 'base'],\n", "        'text_keywords': ['foundation', 'cabin', 'cabina', 'base'],\n", "        'entity_types': ['LWPOLYLINE', 'POLYLINE', 'INSERT'],\n", "        'priority': 3  # High priority for alignment\n", "    },\n", "    'electrical': {\n", "        'layer_keywords': ['electrical', 'elettrico', 'elettrica', 'power', 'energia', 'electric'],\n", "        'block_keywords': ['electrical', 'elettrico', 'power', 'electric'],\n", "        'text_keywords': ['electrical', 'power', 'kw', 'v', 'elettrico'],\n", "        'entity_types': ['LINE', 'LWPOLYLINE', 'INSERT'],\n", "        'priority': 1\n", "    },\n", "    'building': {\n", "        'layer_keywords': ['building', 'edificio', 'fabbricato', 'manufatto', 'costruzione'],\n", "        'block_keywords': ['building', 'edificio', 'fabbricato'],\n", "        'text_keywords': ['building', 'edificio'],\n", "        'entity_types': ['LWPOLYLINE', 'POLYLINE', 'INSERT', 'HATCH'],\n", "        'priority': 2\n", "    },\n", "    'annotation': {\n", "        'layer_keywords': ['text', 'label', 'annotation', 'quota', 'dimension', 'dim'],\n", "        'block_keywords': ['text', 'label', 'annotation'],\n", "        'text_keywords': [],\n", "        'entity_types': ['TEXT', 'MTEXT', 'DIMENSION'],\n", "        'priority': 1\n", "    }\n", "}\n", "\n", "print(f\"Defined classification rules for {len(classification_rules)} categories:\")\n", "for category, rules in classification_rules.items():\n", "    print(f\"  {category}: {len(rules['layer_keywords'])} layer keywords, priority {rules['priority']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Entity Classification Function\n", "\n", "Simple function to classify CAD entities based on multiple criteria with priority scoring."]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Entity classification function defined\n", "Supports multi-criteria classification with priority scoring\n"]}], "source": ["# Define entity classification function with priority scoring\n", "def classify_entity(entity, layer_name=\"\", block_name=\"\", text_content=\"\"):\n", "    \"\"\"Classify CAD entity based on multiple criteria with priority scoring.\"\"\"\n", "    entity_type = entity.dxftype()\n", "    layer_name = layer_name.lower()\n", "    block_name = block_name.lower()\n", "    text_content = text_content.lower()\n", "    \n", "    scores = defaultdict(int)\n", "    \n", "    for category, rules in classification_rules.items():\n", "        base_score = rules.get('priority', 1)\n", "        \n", "        # Check entity type match\n", "        if entity_type in rules['entity_types']:\n", "            scores[category] += base_score * 2\n", "        \n", "        # Check layer keywords with higher weight\n", "        for keyword in rules['layer_keywords']:\n", "            if keyword in layer_name:\n", "                scores[category] += base_score * 4\n", "        \n", "        # Check block keywords\n", "        for keyword in rules['block_keywords']:\n", "            if keyword in block_name:\n", "                scores[category] += base_score * 3\n", "        \n", "        # Check text keywords\n", "        for keyword in rules['text_keywords']:\n", "            if keyword in text_content:\n", "                scores[category] += base_score * 2\n", "    \n", "    # Return highest scoring category or 'unknown'\n", "    if scores:\n", "        return max(scores.items(), key=lambda x: x[1])[0]\n", "    return 'unknown'\n", "\n", "print(\"Entity classification function defined\")\n", "print(\"Supports multi-criteria classification with priority scoring\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Entity Metadata Extraction Functions\n", "\n", "Functions to extract comprehensive metadata from different CAD entity types."]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Entity-specific extraction functions defined\n", "Supports CIRCLE, LINE, INSERT, TEXT, and MTEXT entities\n"]}], "source": ["def extract_circle_data(entity):\n", "    \"\"\"Extract data from CIRCLE entities.\"\"\"\n", "    center = entity.dxf.center\n", "    return {\n", "        'x_coord': center[0],\n", "        'y_coord': center[1],\n", "        'z_coord': center[2] if len(center) > 2 else 0.0,\n", "        'radius': entity.dxf.radius,\n", "        'geometry_type': 'circle'\n", "    }\n", "\n", "def extract_line_data(entity):\n", "    \"\"\"Extract data from LINE entities.\"\"\"\n", "    start = entity.dxf.start\n", "    end = entity.dxf.end\n", "    return {\n", "        'x_coord': (start[0] + end[0]) / 2,  # Midpoint\n", "        'y_coord': (start[1] + end[1]) / 2,\n", "        'z_coord': (start[2] + end[2]) / 2 if len(start) > 2 else 0.0,\n", "        'start_x': start[0],\n", "        'start_y': start[1],\n", "        'start_z': start[2] if len(start) > 2 else 0.0,\n", "        'end_x': end[0],\n", "        'end_y': end[1],\n", "        'end_z': end[2] if len(end) > 2 else 0.0,\n", "        'length': np.linalg.norm(np.array(end) - np.array(start)),\n", "        'geometry_type': 'line'\n", "    }\n", "\n", "def extract_insert_data(entity):\n", "    \"\"\"Extract data from INSERT (block reference) entities.\"\"\"\n", "    insert_point = entity.dxf.insert\n", "    return {\n", "        'x_coord': insert_point[0],\n", "        'y_coord': insert_point[1],\n", "        'z_coord': insert_point[2] if len(insert_point) > 2 else 0.0,\n", "        'block_name': entity.dxf.name,\n", "        'rotation': getattr(entity.dxf, 'rotation', 0.0),\n", "        'scale_x': getattr(entity.dxf, 'xscale', 1.0),\n", "        'scale_y': getattr(entity.dxf, 'yscale', 1.0),\n", "        'scale_z': getattr(entity.dxf, 'zscale', 1.0),\n", "        'geometry_type': 'insert'\n", "    }\n", "\n", "def extract_text_data(entity):\n", "    \"\"\"Extract data from TEXT and MTEXT entities.\"\"\"\n", "    insert_point = entity.dxf.insert\n", "    text_content = entity.dxf.text if entity.dxftype() == 'TEXT' else entity.text\n", "    return {\n", "        'x_coord': insert_point[0],\n", "        'y_coord': insert_point[1],\n", "        'z_coord': insert_point[2] if len(insert_point) > 2 else 0.0,\n", "        'text_content': text_content,\n", "        'text_height': getattr(entity.dxf, 'height', None),\n", "        'text_rotation': getattr(entity.dxf, 'rotation', 0.0),\n", "        'geometry_type': 'text'\n", "    }\n", "\n", "print(\"Entity-specific extraction functions defined\")\n", "print(\"Supports CIRCLE, LINE, INSERT, TEXT, and MTEXT entities\")"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Additional extraction functions defined\n", "Supports POLYLINE entities and default handling\n"]}], "source": ["def extract_polyline_data(entity):\n", "    \"\"\"Extract data from LWPOLYLINE and POLYLINE entities.\"\"\"\n", "    # Get polyline points\n", "    if hasattr(entity, 'get_points'):\n", "        points = list(entity.get_points())\n", "    else:\n", "        points = []\n", "    \n", "    if points:\n", "        # Calculate centroid\n", "        points_array = np.array(points)\n", "        centroid = np.mean(points_array, axis=0)\n", "        return {\n", "            'x_coord': centroid[0],\n", "            'y_coord': centroid[1],\n", "            'z_coord': centroid[2] if len(centroid) > 2 else 0.0,\n", "            'point_count': len(points),\n", "            'is_closed': getattr(entity.dxf, 'flags', 0) & 1,\n", "            'geometry_type': 'polyline'\n", "        }\n", "    else:\n", "        return {\n", "            'x_coord': 0.0,\n", "            'y_coord': 0.0,\n", "            'z_coord': 0.0,\n", "            'geometry_type': 'polyline'\n", "        }\n", "\n", "def extract_default_data(entity):\n", "    \"\"\"Extract default data for other entity types.\"\"\"\n", "    return {\n", "        'x_coord': 0.0,\n", "        'y_coord': 0.0,\n", "        'z_coord': 0.0,\n", "        'geometry_type': 'other'\n", "    }\n", "\n", "print(\"Additional extraction functions defined\")\n", "print(\"Supports POLYLINE entities and default handling\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Main Entity Processing Function\n", "\n", "Comprehensive function to process any CAD entity and extract all relevant metadata."]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Main entity processing function defined\n", "Handles all entity types with comprehensive metadata extraction\n"]}], "source": ["def extract_entity_metadata(entity):\n", "    \"\"\"Extract comprehensive metadata from any CAD entity.\"\"\"\n", "    try:\n", "        entity_type = entity.dxftype()\n", "        handle = getattr(entity.dxf, 'handle', 'unknown')\n", "        layer_name = getattr(entity.dxf, 'layer', '')\n", "        \n", "        # Base entity data\n", "        entity_data = {\n", "            'entity_id': handle,\n", "            'entity_type': entity_type,\n", "            'layer_name': layer_name,\n", "            'color': getattr(entity.dxf, 'color', None),\n", "            'linetype': getattr(entity.dxf, 'linetype', None),\n", "            'lineweight': getattr(entity.dxf, 'lineweight', None)\n", "        }\n", "        \n", "        # Extract geometry based on entity type\n", "        if entity_type == 'CIRCLE':\n", "            entity_data.update(extract_circle_data(entity))\n", "        elif entity_type == 'LINE':\n", "            entity_data.update(extract_line_data(entity))\n", "        elif entity_type == 'INSERT':\n", "            entity_data.update(extract_insert_data(entity))\n", "        elif entity_type in ['TEXT', 'MTEXT']:\n", "            entity_data.update(extract_text_data(entity))\n", "        elif entity_type in ['LWPOLYLINE', 'POLYLINE']:\n", "            entity_data.update(extract_polyline_data(entity))\n", "        else:\n", "            entity_data.update(extract_default_data(entity))\n", "        \n", "        # Classify entity\n", "        block_name = entity_data.get('block_name', '')\n", "        text_content = entity_data.get('text_content', '')\n", "        entity_data['classification'] = classify_entity(\n", "            entity, layer_name, block_name, text_content\n", "        )\n", "        \n", "        # Add extraction metadata\n", "        entity_data['extraction_timestamp'] = datetime.now().isoformat()\n", "        \n", "        return entity_data\n", "        \n", "    except Exception as e:\n", "        logger.warning(f\"Error extracting entity data: {e}\")\n", "        return {\n", "            'entity_id': 'error',\n", "            'entity_type': getattr(entity, 'dxftype', lambda: 'unknown')(),\n", "            'error': str(e),\n", "            'extraction_timestamp': datetime.now().isoformat()\n", "        }\n", "\n", "print(\"Main entity processing function defined\")\n", "print(\"Handles all entity types with comprehensive metadata extraction\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## File Discovery and Initial Analysis"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MLflow experiment tracking initialized\n", "Experiment: cad_metadata_extraction_motali_de_castro\n", "Run name: main_site_cad_extraction_20250630_174129\n"]}], "source": ["# Initialize MLflow tracking\n", "mlflow.set_experiment(f\"cad_metadata_extraction_{project_type}\")\n", "\n", "# Start MLflow run\n", "mlflow.start_run(run_name=f\"{site_name}_cad_extraction_{timestamp}\")\n", "\n", "# Log parameters\n", "mlflow.log_param(\"project_type\", project_type)\n", "mlflow.log_param(\"site_name\", site_name)\n", "mlflow.log_param(\"coordinate_system\", coordinate_system)\n", "mlflow.log_param(\"target_files\", \",\".join(target_files))\n", "\n", "print(\"MLflow experiment tracking initialized\")\n", "print(f\"Experiment: cad_metadata_extraction_{project_type}\")\n", "print(f\"Run name: {site_name}_cad_extraction_{timestamp}\")"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Discovery\n", "==================================================\n", "Found target file: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\n", "Found target file: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg\n", "\n", "Total files to process: 2\n", "  GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf: 15.52 MB\n", "  GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg: 4.64 MB\n"]}], "source": ["# Discover CAD files\n", "print(\"File Discovery\")\n", "print(\"=\" * 50)\n", "\n", "discovered_files = []\n", "\n", "if target_files:\n", "    # Process specific target files\n", "    for target_file in target_files:\n", "        file_path = cad_path / target_file\n", "        if file_path.exists() and file_path.suffix.lower() in ['.dxf', '.dwg']:\n", "            discovered_files.append(file_path)\n", "            print(f\"Found target file: {target_file}\")\n", "        else:\n", "            print(f\"Target file not found or invalid: {target_file}\")\n", "else:\n", "    # Discover all DXF files in directory\n", "    for file_path in cad_path.rglob(\"*.dxf\"):\n", "        discovered_files.append(file_path)\n", "        print(f\"Discovered DXF file: {file_path.name}\")\n", "\n", "print(f\"\\nTotal files to process: {len(discovered_files)}\")\n", "mlflow.log_metric(\"files_discovered\", len(discovered_files))\n", "\n", "# Display file information\n", "for file_path in discovered_files:\n", "    file_size_mb = file_path.stat().st_size / (1024 * 1024)\n", "    print(f\"  {file_path.name}: {file_size_mb:.2f} MB\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## DXF File Loading and Initial Inspection"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing Files\n", "==================================================\n", "\n", "Processing: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\n", "  Successfully loaded DXF file\n", "  DXF version: AC1027\n", "  Number of layers: 78\n", "  Number of blocks: 34\n", "Skipping non-DXF file: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg (DWG processing requires conversion)\n"]}], "source": ["# Process each DXF file\n", "all_entities = []\n", "all_file_stats = []\n", "processing_summary = {\n", "    'total_files': len(discovered_files),\n", "    'successful_files': 0,\n", "    'failed_files': 0,\n", "    'total_entities': 0,\n", "    'classification_summary': defaultdict(int),\n", "    'entity_type_summary': defaultdict(int)\n", "}\n", "\n", "print(\"Processing Files\")\n", "print(\"=\" * 50)\n", "\n", "for file_path in discovered_files:\n", "    if file_path.suffix.lower() == '.dxf':\n", "        print(f\"\\nProcessing: {file_path.name}\")\n", "        \n", "        # Initialize file statistics\n", "        file_stats = {\n", "            'file_name': file_path.name,\n", "            'file_path': str(file_path),\n", "            'file_size_mb': file_path.stat().st_size / (1024 * 1024),\n", "            'processing_timestamp': datetime.now().isoformat(),\n", "            'entity_counts': defaultdict(int),\n", "            'classification_counts': defaultdict(int),\n", "            'layer_counts': defaultdict(int),\n", "            'coordinate_bounds': {'min_x': float('inf'), 'max_x': float('-inf'),\n", "                                 'min_y': float('inf'), 'max_y': float('-inf')},\n", "            'errors': []\n", "        }\n", "        \n", "        try:\n", "            # Try to read the file, use recovery if needed\n", "            try:\n", "                doc = ezdxf.readfile(file_path)\n", "                print(f\"  Successfully loaded DXF file\")\n", "            except ezdxf.DXFStructureError:\n", "                print(f\"  DXF structure error, attempting recovery...\")\n", "                doc, auditor = recover.readfile(file_path)\n", "                if auditor.has_errors:\n", "                    file_stats['errors'].extend([str(error) for error in auditor.errors])\n", "                    print(f\"  Recovery completed with {len(auditor.errors)} errors\")\n", "                else:\n", "                    print(f\"  Recovery successful\")\n", "            \n", "            # Get basic file information\n", "            print(f\"  DXF version: {doc.dxfversion}\")\n", "            print(f\"  Number of layers: {len(doc.layers)}\")\n", "            print(f\"  Number of blocks: {len(doc.blocks)}\")\n", "            \n", "        except Exception as e:\n", "            error_msg = f\"Error loading {file_path.name}: {str(e)}\"\n", "            print(f\"  {error_msg}\")\n", "            file_stats['errors'].append(error_msg)\n", "            file_stats['processing_status'] = 'error'\n", "            all_file_stats.append(file_stats)\n", "            processing_summary['failed_files'] += 1\n", "            continue\n", "    else:\n", "        print(f\"Skipping non-DXF file: {file_path.name} (DWG processing requires conversion)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Entity Extraction from Modelspace"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Processing 1065 entities in modelspace\n", "  Processed 1000 entities...\n", "  Completed modelspace processing: 1065 entities\n", "\n", "Modelspace extraction completed successfully\n"]}], "source": ["# Continue processing the loaded DXF file\n", "if 'doc' in locals():\n", "    # Process modelspace entities\n", "    msp = doc.modelspace()\n", "    print(f\"\\nProcessing {len(msp)} entities in modelspace\")\n", "    \n", "    entities_data = []\n", "    \n", "    for i, entity in enumerate(msp):\n", "        if i % 1000 == 0 and i > 0:\n", "            print(f\"  Processed {i} entities...\")\n", "        \n", "        entity_data = extract_entity_metadata(entity)\n", "        entity_data['source_file'] = file_path.name\n", "        entity_data['source_space'] = 'modelspace'\n", "        entities_data.append(entity_data)\n", "        \n", "        # Update statistics\n", "        file_stats['entity_counts'][entity_data.get('entity_type', 'unknown')] += 1\n", "        file_stats['classification_counts'][entity_data.get('classification', 'unknown')] += 1\n", "        file_stats['layer_counts'][entity_data.get('layer_name', 'unknown')] += 1\n", "        \n", "        # Update coordinate bounds\n", "        x_coord = entity_data.get('x_coord')\n", "        y_coord = entity_data.get('y_coord')\n", "        if x_coord is not None and y_coord is not None:\n", "            file_stats['coordinate_bounds']['min_x'] = min(file_stats['coordinate_bounds']['min_x'], x_coord)\n", "            file_stats['coordinate_bounds']['max_x'] = max(file_stats['coordinate_bounds']['max_x'], x_coord)\n", "            file_stats['coordinate_bounds']['min_y'] = min(file_stats['coordinate_bounds']['min_y'], y_coord)\n", "            file_stats['coordinate_bounds']['max_y'] = max(file_stats['coordinate_bounds']['max_y'], y_coord)\n", "    \n", "    print(f\"  Completed modelspace processing: {len(entities_data)} entities\")\n", "    \n", "    # Add to overall collection\n", "    all_entities.extend(entities_data)\n", "    \n", "    # Update processing summary\n", "    processing_summary['total_entities'] += len(entities_data)\n", "    for classification, count in file_stats['classification_counts'].items():\n", "        processing_summary['classification_summary'][classification] += count\n", "    for entity_type, count in file_stats['entity_counts'].items():\n", "        processing_summary['entity_type_summary'][entity_type] += count\n", "    \n", "    file_stats['total_entities'] = len(entities_data)\n", "    file_stats['processing_status'] = 'success'\n", "    all_file_stats.append(file_stats)\n", "    processing_summary['successful_files'] += 1\n", "    \n", "    print(f\"\\nModelspace extraction completed successfully\")\n", "else:\n", "    print(\"No DXF file loaded for processing\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Block Definition Processing"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Processing 34 block definitions\n", "  Processing block: omega pile sez 1-2\n", "    22 entities in block omega pile sez 1-2\n", "  Processing block: pannello pv alto\n", "    1 entities in block pannello pv alto\n", "  Processing block: TRK56_edge\n", "    206 entities in block TRK56_edge\n", "  Processing block: 1P52@55DEG INTERNE\n", "    1 entities in block 1P52@55DEG INTERNE\n", "  Processing block: TRK52_internal\n", "    249 entities in block TRK52_internal\n", "  Processing block: 1P52@55DEG ESTERNE\n", "    1 entities in block 1P52@55DEG ESTERNE\n", "  Processing block: TRJ52_external\n", "    247 entities in block TRJ52_external\n", "  Processing block: 1P26@55DEG ESTERNE\n", "    2 entities in block 1P26@55DEG ESTERNE\n", "  Processing block: TRK26_external\n", "    135 entities in block TRK26_external\n", "  Processing block: 1P26@55DEG INTERNE\n", "    2 entities in block 1P26@55DEG INTERNE\n", "  Processing block: TRK26_internal\n", "    136 entities in block TRK26_internal\n", "  Processing block: 1P52@55DEG EDGE INF\n", "    2 entities in block 1P52@55DEG EDGE INF\n", "  Processing block: 1P52@55DEG EDGE SUP\n", "    1 entities in block 1P52@55DEG EDGE SUP\n", "  Processing block: Scorrevole\n", "    46 entities in block Scorrevole\n", "  Processing block: ingresso\n", "    2 entities in block ingresso\n", "  Processing block: FRAME_ISO_A1\n", "    144 entities in block FRAME_ISO_A1\n", "  Processing block: A$C4B946721\n", "    93 entities in block A$C4B946721\n", "  Processing block: logo\n", "    487 entities in block logo\n", "  Processing block: title block hybrid - cart. A0_A1_A2_new\n", "    97 entities in block title block hybrid - cart. A0_A1_A2_new\n", "  Processing block: rev.0_hybrid_new logo\n", "    26 entities in block rev.0_hybrid_new logo\n", "  Processing block: rev.X_hybrid_new logo\n", "    18 entities in block rev.X_hybrid_new logo\n", "  Processing block: MdC_layout_25.06.2024_EGP_commented\n", "    217 entities in block MdC_layout_25.06.2024_EGP_commented\n", "  Processing block: MdC_layout_25.06.2024_EGP_commented1\n", "    59 entities in block MdC_layout_25.06.2024_EGP_commented1\n", "  Processing block: MdC_layout_25.06.2024_EGP_commented2\n", "    158 entities in block MdC_layout_25.06.2024_EGP_commented2\n", "  Processing block: MdC_layout_25.06.2024_EGP_commented3\n", "    285 entities in block MdC_layout_25.06.2024_EGP_commented3\n", "  Processing block: MdC_layout_25.06.2024_EGP_commented4\n", "    291 entities in block MdC_layout_25.06.2024_EGP_commented4\n", "  Processing block: MdC_layout_25.06.2024_EGP_commented5\n", "    291 entities in block MdC_layout_25.06.2024_EGP_commented5\n", "  Processing block: _DotSmall\n", "    1 entities in block _DotSmall\n", "  Processing block: cartiglio\n", "    101 entities in block cartiglio\n", "  Processing block: A$C14df579f\n", "    1 entities in block A$C14df579f\n", "\n", "Block processing completed: 3322 additional entities\n"]}], "source": ["# Process block definitions if DXF file was loaded\n", "if 'doc' in locals():\n", "    print(f\"\\nProcessing {len(doc.blocks)} block definitions\")\n", "    \n", "    block_entities_count = 0\n", "    \n", "    for block in doc.blocks:\n", "        if block.name.startswith('*'):  # Skip anonymous blocks\n", "            continue\n", "        \n", "        print(f\"  Processing block: {block.name}\")\n", "        block_entity_count = 0\n", "        \n", "        for entity in block:\n", "            entity_data = extract_entity_metadata(entity)\n", "            entity_data['source_file'] = file_path.name\n", "            entity_data['source_space'] = f'block:{block.name}'\n", "            entities_data.append(entity_data)\n", "            \n", "            # Update statistics\n", "            file_stats['entity_counts'][entity_data.get('entity_type', 'unknown')] += 1\n", "            file_stats['classification_counts'][entity_data.get('classification', 'unknown')] += 1\n", "            \n", "            block_entity_count += 1\n", "            block_entities_count += 1\n", "        \n", "        if block_entity_count > 0:\n", "            print(f\"    {block_entity_count} entities in block {block.name}\")\n", "    \n", "    print(f\"\\nBlock processing completed: {block_entities_count} additional entities\")\n", "    \n", "    # Update totals\n", "    all_entities.extend(entities_data[len(all_entities):])\n", "    processing_summary['total_entities'] = len(all_entities)\n", "    \n", "    # Update classification and entity type summaries\n", "    for classification, count in file_stats['classification_counts'].items():\n", "        processing_summary['classification_summary'][classification] = count\n", "    for entity_type, count in file_stats['entity_counts'].items():\n", "        processing_summary['entity_type_summary'][entity_type] = count\n", "else:\n", "    print(\"No DXF file loaded for block processing\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initial Data Analysis and Summary"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing Summary\n", "==================================================\n", "Total files processed: 2\n", "Successful: 1\n", "Failed: 0\n", "Total entities extracted: 4387\n", "\n", "Entity Type Distribution:\n", "  ACAD_PROXY_ENTITY: 1 (0.0%)\n", "  ACAD_TABLE: 1 (0.0%)\n", "  ARC: 305 (7.0%)\n", "  ATTDEF: 43 (1.0%)\n", "  CIRCLE: 28 (0.6%)\n", "  HATCH: 556 (12.7%)\n", "  IMAGE: 1 (0.0%)\n", "  INSERT: 706 (16.1%)\n", "  LINE: 1202 (27.4%)\n", "  LWPOLYLINE: 415 (9.5%)\n", "  MTEXT: 932 (21.2%)\n", "  OLE2FRAME: 5 (0.1%)\n", "  PDFREFERENCE: 1 (0.0%)\n", "  POLYLINE: 32 (0.7%)\n", "  SOLID: 22 (0.5%)\n", "  TEXT: 124 (2.8%)\n", "  TRACE: 12 (0.3%)\n", "  VIEWPORT: 1 (0.0%)\n", "\n", "Classification Distribution:\n", "  annotation: 948 (21.6%)\n", "  building: 614 (14.0%)\n", "  electrical: 35 (0.8%)\n", "  foundation: 310 (7.1%)\n", "  panel: 161 (3.7%)\n", "  pile: 1417 (32.3%)\n", "  road: 815 (18.6%)\n", "  unknown: 87 (2.0%)\n"]}], "source": ["# Display processing summary\n", "print(\"Processing Summary\")\n", "print(\"=\" * 50)\n", "print(f\"Total files processed: {processing_summary['total_files']}\")\n", "print(f\"Successful: {processing_summary['successful_files']}\")\n", "print(f\"Failed: {processing_summary['failed_files']}\")\n", "print(f\"Total entities extracted: {processing_summary['total_entities']}\")\n", "\n", "# Log metrics to MLflow\n", "mlflow.log_metric(\"files_processed\", processing_summary['total_files'])\n", "mlflow.log_metric(\"files_successful\", processing_summary['successful_files'])\n", "mlflow.log_metric(\"files_failed\", processing_summary['failed_files'])\n", "mlflow.log_metric(\"total_entities\", processing_summary['total_entities'])\n", "\n", "if processing_summary['total_entities'] > 0:\n", "    print(\"\\nEntity Type Distribution:\")\n", "    for entity_type, count in sorted(processing_summary['entity_type_summary'].items()):\n", "        percentage = (count / processing_summary['total_entities']) * 100\n", "        print(f\"  {entity_type}: {count} ({percentage:.1f}%)\")\n", "    \n", "    print(\"\\nClassification Distribution:\")\n", "    for classification, count in sorted(processing_summary['classification_summary'].items()):\n", "        percentage = (count / processing_summary['total_entities']) * 100\n", "        print(f\"  {classification}: {count} ({percentage:.1f}%)\")\n", "        \n", "        # Log classification metrics\n", "        mlflow.log_metric(f\"entities_{classification}\", count)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Convert to DataFrame for Analysis"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Creating DataFrame for analysis\n", "DataFrame created with 4387 rows and 32 columns\n", "Columns: ['entity_id', 'entity_type', 'layer_name', 'color', 'linetype', 'lineweight', 'x_coord', 'y_coord', 'z_coord', 'geometry_type', 'classification', 'extraction_timestamp', 'source_file', 'source_space', 'point_count', 'is_closed', 'text_content', 'text_height', 'text_rotation', 'start_x', 'start_y', 'start_z', 'end_x', 'end_y', 'end_z', 'length', 'radius', 'block_name', 'rotation', 'scale_x', 'scale_y', 'scale_z']\n", "\n", "DataFrame Info:\n", "  Shape: (4387, 32)\n", "  Memory usage: 3.72 MB\n", "  Entities with missing coordinates: 0\n", "\n", "Sample of extracted data:\n", "  entity_type classification                          layer_name        x_coord       y_coord geometry_type\n", "0       HATCH       building  SCS_Fabbricato - Manufatto Stimato       0.000000  0.000000e+00         other\n", "1  LWPOLYLINE       building  SCS_Fabbricato - Manufatto Stimato  708005.967783  4.692933e+06      polyline\n", "2  LWPOLYLINE       building  SCS_Fabbricato - Manufatto Stimato  707956.208724  4.692830e+06      polyline\n", "3       HATCH           road                          SCS_STRADE       0.000000  0.000000e+00         other\n", "4       MTEXT     annotation                            SCS_TEXT  707711.675604  4.693127e+06          text\n", "5       MTEXT     annotation                            SCS_TEXT  707710.368501  4.693110e+06          text\n", "6         ARC           road                              Strade       0.000000  0.000000e+00         other\n", "7  LWPOLYLINE     foundation                            Pozzetto  707715.305103  4.693035e+06      polyline\n", "8  LWPOLYLINE     foundation                            Pozzetto  708047.669129  4.692950e+06      polyline\n", "9  LWPOLYLINE     foundation                            Pozzetto  708037.136280  4.692928e+06      polyline\n"]}], "source": ["# Create comprehensive DataFrame if entities were extracted\n", "if all_entities:\n", "    print(\"\\nCreating DataFrame for analysis\")\n", "    \n", "    # Convert to DataFrame\n", "    entities_df = pd.DataFrame(all_entities)\n", "    \n", "    print(f\"DataFrame created with {len(entities_df)} rows and {len(entities_df.columns)} columns\")\n", "    print(f\"Columns: {list(entities_df.columns)}\")\n", "    \n", "    # Display basic statistics\n", "    print(\"\\nDataFrame Info:\")\n", "    print(f\"  Shape: {entities_df.shape}\")\n", "    print(f\"  Memory usage: {entities_df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB\")\n", "    \n", "    # Check for missing coordinates\n", "    coords_missing = entities_df[['x_coord', 'y_coord']].isnull().any(axis=1).sum()\n", "    print(f\"  Entities with missing coordinates: {coords_missing}\")\n", "    \n", "    # Display sample data\n", "    print(\"\\nSample of extracted data:\")\n", "    display_columns = ['entity_type', 'classification', 'layer_name', 'x_coord', 'y_coord', 'geometry_type']\n", "    available_columns = [col for col in display_columns if col in entities_df.columns]\n", "    print(entities_df[available_columns].head(10).to_string())\n", "    \n", "else:\n", "    print(\"\\nNo entities extracted - cannot create DataFrame\")\n", "    entities_df = pd.DataFrame()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Coordinate Analysis and Validation"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Coordinate Analysis\n", "==================================================\n", "Coordinate bounds:\n", "  X range: -841.00 to 1317413.28 (span: 1318254.28 m)\n", "  Y range: -106.00 to 5748421.94 (span: 5748527.94 m)\n", "  Coordinate system: EPSG:32633\n", "\n", "UTM Zone 33N validation:\n", "  X coordinates in valid range: No\n", "  Y coordinates in valid range: No\n", "\n", "Coordinate completeness: 100.0% (4387/4387)\n"]}], "source": ["# Analyze coordinates if data is available\n", "if not entities_df.empty and 'x_coord' in entities_df.columns:\n", "    print(\"\\nCoordinate Analysis\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Filter entities with valid coordinates\n", "    entities_with_coords = entities_df.dropna(subset=['x_coord', 'y_coord'])\n", "    \n", "    if not entities_with_coords.empty:\n", "        min_x = entities_with_coords['x_coord'].min()\n", "        max_x = entities_with_coords['x_coord'].max()\n", "        min_y = entities_with_coords['y_coord'].min()\n", "        max_y = entities_with_coords['y_coord'].max()\n", "        \n", "        print(f\"Coordinate bounds:\")\n", "        print(f\"  X range: {min_x:.2f} to {max_x:.2f} (span: {max_x - min_x:.2f} m)\")\n", "        print(f\"  Y range: {min_y:.2f} to {max_y:.2f} (span: {max_y - min_y:.2f} m)\")\n", "        print(f\"  Coordinate system: {coordinate_system}\")\n", "        \n", "        # Check if coordinates are in expected UTM range for Italy\n", "        utm_33n_x_min, utm_33n_x_max = 166021, 833978\n", "        utm_33n_y_min, utm_33n_y_max = 0, 9329005\n", "        \n", "        x_in_range = (min_x >= utm_33n_x_min) and (max_x <= utm_33n_x_max)\n", "        y_in_range = (min_y >= utm_33n_y_min) and (max_y <= utm_33n_y_max)\n", "        \n", "        print(f\"\\nUTM Zone 33N validation:\")\n", "        print(f\"  X coordinates in valid range: {'Yes' if x_in_range else 'No'}\")\n", "        print(f\"  Y coordinates in valid range: {'Yes' if y_in_range else 'No'}\")\n", "        \n", "        # Log coordinate bounds to MLflow\n", "        mlflow.log_metric(\"coord_min_x\", min_x)\n", "        mlflow.log_metric(\"coord_max_x\", max_x)\n", "        mlflow.log_metric(\"coord_min_y\", min_y)\n", "        mlflow.log_metric(\"coord_max_y\", max_y)\n", "        mlflow.log_metric(\"coord_span_x\", max_x - min_x)\n", "        mlflow.log_metric(\"coord_span_y\", max_y - min_y)\n", "        \n", "        # Calculate coordinate completeness\n", "        coord_completeness = len(entities_with_coords) / len(entities_df)\n", "        print(f\"\\nCoordinate completeness: {coord_completeness:.1%} ({len(entities_with_coords)}/{len(entities_df)})\")\n", "        mlflow.log_metric(\"coordinate_completeness_pct\", coord_completeness * 100)\n", "    \n", "    else:\n", "        print(\"No entities with valid coordinates found\")\n", "else:\n", "    print(\"No coordinate data available for analysis\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate Structured Output Files"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Generating Structured Output\n", "==================================================\n", "Main entities CSV saved: motali_de_castro_main_site_cad_entities.csv\n", "Building entities CSV saved: motali_de_castro_main_site_cad_building.csv (614 entities)\n", "Road entities CSV saved: motali_de_castro_main_site_cad_road.csv (815 entities)\n", "Annotation entities CSV saved: motali_de_castro_main_site_cad_annotation.csv (948 entities)\n", "Foundation entities CSV saved: motali_de_castro_main_site_cad_foundation.csv (310 entities)\n", "Panel entities CSV saved: motali_de_castro_main_site_cad_panel.csv (161 entities)\n", "Electrical entities CSV saved: motali_de_castro_main_site_cad_electrical.csv (35 entities)\n", "Pile entities CSV saved: motali_de_castro_main_site_cad_pile.csv (1417 entities)\n", "File statistics CSV saved: motali_de_castro_main_site_cad_file_stats.csv\n", "Processing summary JSON saved: motali_de_castro_main_site_cad_summary.json\n", "\n", "All output files logged to MLflow\n"]}], "source": ["# Generate structured output if entities were extracted\n", "if not entities_df.empty:\n", "    print(\"\\nGenerating Structured Output\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Save main entities CSV\n", "    entities_csv_path = run_output_dir / f\"{project_type}_{site_name}_cad_entities.csv\"\n", "    entities_df.to_csv(entities_csv_path, index=False)\n", "    print(f\"Main entities CSV saved: {entities_csv_path.name}\")\n", "    \n", "    # Save classification-specific CSVs\n", "    classification_files = {}\n", "    for classification in processing_summary['classification_summary'].keys():\n", "        if classification != 'unknown':\n", "            classified_df = entities_df[entities_df['classification'] == classification]\n", "            if not classified_df.empty:\n", "                classified_csv_path = run_output_dir / f\"{project_type}_{site_name}_cad_{classification}.csv\"\n", "                classified_df.to_csv(classified_csv_path, index=False)\n", "                classification_files[classification] = str(classified_csv_path)\n", "                print(f\"{classification.title()} entities CSV saved: {classified_csv_path.name} ({len(classified_df)} entities)\")\n", "    \n", "    # Save file statistics\n", "    if all_file_stats:\n", "        file_stats_df = pd.DataFrame(all_file_stats)\n", "        file_stats_csv_path = run_output_dir / f\"{project_type}_{site_name}_cad_file_stats.csv\"\n", "        file_stats_df.to_csv(file_stats_csv_path, index=False)\n", "        print(f\"File statistics CSV saved: {file_stats_csv_path.name}\")\n", "    \n", "    # Save processing summary as JSON\n", "    summary_json_path = run_output_dir / f\"{project_type}_{site_name}_cad_summary.json\"\n", "    summary_for_json = {\n", "        'processing_summary': dict(processing_summary),\n", "        'classification_summary': dict(processing_summary['classification_summary']),\n", "        'entity_type_summary': dict(processing_summary['entity_type_summary']),\n", "        'coordinate_system': coordinate_system,\n", "        'extraction_timestamp': datetime.now().isoformat()\n", "    }\n", "    \n", "    with open(summary_json_path, 'w') as f:\n", "        json.dump(summary_for_json, f, indent=2)\n", "    print(f\"Processing summary JSON saved: {summary_json_path.name}\")\n", "    \n", "    # Log artifacts to MLflow\n", "    mlflow.log_artifact(str(entities_csv_path))\n", "    mlflow.log_artifact(str(summary_json_path))\n", "    if 'file_stats_csv_path' in locals():\n", "        mlflow.log_artifact(str(file_stats_csv_path))\n", "    \n", "    for classification, file_path in classification_files.items():\n", "        mlflow.log_artifact(file_path)\n", "    \n", "    print(f\"\\nAll output files logged to MLflow\")\n", "    \n", "else:\n", "    print(\"\\nNo entities extracted - skipping output generation\")\n", "    mlflow.log_metric(\"extraction_success\", 0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Schema Conversion Functions\n", "\n", "Functions to convert extracted data to standardized metadata schema format."]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Schema conversion helper functions defined\n"]}], "source": ["def calculate_confidence_score(row):\n", "    \"\"\"Calculate confidence score based on data completeness and classification.\"\"\"\n", "    score = 0.0\n", "    \n", "    # Base score for having coordinates\n", "    if pd.notna(row.get('x_coord')) and pd.notna(row.get('y_coord')):\n", "        score += 0.3\n", "    \n", "    # Score for classification\n", "    if row.get('classification', 'unknown') != 'unknown':\n", "        score += 0.3\n", "    \n", "    # Score for having geometric properties\n", "    if row.get('geometry_type', '') in ['circle', 'line', 'polyline', 'insert']:\n", "        score += 0.2\n", "    \n", "    # Score for having layer information\n", "    if row.get('layer_name', ''):\n", "        score += 0.1\n", "    \n", "    # Score for having additional properties\n", "    if pd.notna(row.get('radius')) or pd.notna(row.get('length')) or pd.notna(row.get('text_content')):\n", "        score += 0.1\n", "    \n", "    return min(score, 1.0)\n", "\n", "def map_classification_to_element_type(classification):\n", "    \"\"\"Map CAD classification to standard element types.\"\"\"\n", "    mapping = {\n", "        'pile': 'pile',\n", "        'foundation': 'foundation',\n", "        'panel': 'solar_panel',\n", "        'building': 'building',\n", "        'road': 'infrastructure',\n", "        'trench': 'infrastructure',\n", "        'electrical': 'electrical',\n", "        'annotation': 'annotation',\n", "        'unknown': 'other'\n", "    }\n", "    return mapping.get(classification.lower(), 'other')\n", "\n", "print(\"Schema conversion helper functions defined\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate Alignment and Labeling Datasets"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Generating Schema-Compliant Datasets\n", "==================================================\n", "Alignment dataset saved: motali_de_castro_main_site_alignment_points.json\n", "  - Pile points: 1417\n", "  - Foundation points: 310\n"]}], "source": ["# Generate schema-compliant outputs if entities were extracted\n", "if not entities_df.empty:\n", "    print(\"\\nGenerating Schema-Compliant Datasets\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Create alignment-ready dataset\n", "    pile_entities = entities_df[entities_df['classification'] == 'pile']\n", "    foundation_entities = entities_df[entities_df['classification'] == 'foundation']\n", "    \n", "    alignment_data = {\n", "        'metadata': {\n", "            'dataset_type': 'cad_alignment_points',\n", "            'coordinate_system': coordinate_system,\n", "            'creation_timestamp': datetime.now().isoformat(),\n", "            'total_points': len(pile_entities) + len(foundation_entities)\n", "        },\n", "        'pile_points': [],\n", "        'foundation_points': [],\n", "        'coordinate_bounds': {\n", "            'min_x': None,\n", "            'max_x': None,\n", "            'min_y': None,\n", "            'max_y': None\n", "        }\n", "    }\n", "    \n", "    # Process pile entities\n", "    for idx, row in pile_entities.iterrows():\n", "        if pd.notna(row.get('x_coord')) and pd.notna(row.get('y_coord')):\n", "            pile_point = {\n", "                'id': str(row.get('entity_id', f'pile_{idx}')),\n", "                'x': float(row['x_coord']),\n", "                'y': float(row['y_coord']),\n", "                'z': float(row.get('z_coord', 0.0)),\n", "                'type': 'pile',\n", "                'subtype': row.get('block_name', 'unknown'),\n", "                'layer': row.get('layer_name', ''),\n", "                'confidence': calculate_confidence_score(row)\n", "            }\n", "            alignment_data['pile_points'].append(pile_point)\n", "    \n", "    # Process foundation entities\n", "    for idx, row in foundation_entities.iterrows():\n", "        if pd.notna(row.get('x_coord')) and pd.notna(row.get('y_coord')):\n", "            foundation_point = {\n", "                'id': str(row.get('entity_id', f'foundation_{idx}')),\n", "                'x': float(row['x_coord']),\n", "                'y': float(row['y_coord']),\n", "                'z': float(row.get('z_coord', 0.0)),\n", "                'type': 'foundation',\n", "                'subtype': row.get('block_name', 'unknown'),\n", "                'layer': row.get('layer_name', ''),\n", "                'confidence': calculate_confidence_score(row)\n", "            }\n", "            alignment_data['foundation_points'].append(foundation_point)\n", "    \n", "    # Calculate coordinate bounds\n", "    all_points = alignment_data['pile_points'] + alignment_data['foundation_points']\n", "    if all_points:\n", "        x_coords = [p['x'] for p in all_points]\n", "        y_coords = [p['y'] for p in all_points]\n", "        alignment_data['coordinate_bounds'] = {\n", "            'min_x': min(x_coords),\n", "            'max_x': max(x_coords),\n", "            'min_y': min(y_coords),\n", "            'max_y': max(y_coords)\n", "        }\n", "    \n", "    # Save alignment dataset\n", "    alignment_path = run_output_dir / f\"{project_type}_{site_name}_alignment_points.json\"\n", "    with open(alignment_path, 'w') as f:\n", "        json.dump(alignment_data, f, indent=2, default=str)\n", "    print(f\"Alignment dataset saved: {alignment_path.name}\")\n", "    print(f\"  - Pile points: {len(alignment_data['pile_points'])}\")\n", "    print(f\"  - Foundation points: {len(alignment_data['foundation_points'])}\")\n", "    \n", "    # Log alignment metrics\n", "    mlflow.log_metric(\"alignment_points_total\", len(all_points))\n", "    mlflow.log_metric(\"alignment_pile_points\", len(alignment_data['pile_points']))\n", "    mlflow.log_metric(\"alignment_foundation_points\", len(alignment_data['foundation_points']))\n", "    mlflow.log_artifact(str(alignment_path))\n", "    \n", "else:\n", "    print(\"\\nNo entities available for schema conversion\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create Labeling Dataset"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Creating Labeling Dataset\n", "==============================\n", "  building: 614 entities\n", "  road: 815 entities\n", "  annotation: 948 entities\n", "  foundation: 310 entities\n", "  panel: 161 entities\n", "  electrical: 35 entities\n", "  pile: 1417 entities\n", "\n", "Labeling dataset saved: motali_de_castro_main_site_labeling_data.json\n", "  - Text annotations: 1056\n", "  - Classified entity types: 7\n"]}], "source": ["# Create labeling dataset if entities were extracted\n", "if not entities_df.empty:\n", "    print(\"\\nCreating Labeling Dataset\")\n", "    print(\"=\" * 30)\n", "    \n", "    labeling_data = {\n", "        'metadata': {\n", "            'dataset_type': 'cad_labeling_data',\n", "            'coordinate_system': coordinate_system,\n", "            'creation_timestamp': datetime.now().isoformat(),\n", "            'total_entities': len(entities_df)\n", "        },\n", "        'classified_entities': {},\n", "        'text_annotations': [],\n", "        'classification_summary': {}\n", "    }\n", "    \n", "    # Group entities by classification\n", "    for classification in entities_df['classification'].unique():\n", "        if classification != 'unknown':\n", "            classified_entities = entities_df[entities_df['classification'] == classification]\n", "            \n", "            entity_list = []\n", "            for idx, row in classified_entities.iterrows():\n", "                if pd.notna(row.get('x_coord')) and pd.notna(row.get('y_coord')):\n", "                    entity = {\n", "                        'id': str(row.get('entity_id', f'{classification}_{idx}')),\n", "                        'x': float(row['x_coord']),\n", "                        'y': float(row['y_coord']),\n", "                        'z': float(row.get('z_coord', 0.0)),\n", "                        'entity_type': row.get('entity_type', ''),\n", "                        'layer': row.get('layer_name', ''),\n", "                        'confidence': calculate_confidence_score(row)\n", "                    }\n", "                    entity_list.append(entity)\n", "            \n", "            labeling_data['classified_entities'][classification] = entity_list\n", "            labeling_data['classification_summary'][classification] = len(entity_list)\n", "            print(f\"  {classification}: {len(entity_list)} entities\")\n", "    \n", "    # Extract text annotations\n", "    text_entities = entities_df[entities_df['geometry_type'] == 'text']\n", "    for idx, row in text_entities.iterrows():\n", "        if pd.notna(row.get('text_content')) and pd.notna(row.get('x_coord')):\n", "            annotation = {\n", "                'id': str(row.get('entity_id', f'text_{idx}')),\n", "                'x': float(row['x_coord']),\n", "                'y': float(row['y_coord']),\n", "                'z': float(row.get('z_coord', 0.0)),\n", "                'text': str(row['text_content']),\n", "                'height': float(row.get('text_height', 0)) if pd.notna(row.get('text_height')) else None,\n", "                'rotation': float(row.get('text_rotation', 0)) if pd.notna(row.get('text_rotation')) else None,\n", "                'layer': row.get('layer_name', ''),\n", "                'classification': row.get('classification', 'annotation')\n", "            }\n", "            labeling_data['text_annotations'].append(annotation)\n", "    \n", "    # Save labeling dataset\n", "    labeling_path = run_output_dir / f\"{project_type}_{site_name}_labeling_data.json\"\n", "    with open(labeling_path, 'w') as f:\n", "        json.dump(labeling_data, f, indent=2, default=str)\n", "    print(f\"\\nLabeling dataset saved: {labeling_path.name}\")\n", "    print(f\"  - Text annotations: {len(labeling_data['text_annotations'])}\")\n", "    print(f\"  - Classified entity types: {len(labeling_data['classified_entities'])}\")\n", "    \n", "    # Log labeling metrics\n", "    mlflow.log_metric(\"text_annotations\", len(labeling_data['text_annotations']))\n", "    mlflow.log_artifact(str(labeling_path))\n", "    \n", "else:\n", "    print(\"\\nNo entities available for labeling dataset creation\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Final Analysis and Summary"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Final Analysis and Summary\n", "============================================================\n", "Classification success rate: 98.0% (4300/4387)\n", "Coordinate completeness: 100.0% (4387/4387)\n", "\n", "Layer Analysis:\n", "  0: 1088 entities\n", "  CVT - PILE LATERAL: 330 entities\n", "  CVT_Tracker 1x52 int: 248 entities\n", "  CVT - PILE DRIVE: 240 entities\n", "  CVT - Pile END: 178 entities\n", "  SCS_Legenda: 165 entities\n", "  PDF6_Text: 160 entities\n", "  PDF4_Text: 160 entities\n", "  PDF5_Text: 160 entities\n", "  PDF_Text: 158 entities\n", "\n", "Readiness for Downstream Workflows:\n", "  Alignment points available: 1727\n", "    - Pile points: 1417\n", "    - Foundation points: 310\n", "    Status: Ready for point cloud alignment\n", "  Text annotations available: 1056\n", "  Classified entity categories: 7\n", "    Status: Ready for automated labeling workflows\n", "\n", "Output Directory: ../../../output_runs/cad_metadata/motali_de_castro_main_site_20250630_174129\n", "Coordinate System: EPSG:32633\n", "Processing Timestamp: 20250630_174129\n"]}], "source": ["# Display final analysis if entities were extracted\n", "if not entities_df.empty:\n", "    print(\"\\nFinal Analysis and Summary\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Classification quality metrics\n", "    classified_entities = len(entities_df[entities_df['classification'] != 'unknown'])\n", "    classification_rate = (classified_entities / len(entities_df)) * 100\n", "    print(f\"Classification success rate: {classification_rate:.1f}% ({classified_entities}/{len(entities_df)})\")\n", "    \n", "    # Coordinate quality metrics\n", "    entities_with_coords = entities_df.dropna(subset=['x_coord', 'y_coord'])\n", "    coord_completeness = (len(entities_with_coords) / len(entities_df)) * 100\n", "    print(f\"Coordinate completeness: {coord_completeness:.1f}% ({len(entities_with_coords)}/{len(entities_df)})\")\n", "    \n", "    # Layer analysis\n", "    print(f\"\\nLayer Analysis:\")\n", "    layer_counts = entities_df['layer_name'].value_counts().head(10)\n", "    for layer, count in layer_counts.items():\n", "        print(f\"  {layer}: {count} entities\")\n", "    \n", "    # Entities for alignment and labeling\n", "    print(f\"\\nReadiness for Downstream Workflows:\")\n", "    \n", "    if 'alignment_data' in locals():\n", "        total_alignment_points = len(alignment_data['pile_points']) + len(alignment_data['foundation_points'])\n", "        print(f\"  Alignment points available: {total_alignment_points}\")\n", "        print(f\"    - Pile points: {len(alignment_data['pile_points'])}\")\n", "        print(f\"    - Foundation points: {len(alignment_data['foundation_points'])}\")\n", "        \n", "        if total_alignment_points >= 4:\n", "            print(f\"    Status: Ready for point cloud alignment\")\n", "        else:\n", "            print(f\"    Status: Insufficient points for reliable alignment\")\n", "    \n", "    if 'labeling_data' in locals():\n", "        print(f\"  Text annotations available: {len(labeling_data['text_annotations'])}\")\n", "        print(f\"  Classified entity categories: {len(labeling_data['classified_entities'])}\")\n", "        print(f\"    Status: Ready for automated labeling workflows\")\n", "    \n", "    # Log final quality metrics\n", "    mlflow.log_metric(\"classification_success_pct\", classification_rate)\n", "    mlflow.log_metric(\"coordinate_completeness_pct\", coord_completeness)\n", "    \n", "    print(f\"\\nOutput Directory: {run_output_dir}\")\n", "    print(f\"Coordinate System: {coordinate_system}\")\n", "    print(f\"Processing Timestamp: {timestamp}\")\n", "    \n", "else:\n", "    print(\"\\nNo entities extracted - check input files and processing errors\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Completion and Next Steps"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "EXPLORATORY CAD METADATA EXTRACTION COMPLETE\n", "============================================================\n", "\n", "Successfully processed 1/2 files\n", "Extracted 4387 entities with 98.0% classification rate\n", "Generated 1727 alignment points for point cloud registration\n", "Generated 1056 text annotations for labeling\n", "\n", "Output directory: ../../../output_runs/cad_metadata/motali_de_castro_main_site_20250630_174129\n", "Coordinate system: EPSG:32633\n", "\n", "Next Steps:\n", "1. Use alignment points for point cloud to CAD registration\n", "2. Apply classified entities for automated point cloud labeling\n", "3. Validate coordinate system consistency with survey data\n", "4. Integrate with existing preprocessing pipeline\n", "\n", "MLflow run completed and logged\n", "\n", "============================================================\n"]}], "source": ["# Final completion summary\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"EXPLORATORY CAD METADATA EXTRACTION COMPLETE\")\n", "print(\"=\" * 60)\n", "\n", "if processing_summary['total_entities'] > 0:\n", "    print(f\"\\nSuccessfully processed {processing_summary['successful_files']}/{processing_summary['total_files']} files\")\n", "    print(f\"Extracted {processing_summary['total_entities']} entities with {classification_rate:.1f}% classification rate\")\n", "    \n", "    if 'alignment_data' in locals():\n", "        total_alignment_points = len(alignment_data['pile_points']) + len(alignment_data['foundation_points'])\n", "        print(f\"Generated {total_alignment_points} alignment points for point cloud registration\")\n", "    \n", "    if 'labeling_data' in locals():\n", "        print(f\"Generated {len(labeling_data['text_annotations'])} text annotations for labeling\")\n", "    \n", "    print(f\"\\nOutput directory: {run_output_dir}\")\n", "    print(f\"Coordinate system: {coordinate_system}\")\n", "    \n", "    print(f\"\\nNext Steps:\")\n", "    print(\"1. Use alignment points for point cloud to CAD registration\")\n", "    print(\"2. Apply classified entities for automated point cloud labeling\")\n", "    print(\"3. Validate coordinate system consistency with survey data\")\n", "    print(\"4. Integrate with existing preprocessing pipeline\")\n", "    \n", "else:\n", "    print(f\"\\nNo entities extracted - check input files and processing errors\")\n", "    print(\"Review file statistics and error logs for troubleshooting\")\n", "\n", "# End MLflow run\n", "mlflow.end_run()\n", "print(f\"\\nMLflow run completed and logged\")\n", "\n", "print(\"\\n\" + \"=\" * 60)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}