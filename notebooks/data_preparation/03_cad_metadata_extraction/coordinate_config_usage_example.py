# Example: How to use coordinate system configuration in CAD extraction notebooks
# This shows how to load and use the configuration generated by 01_coordinate_system_analysis.ipynb

import json
from pathlib import Path

def load_site_coordinate_config(config_dir="../../../output_runs/coordinate_analysis"):
    """
    Load the most recent site coordinate configuration.
    """
    config_path = Path(config_dir)
    
    if not config_path.exists():
        print(f"❌ Configuration directory not found: {config_path}")
        print("   Please run 01_coordinate_system_analysis.ipynb first!")
        return None
    
    # Find the most recent config file
    config_files = list(config_path.glob("*/site_coordinate_config_*.json"))
    
    if not config_files:
        print(f"❌ No coordinate configuration found in {config_path}")
        print("   Please run 01_coordinate_system_analysis.ipynb first!")
        return None
    
    # Get the most recent config file
    latest_config = max(config_files, key=lambda x: x.stat().st_mtime)
    
    try:
        with open(latest_config, 'r') as f:
            config = json.load(f)
        
        print(f"✅ Loaded coordinate configuration: {latest_config.name}")
        return config
    
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return None

def create_coordinate_validator(config):
    """
    Create a coordinate validation function based on the site configuration.
    """
    if not config or not config.get('valid_ranges'):
        print("⚠️ No valid ranges in configuration - using basic validation")
        
        def basic_validator(x, y, z=None):
            return x != 0.0 and y != 0.0 and abs(x) > 100 and abs(y) > 100
        
        return basic_validator
    
    ranges = config['valid_ranges']
    coord_system = config.get('coordinate_system', {})
    
    print(f"🔧 Creating validator for {coord_system.get('type', 'Unknown')} coordinate system")
    print(f"   X range: {ranges['x_min']:.2f} to {ranges['x_max']:.2f}")
    print(f"   Y range: {ranges['y_min']:.2f} to {ranges['y_max']:.2f}")
    
    def adaptive_validator(x, y, z=None):
        """Validate coordinates using site-specific ranges."""
        # Basic checks
        if x == 0.0 and y == 0.0:
            return False
        if abs(x) < 100 or abs(y) < 100:
            return False
        
        # Site-specific range checks
        if not (ranges['x_min'] <= x <= ranges['x_max']):
            return False
        if not (ranges['y_min'] <= y <= ranges['y_max']):
            return False
        
        return True
    
    return adaptive_validator

# Usage example in CAD extraction notebook:
def example_usage_in_cad_extraction():
    """
    Example of how to integrate coordinate validation in CAD extraction.
    """
    print("📋 Example: Using coordinate configuration in CAD extraction")
    print("=" * 60)
    
    # Step 1: Load configuration
    config = load_site_coordinate_config()
    if not config:
        return
    
    # Step 2: Create validator
    is_valid_coordinate = create_coordinate_validator(config)
    
    # Step 3: Use in extraction (example)
    print("\n🔧 Example extraction with validation:")
    
    # Simulate some coordinates
    test_coordinates = [
        (0.0, 0.0, 0.0),           # Invalid: zero coordinates
        (707500, 4692800, 10),     # Valid: within Castro range
        (1000000, 5000000, 0),     # Invalid: outside range
        (707800, 4693000, 15)      # Valid: within Castro range
    ]
    
    valid_coords = []
    for x, y, z in test_coordinates:
        if is_valid_coordinate(x, y, z):
            valid_coords.append((x, y, z))
            print(f"   ✅ Valid: ({x}, {y}, {z})")
        else:
            print(f"   ❌ Invalid: ({x}, {y}, {z})")
    
    print(f"\n📊 Results: {len(valid_coords)} valid out of {len(test_coordinates)} total coordinates")
    
    # Step 4: Show configuration info
    print(f"\n📍 Site Configuration Summary:")
    print(f"   Coordinate System: {config['coordinate_system'].get('type', 'Unknown')}")
    print(f"   Confidence: {config['coordinate_system'].get('confidence', 'Unknown')}")
    print(f"   Files Analyzed: {len(config['analysis_info']['files_analyzed'])}")
    print(f"   Total Coordinates: {config['analysis_info']['total_coordinates_extracted']}")

# Integration template for CAD extraction notebooks:
INTEGRATION_TEMPLATE = '''
# Add this to your CAD extraction notebook after imports:

# Load coordinate system configuration
config = load_site_coordinate_config()
is_valid_coordinate = create_coordinate_validator(config)

# Use in your extraction function:
def extract_solar_data_with_validation(file_path):
    """Extract solar data with automatic coordinate validation."""
    # ... your existing extraction code ...
    
    # Replace coordinate filtering with:
    if is_valid_coordinate(point.x, point.y, point.z):
        # Add to results
        tracker_data = {...}
        trackers.append(tracker_data)
    
    # ... rest of extraction code ...
'''

if __name__ == "__main__":
    example_usage_in_cad_extraction()
    
    print(f"\n" + "=" * 60)
    print("INTEGRATION TEMPLATE:")
    print("=" * 60)
    print(INTEGRATION_TEMPLATE)
    
    print(f"\n🎯 Benefits of this approach:")
    print(f"   ✅ No hard-coding of coordinate ranges")
    print(f"   ✅ Automatic adaptation to any site")
    print(f"   ✅ Reusable configuration across notebooks")
    print(f"   ✅ Statistical validation based on actual data")
    print(f"   ✅ Multi-site compatibility")
