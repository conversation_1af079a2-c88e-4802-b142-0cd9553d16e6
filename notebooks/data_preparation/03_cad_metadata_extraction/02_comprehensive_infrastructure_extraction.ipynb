{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Comprehensive Infrastructure CAD Extraction\n", "\n", "Enhanced CAD metadata extraction that distinguishes between different pile types and extracts all infrastructure elements to match ground truth specifications.\n", "\n", "## Target Ground Truth Validation:\n", "- **Foundation Piles**: 4,199 (main structural piles)\n", "- **Module Support Points**: 23,764 (solar panel support)\n", "- **Trackers**: 543 (tracking systems)\n", "- **Infrastructure**: Roads, fencing, electrical, logistics\n", "\n", "## Enhanced Classification:\n", "- Distinguishes foundation piles from module support piles\n", "- Extracts roads, fencing, electrical infrastructure\n", "- Identifies transformers, boundaries, facilities\n", "- Validates against ground truth quantities"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Papermill parameters\n", "project_type = \"motali_de_castro\"\n", "site_name = \"comprehensive_extraction\"\n", "cad_data_path = \"../../../data/raw/motali_de_castro/cad\"\n", "output_dir = \"../../../output_runs/cad_metadata\"\n", "target_files = [\"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\"]\n", "coordinate_system = \"EPSG:32633\"  # UTM Zone 33N for Italy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "import ezdxf\n", "import json\n", "from datetime import datetime\n", "from collections import defaultdict\n", "import logging\n", "import mlflow\n", "import matplotlib.pyplot as plt\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "print(f\"Comprehensive Infrastructure CAD Extraction - {project_type.title()}\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(\"=\" * 60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load Ground Truth Specifications"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load ground truth for validation\n", "ground_truth_path = Path(\"../../../data/reference/montalto_di_castro_ground_truth.csv\")\n", "ground_truth_df = pd.read_csv(ground_truth_path)\n", "\n", "# Convert to dictionary for easy lookup\n", "GROUND_TRUTH = {}\n", "for _, row in ground_truth_df.iterrows():\n", "    key = row['Features'].lower().replace(' ', '_')\n", "    GROUND_TRUTH[key] = {\n", "        'planned': row['Planned'],\n", "        'unit': row['Unit'],\n", "        'category': row['Categories'],\n", "        'notes': row.get('Notes', '')\n", "    }\n", "\n", "print(f\"Ground Truth Specifications Loaded:\")\n", "print(f\"- Foundation Piles: {GROUND_TRUTH['piling']['planned']}\")\n", "print(f\"- Trackers: {GROUND_TRUTH['tracker']['planned']}\")\n", "print(f\"- Modules: {GROUND_TRUTH['modules']['planned']}\")\n", "print(f\"- Roads: {GROUND_TRUTH['road']['planned']} {GROUND_TRUTH['road']['unit']}\")\n", "print(f\"- Fencing: {GROUND_TRUTH['fence_chainlink']['planned']} {GROUND_TRUTH['fence_chainlink']['unit']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Enhanced Infrastructure Classification System"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class ComprehensiveInfrastructureClassifier:\n", "    \"\"\"Enhanced classifier for all infrastructure types.\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.classification_rules = {\n", "            # Foundation piles (structural)\n", "            'foundation_pile': {\n", "                'layers': ['CVT - PILE LATERAL', 'CVT - PILE DRIVE', 'CVT - <PERSON>le END'],\n", "                'entity_types': ['LINE'],\n", "                'keywords': ['pile', 'foundation', 'structural']\n", "            },\n", "            \n", "            # Trackers (solar tracking systems)\n", "            'tracker': {\n", "                'layers': ['CVT_Tracker 1x52 int', 'CVT_Tracker 1x26 int', \n", "                          'CVT_Tracker 1X52 Edge', 'CVT_Tracker 1x26 ext', 'CVT_Tracker 1x52 ext'],\n", "                'entity_types': ['INSERT'],\n", "                'keywords': ['tracker', 'TRJ']\n", "            },\n", "            \n", "            # Roads and access\n", "            'road': {\n", "                'layers': ['ROAD', 'ACCESS', 'STREET'],\n", "                'entity_types': ['POLYLINE', 'LINE'],\n", "                'keywords': ['road', 'access', 'street', 'path']\n", "            },\n", "            \n", "            # Fencing\n", "            'fencing': {\n", "                'layers': ['SCS_RECINZIONE', 'FENCE', 'BOUNDARY'],\n", "                'entity_types': ['POLYLINE', 'LINE'],\n", "                'keywords': ['fence', 'recinzione', 'boundary']\n", "            },\n", "            \n", "            # Electrical infrastructure\n", "            'electrical': {\n", "                'layers': ['ELECTRICAL', 'CABLE', 'POWER'],\n", "                'entity_types': ['POLYLINE', 'LINE'],\n", "                'keywords': ['electrical', 'cable', 'power', 'voltage']\n", "            },\n", "            \n", "            # Buildings and facilities\n", "            'building': {\n", "                'layers': ['BUILDING', 'FACILITY', 'CABIN'],\n", "                'entity_types': ['POLYLINE', 'INSERT'],\n", "                'keywords': ['building', 'cabin', 'facility', 'booth']\n", "            }\n", "        }\n", "    \n", "    def classify_entity(self, entity_data):\n", "        \"\"\"Classify entity based on comprehensive rules.\"\"\"\n", "        layer_name = entity_data.get('layer_name', '').upper()\n", "        entity_type = entity_data.get('entity_type', '')\n", "        text_content = str(entity_data.get('text_content', '')).lower()\n", "        \n", "        for classification, rules in self.classification_rules.items():\n", "            # Check layer match\n", "            if any(layer.upper() in layer_name for layer in rules['layers']):\n", "                return classification\n", "            \n", "            # Check entity type and keyword combination\n", "            if entity_type in rules['entity_types']:\n", "                if any(keyword in text_content for keyword in rules['keywords']):\n", "                    return classification\n", "        \n", "        return 'unknown'\n", "\n", "# Initialize classifier\n", "classifier = ComprehensiveInfrastructureClassifier()\n", "print(\"Comprehensive Infrastructure Classifier initialized\")\n", "print(f\"Classification categories: {list(classifier.classification_rules.keys())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. CAD File Processing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_entity_data(entity):\n", "    \"\"\"Extract comprehensive entity data.\"\"\"\n", "    try:\n", "        entity_type = entity.dxftype()\n", "        layer_name = getattr(entity.dxf, 'layer', 'unknown')\n", "        \n", "        # Base entity data\n", "        entity_data = {\n", "            'entity_id': str(entity.dxf.handle),\n", "            'entity_type': entity_type,\n", "            'layer_name': layer_name,\n", "            'color': getattr(entity.dxf, 'color', 256),\n", "            'linetype': getattr(entity.dxf, 'linetype', 'Continuous')\n", "        }\n", "        \n", "        # Extract coordinates based on entity type\n", "        if hasattr(entity.dxf, 'insert'):\n", "            # INSERT entities (trackers)\n", "            insert_point = entity.dxf.insert\n", "            entity_data.update({\n", "                'x_coord': insert_point.x,\n", "                'y_coord': insert_point.y,\n", "                'z_coord': insert_point.z,\n", "                'block_name': getattr(entity.dxf, 'name', ''),\n", "                'rotation': getattr(entity.dxf, 'rotation', 0.0)\n", "            })\n", "        <PERSON><PERSON> hasattr(entity.dxf, 'start') and hasattr(entity.dxf, 'end'):\n", "            # LINE entities (foundation piles)\n", "            start_point = entity.dxf.start\n", "            end_point = entity.dxf.end\n", "            entity_data.update({\n", "                'x_coord': (start_point.x + end_point.x) / 2,\n", "                'y_coord': (start_point.y + end_point.y) / 2,\n", "                'z_coord': (start_point.z + end_point.z) / 2,\n", "                'start_x': start_point.x,\n", "                'start_y': start_point.y,\n", "                'end_x': end_point.x,\n", "                'end_y': end_point.y,\n", "                'length': start_point.distance(end_point)\n", "            })\n", "        <PERSON><PERSON>(entity.dxf, 'center'):\n", "            # CIRCLE entities\n", "            center = entity.dxf.center\n", "            entity_data.update({\n", "                'x_coord': center.x,\n", "                'y_coord': center.y,\n", "                'z_coord': center.z,\n", "                'radius': getattr(entity.dxf, 'radius', 0.0)\n", "            })\n", "        else:\n", "            # Default coordinates\n", "            entity_data.update({\n", "                'x_coord': 0.0,\n", "                'y_coord': 0.0,\n", "                'z_coord': 0.0\n", "            })\n", "        \n", "        # Text content for MTEXT entities\n", "        if entity_type == 'MTEXT':\n", "            entity_data['text_content'] = getattr(entity, 'text', '')\n", "        \n", "        # Classification\n", "        entity_data['classification'] = classifier.classify_entity(entity_data)\n", "        entity_data['extraction_timestamp'] = datetime.now().isoformat()\n", "        \n", "        return entity_data\n", "        \n", "    except Exception as e:\n", "        logger.warning(f\"Error extracting entity data: {e}\")\n", "        return None\n", "\n", "print(\"Entity extraction function defined\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Process CAD file\n", "cad_input_dir = Path(cad_data_path)\n", "main_dxf_file = cad_input_dir / \"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\"\n", "\n", "if not main_dxf_file.exists():\n", "    raise FileNotFoundError(f\"CAD file not found: {main_dxf_file}\")\n", "\n", "print(f\"Processing CAD file: {main_dxf_file.name}\")\n", "\n", "# Load and process DXF file\n", "doc = ezdxf.readfile(main_dxf_file)\n", "msp = doc.modelspace()\n", "\n", "all_entities = []\n", "processing_stats = defaultdict(int)\n", "\n", "for entity in msp:\n", "    processing_stats['total_entities'] += 1\n", "    \n", "    entity_data = extract_entity_data(entity)\n", "    if entity_data:\n", "        all_entities.append(entity_data)\n", "        processing_stats['successful_extractions'] += 1\n", "        processing_stats[f\"classification_{entity_data['classification']}\"] += 1\n", "    else:\n", "        processing_stats['failed_extractions'] += 1\n", "\n", "print(f\"\\nProcessing completed:\")\n", "print(f\"  Total entities: {processing_stats['total_entities']}\")\n", "print(f\"  Successful extractions: {processing_stats['successful_extractions']}\")\n", "print(f\"  Failed extractions: {processing_stats['failed_extractions']}\")\n", "\n", "# Convert to DataFrame\n", "if all_entities:\n", "    entities_df = pd.DataFrame(all_entities)\n", "    print(f\"\\nExtracted entities DataFrame: {len(entities_df)} rows\")\n", "else:\n", "    print(\"No entities extracted\")\n", "    entities_df = pd.DataFrame()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Infrastructure Analysis and Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not entities_df.empty:\n", "    print(\"\\n=== INFRASTRUCTURE ANALYSIS ===\")\n", "    \n", "    # Classification summary\n", "    classification_counts = entities_df['classification'].value_counts()\n", "    print(f\"\\nClassification Summary:\")\n", "    for classification, count in classification_counts.items():\n", "        print(f\"  {classification}: {count}\")\n", "    \n", "    # Infrastructure-specific analysis\n", "    infrastructure_analysis = {}\n", "    \n", "    # Foundation piles\n", "    foundation_piles = entities_df[entities_df['classification'] == 'foundation_pile']\n", "    infrastructure_analysis['foundation_piles'] = {\n", "        'count': len(foundation_piles),\n", "        'expected': GROUND_TRUTH['piling']['planned'],\n", "        'accuracy': (len(foundation_piles) / GROUND_TRUTH['piling']['planned']) * 100 if GROUND_TRUTH['piling']['planned'] > 0 else 0\n", "    }\n", "    \n", "    # Trackers\n", "    trackers = entities_df[entities_df['classification'] == 'tracker']\n", "    infrastructure_analysis['trackers'] = {\n", "        'count': len(trackers),\n", "        'expected': GROUND_TRUTH['tracker']['planned'],\n", "        'accuracy': (len(trackers) / GROUND_TRUTH['tracker']['planned']) * 100 if GROUND_TRUTH['tracker']['planned'] > 0 else 0\n", "    }\n", "    \n", "    # Module support piles (estimated from trackers)\n", "    module_support_estimate = 0\n", "    for _, tracker in trackers.iterrows():\n", "        block_name = tracker.get('block_name', '')\n", "        if '1x52' in block_name:\n", "            module_support_estimate += 52\n", "        elif '1x26' in block_name:\n", "            module_support_estimate += 26\n", "    \n", "    infrastructure_analysis['module_support_piles'] = {\n", "        'count': module_support_estimate,\n", "        'expected': GROUND_TRUTH['modules']['planned'],\n", "        'accuracy': (module_support_estimate / GROUND_TRUTH['modules']['planned']) * 100 if GROUND_TRUTH['modules']['planned'] > 0 else 0\n", "    }\n", "    \n", "    print(f\"\\n=== GROUND TRUTH VALIDATION ===\")\n", "    for infra_type, analysis in infrastructure_analysis.items():\n", "        print(f\"\\n{infra_type.replace('_', ' ').title()}:\")\n", "        print(f\"  Extracted: {analysis['count']}\")\n", "        print(f\"  Expected: {analysis['expected']}\")\n", "        print(f\"  Accuracy: {analysis['accuracy']:.1f}%\")\n", "        \n", "        if analysis['accuracy'] >= 90:\n", "            status = \"✅ Excellent\"\n", "        elif analysis['accuracy'] >= 70:\n", "            status = \"✅ Good\"\n", "        elif analysis['accuracy'] >= 50:\n", "            status = \"⚠️ Partial\"\n", "        else:\n", "            status = \"❌ Poor\"\n", "        print(f\"  Status: {status}\")\n", "    \n", "    # Overall accuracy\n", "    accuracy_scores = [analysis['accuracy'] for analysis in infrastructure_analysis.values()]\n", "    overall_accuracy = np.mean(accuracy_scores)\n", "    print(f\"\\nOverall Accuracy: {overall_accuracy:.1f}%\")\n", "else:\n", "    print(\"No entities to analyze\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Export Results and Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not entities_df.empty:\n", "    # Create output directory\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    output_path = Path(output_dir)\n", "    run_output_dir = output_path / f\"{project_type}_{site_name}_{timestamp}\"\n", "    run_output_dir.mkdir(parents=True, exist_ok=True)\n", "    \n", "    print(f\"\\n=== EXPORTING RESULTS ===\")\n", "    print(f\"Output directory: {run_output_dir}\")\n", "    \n", "    # Main entities file\n", "    main_file = run_output_dir / f\"comprehensive_infrastructure_extraction_{timestamp}.csv\"\n", "    entities_df.to_csv(main_file, index=False)\n", "    print(f\"Main entities file: {main_file.name}\")\n", "    \n", "    # Infrastructure-specific files\n", "    for classification in entities_df['classification'].unique():\n", "        if classification != 'unknown':\n", "            subset_df = entities_df[entities_df['classification'] == classification]\n", "            if not subset_df.empty:\n", "                subset_file = run_output_dir / f\"{classification}_entities_{timestamp}.csv\"\n", "                subset_df.to_csv(subset_file, index=False)\n", "                print(f\"{classification.title()} file: {subset_file.name} ({len(subset_df)} entities)\")\n", "    \n", "    print(f\"\\n✅ Export completed successfully\")\n", "    print(f\"📁 All files saved to: {run_output_dir}\")\n", "    \n", "    print(f\"\\n🎯 Recommended Next Steps:\")\n", "    print(f\"  1. Use extracted data for point cloud alignment\")\n", "    print(f\"  2. Implement pile area assignment using tracker data\")\n", "    print(f\"  3. Validate coordinate system consistency (EPSG:32633)\")\n", "    print(f\"  4. Integrate with as-built validation workflows\")\n", "    \n", "else:\n", "    print(\"No entities to export\")\n", "\n", "print(f\"\\n📅 Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}