{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Comprehensive Infrastructure CAD Extraction\n",
    "\n",
    "Enhanced CAD metadata extraction that distinguishes between different pile types and extracts all infrastructure elements to match ground truth specifications.\n",
    "\n",
    "## Target Ground Truth Validation:\n",
    "- **Foundation Piles**: 4,199 (main structural piles)\n",
    "- **Module Support Points**: 23,764 (solar panel support)\n",
    "- **Trackers**: 543 (tracking systems)\n",
    "- **Infrastructure**: Roads, fencing, electrical, logistics\n",
    "\n",
    "## Enhanced Classification:\n",
    "- Distinguishes foundation piles from module support piles\n",
    "- Extracts roads, fencing, electrical infrastructure\n",
    "- Identifies transformers, boundaries, facilities\n",
    "- Validates against ground truth quantities"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import required libraries\n",
    "import pandas as pd\n",
    "import numpy as np\n",
    "from pathlib import Path\n",
    "import ezdxf\n",
    "import json\n",
    "from datetime import datetime\n",
    "from collections import defaultdict\n",
    "import logging\n",
    "import mlflow\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "import re\n",
    "\n",
    "# Configure logging\n",
    "logging.basicConfig(level=logging.INFO)\n",
    "logger = logging.getLogger(__name__)\n",
    "\n",
    "# Configuration\n",
    "project_type = \"motali_de_castro\"\n",
    "site_name = \"comprehensive_extraction\"\n",
    "coordinate_system = \"UTM_33N_WGS84\"\n",
    "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n",
    "\n",
    "# Load ground truth for validation\n",
    "ground_truth_path = Path(\"../../../data/reference/montalto_di_castro_ground_truth.csv\")\n",
    "ground_truth_df = pd.read_csv(ground_truth_path)\n",
    "\n",
    "# Convert to dictionary for easy lookup\n",
    "GROUND_TRUTH = {}\n",
    "for _, row in ground_truth_df.iterrows():\n",
    "    key = row['Features'].lower().replace(' ', '_')\n",
    "    GROUND_TRUTH[key] = {\n",
    "        'planned': row['Planned'],\n",
    "        'unit': row['Unit'],\n",
    "        'category': row['Categories'],\n",
    "        'notes': row.get('Notes', '')\n",
    "    }\n",
    "\n",
    "print(f\"Comprehensive Infrastructure CAD Extraction - {project_type.title()}\")\n",
    "print(f\"Timestamp: {timestamp}\")\n",
    "print(f\"Ground truth items loaded: {len(GROUND_TRUTH)}\")\n",
    "print(\"=\"*60)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. Enhanced Infrastructure Classifier"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "class ComprehensiveInfrastructureClassifier:\n",
    "    \"\"\"Enhanced classifier for all infrastructure elements.\"\"\"\n",
    "    \n",
    "    def __init__(self):\n",
    "        # Tracker patterns\n",
    "        self.tracker_patterns = [\n",
    "            'CVT_Tracker 1x52 int', 'CVT_Tracker 1x52 ext', 'CVT_Tracker 1X52 Edge',\n",
    "            'CVT_Tracker 1x26 int', 'CVT_Tracker 1x26 ext'\n",
    "        ]\n",
    "        \n",
    "        # Infrastructure classification patterns\n",
    "        self.classification_patterns = {\n",
    "            # Pile types\n",
    "            'foundation_pile': {\n",
    "                'keywords': ['foundation', 'found', 'pile_foundation', 'structural_pile'],\n",
    "                'layer_patterns': ['foundation', 'found', 'pile_found'],\n",
    "                'entity_types': ['CIRCLE', 'INSERT'],\n",
    "                'expected_count': 4199\n",
    "            },\n",
    "            'module_support_pile': {\n",
    "                'keywords': ['pile', 'piling', 'support', 'tracker_pile'],\n",
    "                'layer_patterns': ['pile', 'piling', 'support'],\n",
    "                'entity_types': ['CIRCLE', 'INSERT'],\n",
    "                'expected_count': 23764\n",
    "            },\n",
    "            'pile_hole': {\n",
    "                'keywords': ['hole', 'drill', 'boring'],\n",
    "                'layer_patterns': ['hole', 'drill', 'boring'],\n",
    "                'entity_types': ['CIRCLE'],\n",
    "                'expected_count': 4199\n",
    "            },\n",
    "            \n",
    "            # Tracker systems\n",
    "            'tracker': {\n",
    "                'keywords': ['tracker', 'cvt'],\n",
    "                'layer_patterns': self.tracker_patterns,\n",
    "                'entity_types': ['INSERT'],\n",
    "                'expected_count': 543\n",
    "            },\n",
    "            \n",
    "            # Infrastructure\n",
    "            'road': {\n",
    "                'keywords': ['road', 'strada', 'access', 'path'],\n",
    "                'layer_patterns': ['road', 'strada', 'access', 'path'],\n",
    "                'entity_types': ['LINE', 'LWPOLYLINE', 'POLYLINE'],\n",
    "                'expected_count': 3220.93,  # meters\n",
    "                'unit': 'meters'\n",
    "            },\n",
    "            'fence': {\n",
    "                'keywords': ['fence', 'recinzione', 'chainlink', 'boundary'],\n",
    "                'layer_patterns': ['fence', 'recinzione', 'chainlink', 'boundary'],\n",
    "                'entity_types': ['LINE', 'LWPOLYLINE', 'POLYLINE'],\n",
    "                'expected_count': 3873.1,  # meters\n",
    "                'unit': 'meters'\n",
    "            },\n",
    "            \n",
    "            # Electrical infrastructure\n",
    "            'electrical_trench': {\n",
    "                'keywords': ['trench', 'electrical', 'cable', 'voltage'],\n",
    "                'layer_patterns': ['trench', 'electrical', 'cable', 'voltage'],\n",
    "                'entity_types': ['LINE', 'LWPOLYLINE', 'POLYLINE'],\n",
    "                'expected_count': 7706,  # total trenching meters\n",
    "                'unit': 'meters'\n",
    "            },\n",
    "            'transformer': {\n",
    "                'keywords': ['transformer', 'trafo', 'electrical_unit'],\n",
    "                'layer_patterns': ['transformer', 'trafo', 'electrical'],\n",
    "                'entity_types': ['INSERT', 'CIRCLE'],\n",
    "                'expected_count': 3\n",
    "            },\n",
    "            \n",
    "            # Facilities\n",
    "            'cabin_foundation': {\n",
    "                'keywords': ['cabin', 'user_cabin', 'facility'],\n",
    "                'layer_patterns': ['cabin', 'facility', 'building'],\n",
    "                'entity_types': ['INSERT', 'LWPOLYLINE'],\n",
    "                'expected_count': 2  # execution + installation\n",
    "            },\n",
    "            'delivery_booth': {\n",
    "                'keywords': ['delivery', 'booth', 'gate'],\n",
    "                'layer_patterns': ['delivery', 'booth', 'gate'],\n",
    "                'entity_types': ['INSERT', 'LWPOLYLINE'],\n",
    "                'expected_count': 2  # execution + installation\n",
    "            },\n",
    "            \n",
    "            # Site logistics\n",
    "            'vehicle_area': {\n",
    "                'keywords': ['vehicle', 'parking', 'equipment'],\n",
    "                'layer_patterns': ['vehicle', 'parking', 'equipment'],\n",
    "                'entity_types': ['LWPOLYLINE', 'INSERT'],\n",
    "                'expected_count': 35\n",
    "            },\n",
    "            'pallet_position': {\n",
    "                'keywords': ['pallet', 'material', 'storage'],\n",
    "                'layer_patterns': ['pallet', 'material', 'storage'],\n",
    "                'entity_types': ['INSERT', 'CIRCLE'],\n",
    "                'expected_count': 42\n",
    "            },\n",
    "            'site_structure': {\n",
    "                'keywords': ['structure', 'temporary', 'site'],\n",
    "                'layer_patterns': ['structure', 'temporary', 'site'],\n",
    "                'entity_types': ['INSERT', 'LWPOLYLINE'],\n",
    "                'expected_count': 34\n",
    "            },\n",
    "            \n",
    "            # Boundaries\n",
    "            'block_boundary': {\n",
    "                'keywords': ['block', 'boundary', 'limit'],\n",
    "                'layer_patterns': ['block', 'boundary', 'limit'],\n",
    "                'entity_types': ['LWPOLYLINE', 'LINE'],\n",
    "                'expected_count': 3\n",
    "            }\n",
    "        }\n",
    "    \n",
    "    def classify_entity(self, entity, layer_name, block_name, text_content):\n",
    "        \"\"\"Enhanced classification with infrastructure-specific logic.\"\"\"\n",
    "        entity_type = entity.dxftype()\n",
    "        layer_lower = layer_name.lower()\n",
    "        block_lower = block_name.lower()\n",
    "        text_lower = text_content.lower()\n",
    "        \n",
    "        # Check each classification pattern\n",
    "        for classification, pattern in self.classification_patterns.items():\n",
    "            # Check entity type compatibility\n",
    "            if entity_type not in pattern['entity_types']:\n",
    "                continue\n",
    "            \n",
    "            # Check layer patterns\n",
    "            layer_match = any(pattern_str in layer_lower for pattern_str in pattern['layer_patterns'])\n",
    "            \n",
    "            # Check keyword patterns\n",
    "            keyword_match = any(keyword in layer_lower or keyword in block_lower or keyword in text_lower \n",
    "                              for keyword in pattern['keywords'])\n",
    "            \n",
    "            if layer_match or keyword_match:\n",
    "                return classification\n",
    "        \n",
    "        # Special handling for tracker patterns (exact match)\n",
    "        for tracker_pattern in self.tracker_patterns:\n",
    "            if tracker_pattern in layer_name:\n",
    "                return 'tracker'\n",
    "        \n",
    "        # Text annotations\n",
    "        if entity_type in ['TEXT', 'MTEXT'] and text_content:\n",
    "            return 'annotation'\n",
    "        \n",
    "        return 'unknown'\n",
    "    \n",
    "    def get_detailed_classification(self, entity, layer_name, block_name, text_content):\n",
    "        \"\"\"Get detailed classification with confidence and reasoning.\"\"\"\n",
    "        base_classification = self.classify_entity(entity, layer_name, block_name, text_content)\n",
    "        \n",
    "        # Calculate confidence based on matching criteria\n",
    "        confidence = 0.5  # base confidence\n",
    "        reasoning = []\n",
    "        \n",
    "        if base_classification != 'unknown':\n",
    "            pattern = self.classification_patterns.get(base_classification, {})\n",
    "            \n",
    "            # Layer name match\n",
    "            if any(p in layer_name.lower() for p in pattern.get('layer_patterns', [])):\n",
    "                confidence += 0.3\n",
    "                reasoning.append('layer_match')\n",
    "            \n",
    "            # Block name match\n",
    "            if any(k in block_name.lower() for k in pattern.get('keywords', [])):\n",
    "                confidence += 0.2\n",
    "                reasoning.append('block_match')\n",
    "            \n",
    "            # Entity type match\n",
    "            if entity.dxftype() in pattern.get('entity_types', []):\n",
    "                confidence += 0.2\n",
    "                reasoning.append('entity_type_match')\n",
    "        \n",
    "        return {\n",
    "            'classification': base_classification,\n",
    "            'confidence': min(confidence, 1.0),\n",
    "            'reasoning': ','.join(reasoning) if reasoning else 'default'\n",
    "        }\n",
    "\n",
    "# Initialize enhanced classifier\n",
    "classifier = ComprehensiveInfrastructureClassifier()\n",
    "print(f\"Enhanced infrastructure classifier initialized\")\n",
    "print(f\"Tracking {len(classifier.classification_patterns)} infrastructure types\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. Enhanced Entity Extraction with Infrastructure Support"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def extract_comprehensive_entity_data(entity, classifier):\n",
    "    \"\"\"Extract comprehensive entity data with infrastructure-specific measurements.\"\"\"\n",
    "    try:\n",
    "        entity_type = entity.dxftype()\n",
    "        layer_name = getattr(entity.dxf, 'layer', 'unknown')\n",
    "        \n",
    "        # Base entity data\n",
    "        entity_data = {\n",
    "            'entity_id': str(entity.dxf.handle),\n",
    "            'entity_type': entity_type,\n",
    "            'layer_name': layer_name,\n",
    "            'color': getattr(entity.dxf, 'color', 256),\n",
    "            'linetype': getattr(entity.dxf, 'linetype', 'Continuous'),\n",
    "            'lineweight': getattr(entity.dxf, 'lineweight', -1)\n",
    "        }\n",
    "        \n",
    "        # Extract geometry and measurements based on entity type\n",
    "        if entity_type == 'INSERT':\n",
    "            # INSERT entities (trackers, transformers, facilities)\n",
    "            insert_point = getattr(entity.dxf, 'insert', (0, 0, 0))\n",
    "            entity_data.update({\n",
    "                'x_coord': float(insert_point[0]),\n",
    "                'y_coord': float(insert_point[1]),\n",
    "                'z_coord': float(insert_point[2]),\n",
    "                'geometry_type': 'insert',\n",
    "                'block_name': getattr(entity.dxf, 'name', ''),\n",
    "                'rotation': getattr(entity.dxf, 'rotation', 0.0),\n",
    "                'scale_x': getattr(entity.dxf, 'xscale', 1.0),\n",
    "                'scale_y': getattr(entity.dxf, 'yscale', 1.0),\n",
    "                'scale_z': getattr(entity.dxf, 'zscale', 1.0),\n",
    "                'measurement_value': 1.0,  # count\n",
    "                'measurement_unit': 'count'\n",
    "            })\n",
    "            \n",
    "        elif entity_type == 'CIRCLE':\n",
    "            # CIRCLE entities (piles, holes)\n",
    "            center = getattr(entity.dxf, 'center', (0, 0, 0))\n",
    "            radius = getattr(entity.dxf, 'radius', 0.0)\n",
    "            entity_data.update({\n",
    "                'x_coord': float(center[0]),\n",
    "                'y_coord': float(center[1]),\n",
    "                'z_coord': float(center[2]),\n",
    "                'geometry_type': 'circle',\n",
    "                'radius': radius,\n",
    "                'diameter': radius * 2,\n",
    "                'area': np.pi * radius * radius,\n",
    "                'measurement_value': 1.0,  # count\n",
    "                'measurement_unit': 'count'\n",
    "            })\n",
    "            \n",
    "        elif entity_type == 'LINE':\n",
    "            # LINE entities (roads, fencing, trenches)\n",
    "            start = getattr(entity.dxf, 'start', (0, 0, 0))\n",
    "            end = getattr(entity.dxf, 'end', (0, 0, 0))\n",
    "            length = float(np.linalg.norm(np.array(end) - np.array(start)))\n",
    "            \n",
    "            entity_data.update({\n",
    "                'x_coord': float((start[0] + end[0]) / 2),\n",
    "                'y_coord': float((start[1] + end[1]) / 2),\n",
    "                'z_coord': float((start[2] + end[2]) / 2),\n",
    "                'geometry_type': 'line',\n",
    "                'start_x': float(start[0]),\n",
    "                'start_y': float(start[1]),\n",
    "                'start_z': float(start[2]),\n",
    "                'end_x': float(end[0]),\n",
    "                'end_y': float(end[1]),\n",
    "                'end_z': float(end[2]),\n",
    "                'length': length,\n",
    "                'measurement_value': length,\n",
    "                'measurement_unit': 'meters'\n",
    "            })\n",
    "            \n",
    "        elif entity_type in ['LWPOLYLINE', 'POLYLINE']:\n",
    "            # POLYLINE entities (roads, boundaries, areas)\n",
    "            try:\n",
    "                if hasattr(entity, 'get_points'):\n",
    "                    points = list(entity.get_points())\n",
    "                else:\n",
    "                    points = []\n",
    "                \n",
    "                if points:\n",
    "                    points_array = np.array(points)\n",
    "                    centroid = np.mean(points_array, axis=0)\n",
    "                    \n",
    "                    # Calculate total length\n",
    "                    total_length = 0.0\n",
    "                    for i in range(len(points) - 1):\n",
    "                        segment_length = np.linalg.norm(np.array(points[i+1]) - np.array(points[i]))\n",
    "                        total_length += segment_length\n",
    "                    \n",
    "                    # Check if closed\n",
    "                    is_closed = getattr(entity.dxf, 'flags', 0) & 1\n",
    "                    if is_closed and len(points) > 2:\n",
    "                        # Add closing segment\n",
    "                        total_length += np.linalg.norm(np.array(points[0]) - np.array(points[-1]))\n",
    "                    \n",
    "                    entity_data.update({\n",
    "                        'x_coord': float(centroid[0]),\n",
    "                        'y_coord': float(centroid[1]),\n",
    "                        'z_coord': float(centroid[2]) if len(centroid) > 2 else 0.0,\n",
    "                        'geometry_type': 'polyline',\n",
    "                        'point_count': len(points),\n",
    "                        'is_closed': is_closed,\n",
    "                        'length': total_length,\n",
    "                        'measurement_value': total_length,\n",
    "                        'measurement_unit': 'meters'\n",
    "                    })\n",
    "                else:\n",
    "                    entity_data.update({\n",
    "                        'x_coord': 0.0,\n",
    "                        'y_coord': 0.0,\n",
    "                        'z_coord': 0.0,\n",
    "                        'geometry_type': 'polyline',\n",
    "                        'measurement_value': 0.0,\n",
    "                        'measurement_unit': 'meters'\n",
    "                    })\n",
    "            except Exception as e:\n",
    "                logger.warning(f\"Error processing polyline: {e}\")\n",
    "                entity_data.update({\n",
    "                    'x_coord': 0.0,\n",
    "                    'y_coord': 0.0,\n",
    "                    'z_coord': 0.0,\n",
    "                    'geometry_type': 'polyline',\n",
    "                    'measurement_value': 0.0,\n",
    "                    'measurement_unit': 'meters'\n",
    "                })\n",
    "            \n",
    "        elif entity_type in ['TEXT', 'MTEXT']:\n",
    "            # Text entities\n",
    "            insert_point = getattr(entity.dxf, 'insert', (0, 0, 0))\n",
    "            entity_data.update({\n",
    "                'x_coord': float(insert_point[0]),\n",
    "                'y_coord': float(insert_point[1]),\n",
    "                'z_coord': float(insert_point[2]),\n",
    "                'geometry_type': 'text',\n",
    "                'text_content': getattr(entity.dxf, 'text', ''),\n",
    "                'text_height': getattr(entity.dxf, 'height', 0.0),\n",
    "                'text_rotation': getattr(entity.dxf, 'rotation', 0.0),\n",
    "                'measurement_value': 1.0,\n",
    "                'measurement_unit': 'count'\n",
    "            })\n",
    "            \n",
    "        else:\n",
    "            # Default handling for other entity types\n",
    "            entity_data.update({\n",
    "                'x_coord': 0.0,\n",
    "                'y_coord': 0.0,\n",
    "                'z_coord': 0.0,\n",
    "                'geometry_type': entity_type.lower(),\n",
    "                'measurement_value': 1.0,\n",
    "                'measurement_unit': 'count'\n",
    "            })\n",
    "        \n",
    "        # Get detailed classification\n",
    "        block_name = entity_data.get('block_name', '')\n",
    "        text_content = entity_data.get('text_content', '')\n",
    "        classification_result = classifier.get_detailed_classification(\n",
    "            entity, layer_name, block_name, text_content\n",
    "        )\n",
    "        \n",
    "        entity_data.update(classification_result)\n",
    "        \n",
    "        # Add extraction metadata\n",
    "        entity_data['extraction_timestamp'] = datetime.now().isoformat()\n",
    "        \n",
    "        return entity_data\n",
    "        \n",
    "    except Exception as e:\n",
    "        logger.warning(f\"Error extracting entity data: {e}\")\n",
    "        return {\n",
    "            'entity_id': 'error',\n",
    "            'entity_type': getattr(entity, 'dxftype', lambda: 'unknown')(),\n",
    "            'error': str(e),\n",
    "            'extraction_timestamp': datetime.now().isoformat()\n",
    "        }\n",
    "\n",
    "print(\"Comprehensive entity extraction function defined\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",\n",
   "language": "python",\n",
   "name": "python3"\n",
  },\n",
  "language_info": {\n",
   "codemirror_mode": {\n",
    "name": "ipython",\n",
    "version": 3\n",
   },\n",
   "file_extension": ".py",\n",
   "mimetype": "text/x-python",\n",
   "name": "python",\n",
   "nbconvert_exporter": "python",\n",
   "pygments_lexer": "ipython3",\n",
   "version": "3.11.11"\n",
  }\n",
 "nbformat": 4,\n",
 "nbformat_minor": 4\n}
