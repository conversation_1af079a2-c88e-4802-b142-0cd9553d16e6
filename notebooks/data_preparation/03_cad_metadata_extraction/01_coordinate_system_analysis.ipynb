{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CAD Coordinate System Analysis\n", "\n", "This notebook analyzes CAD files to automatically detect coordinate systems and valid coordinate ranges.\n", "\n", "## Purpose:\n", "- **Detect coordinate system type** (UTM, Geographic, Local)\n", "- **Find valid coordinate ranges** using statistical analysis\n", "- **Generate site configuration** for other notebooks to use\n", "- **No hard-coding** - adapts to any site automatically\n", "\n", "## Outputs:\n", "- **site_coordinate_config.json**: Coordinate system info and valid ranges\n", "- **coordinate_analysis_report.txt**: Human-readable analysis report\n", "- **coordinate_distribution_plots.png**: Visual analysis of coordinate distribution\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Setup and Configuration"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-01 17:05:06,702 - INFO - Libraries imported successfully\n", "2025-07-01 17:05:06,703 - INFO - Analysis started: 2025-07-01 17:05:06\n"]}], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import ezdxf\n", "import json\n", "from datetime import datetime\n", "from collections import defaultdict\n", "import seaborn as sns\n", "import logging\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "logger.info(\"Libraries imported successfully\")\n", "logger.info(f\"Analysis started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Configuration:\n", "-  Target files: ['GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf']\n", "-  Search path: ../../../data/raw\n", "-  Output directory: ../../../output_runs/coordinate_analysis\n"]}], "source": ["# Configuration\n", "target_files = [\n", "    \"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\"\n", "    #\"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg\"\n", "]\n", "\n", "search_path = \"../../../data/raw\"\n", "output_dir = \"../../../output_runs/coordinate_analysis\"\n", "\n", "print(\"Configuration:\")\n", "print(f\"-  Target files: {target_files}\")\n", "print(f\"-  Search path: {search_path}\")\n", "print(f\"-  Output directory: {output_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Find and Analyze CAD Files"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Finding CAD files...\n", "Found: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\n", "\n", "Files to analyze: 1\n"]}], "source": ["print(\"Finding CAD files...\")\n", "\n", "found_files = []\n", "for target_file in target_files:\n", "    result = !find {search_path} -name \"{target_file}\" 2>/dev/null\n", "    if result:\n", "        file_path = Path(result[0])\n", "        found_files.append(file_path)\n", "        print(f\"Found: {file_path.name}\")\n", "    else:\n", "        print(f\"Not found: {target_file}\")\n", "\n", "print(f\"\\nFiles to analyze: {len(found_files)}\")\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running ogrinfo analysis...\n", "\n", "GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf:\n", "INFO: Open of `../../../data/raw/motali_de_castro/cad/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf'\n", "      using driver `DXF' successful.\n", "1: entities\n"]}], "source": ["\n", "# Quick ogrinfo analysis if available\n", "ogrinfo_check = !which ogrinfo 2>/dev/null\n", "if ogrinfo_check:\n", "    print(\"Running ogrinfo analysis...\")\n", "    for file_path in found_files:\n", "        if file_path.suffix.lower() == '.dxf':\n", "            print(f\"\\n{file_path.name}:\")\n", "            !ogrinfo -so \"{file_path}\" | head -20\n", "else:\n", "    print(\"\\nogrinfo not available - proceeding with Python analysis\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Extract All Coordinates for Analysis"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def extract_point_coordinates(entity, layer):\n", "    \"\"\"\n", "    Extract coordinates from entity using adaptive validation based on detected ranges.\n", "    Uses the site configuration generated by coordinate system analysis.\n", "    \"\"\"\n", "    coordinates = []\n", "    entity_type = entity.dxftype()\n", "    \n", "    # Load site configuration if available\n", "    try:\n", "        config_file = Path(\"../../../output_runs/coordinate_analysis/coordinate_analysis_20250701_155139/site_coordinate_config_20250701_155139.json\")\n", "        if config_file.exists():\n", "            with open(config_file, 'r') as f:\n", "                site_config = json.load(f)\n", "            valid_ranges = site_config.get('valid_ranges')\n", "        else:\n", "            valid_ranges = None\n", "    except:\n", "        valid_ranges = None\n", "    \n", "    def is_valid_coordinate(x, y, z=None):\n", "        \"\"\"Validate coordinates using detected ranges or fallback validation.\"\"\"\n", "        # Basic validation\n", "        if x == 0.0 and y == 0.0:\n", "            return False\n", "        if abs(x) < 100 or abs(y) < 100:\n", "            return False\n", "        \n", "        # Use detected ranges if available\n", "        if valid_ranges:\n", "            if not (valid_ranges['x_min'] <= x <= valid_ranges['x_max']):\n", "                return False\n", "            if not (valid_ranges['y_min'] <= y <= valid_ranges['y_max']):\n", "                return False\n", "        \n", "        return True\n", "    \n", "    # Extract coordinates based on entity type\n", "    if entity_type == 'POINT':\n", "        if hasattr(entity.dxf, 'location'):\n", "            pt = entity.dxf.location\n", "            if is_valid_coordinate(pt.x, pt.y, pt.z):\n", "                coordinates.append({\n", "                    'x': pt.x, 'y': pt.y, 'z': pt.z,\n", "                    'entity_type': entity_type,\n", "                    'layer': layer,\n", "                    'source': 'point_location'\n", "                })\n", "    \n", "    elif entity_type == 'INSERT':\n", "        if hasattr(entity.dxf, 'insert'):\n", "            pt = entity.dxf.insert\n", "            if is_valid_coordinate(pt.x, pt.y, pt.z):\n", "                coordinates.append({\n", "                    'x': pt.x, 'y': pt.y, 'z': pt.z,\n", "                    'entity_type': entity_type,\n", "                    'layer': layer,\n", "                    'source': 'insert_point'\n", "                })\n", "    \n", "    elif entity_type == 'LINE':\n", "        if hasattr(entity.dxf, 'start') and hasattr(entity.dxf, 'end'):\n", "            start = entity.dxf.start\n", "            end = entity.dxf.end\n", "            \n", "            if is_valid_coordinate(start.x, start.y, start.z):\n", "                coordinates.append({\n", "                    'x': start.x, 'y': start.y, 'z': start.z,\n", "                    'entity_type': entity_type, 'layer': layer, 'source': 'line_start'\n", "                })\n", "            \n", "            if is_valid_coordinate(end.x, end.y, end.z):\n", "                coordinates.append({\n", "                    'x': end.x, 'y': end.y, 'z': end.z,\n", "                    'entity_type': entity_type, 'layer': layer, 'source': 'line_end'\n", "                })\n", "    \n", "    elif entity_type in ['LWPOLYLINE', 'POLYLINE']:\n", "        try:\n", "            bbox = entity.bbox()\n", "            if bbox:\n", "                center_x = (bbox[0].x + bbox[1].x) / 2\n", "                center_y = (bbox[0].y + bbox[1].y) / 2\n", "                center_z = (bbox[0].z + bbox[1].z) / 2\n", "                \n", "                if is_valid_coordinate(center_x, center_y, center_z):\n", "                    coordinates.append({\n", "                        'x': center_x, 'y': center_y, 'z': center_z,\n", "                        'entity_type': entity_type,\n", "                        'layer': layer,\n", "                        'source': 'polyline_center'\n", "                    })\n", "        except:\n", "            pass\n", "    \n", "    return coordinates"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracting coordinates from all CAD files...\n"]}, {"ename": "NameError", "evalue": "name 'extract_all_coordinates' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 9\u001b[39m\n\u001b[32m      7\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m file_path \u001b[38;5;129;01min\u001b[39;00m found_files:\n\u001b[32m      8\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m file_path.suffix.lower() == \u001b[33m'\u001b[39m\u001b[33m.dxf\u001b[39m\u001b[33m'\u001b[39m:  \u001b[38;5;66;03m# Process DXF files\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m9\u001b[39m         coords, entity_info = \u001b[43mextract_all_coordinates\u001b[49m(file_path)\n\u001b[32m     11\u001b[39m         \u001b[38;5;66;03m# Add file source to each coordinate\u001b[39;00m\n\u001b[32m     12\u001b[39m         \u001b[38;5;28;01mfor\u001b[39;00m coord \u001b[38;5;129;01min\u001b[39;00m coords:\n", "\u001b[31mNameError\u001b[39m: name 'extract_all_coordinates' is not defined"]}], "source": ["# Extract coordinates from all files\n", "print(\"Extracting coordinates from all CAD files...\")\n", "\n", "all_coordinates = []\n", "file_analysis = {}\n", "\n", "for file_path in found_files:\n", "    if file_path.suffix.lower() == '.dxf':  # Process DXF files\n", "        coords, entity_info = extract_all_coordinates(file_path)\n", "        \n", "        # Add file source to each coordinate\n", "        for coord in coords:\n", "            coord['source_file'] = file_path.name\n", "        \n", "        all_coordinates.extend(coords)\n", "        file_analysis[file_path.name] = {\n", "            'coordinate_count': len(coords),\n", "            'entity_types': entity_info\n", "        }\n", "    else:\n", "        print(f\"Skipping {file_path.name} (DWG - convert to DXF first)\")\n", "\n", "print(f\"\\nTotal coordinates extracted: {len(all_coordinates)}\")\n", "print(f\"Files processed: {len(file_analysis)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Coordinate System Detection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "def _filter_valid_coordinates(coordinates):\n", "    df = pd.DataFrame(coordinates)\n", "    return df[\n", "        (df['x'] != 0.0) &\n", "        (df['y'] != 0.0) &\n", "        (df['x'].abs() > 1) &\n", "        (df['y'].abs() > 1)\n", "    ]\n", "\n", "def _compute_ranges(df):\n", "    x_min, x_max = df['x'].min(), df['x'].max()\n", "    y_min, y_max = df['y'].min(), df['y'].max()\n", "    return {\n", "        'x_min': x_min, 'x_max': x_max, 'x_range': x_max - x_min,\n", "        'y_min': y_min, 'y_max': y_max, 'y_range': y_max - y_min\n", "    }\n", "\n", "def _infer_crs_from_ranges(ranges):\n", "    x_min, y_min = ranges['x_min'], ranges['y_min']\n", "    x_range, y_range = ranges['x_range'], ranges['y_range']\n", "\n", "    if 100_000 <= x_min <= 900_000 and 1_000_000 <= y_min <= 10_000_000:\n", "        result = {'type': 'UTM', 'confidence': 'high', 'reason': 'coordinates_in_utm_range'}\n", "        if 600_000 <= x_min <= 800_000:\n", "            result['utm_zone_estimate'] = '32N or 33N (Europe)'\n", "        return result\n", "\n", "    elif -180 <= x_min <= 180 and -90 <= y_min <= 90:\n", "        return {'type': 'Geographic', 'confidence': 'high', 'reason': 'coordinates_in_geographic_range'}\n", "\n", "    elif x_range < 100_000 and y_range < 100_000:\n", "        return {'type': 'Local', 'confidence': 'medium', 'reason': 'small_coordinate_ranges_suggest_local_system'}\n", "\n", "    return {'type': 'Unknown', 'confidence': 'low', 'reason': 'coordinates_outside_known_patterns'}\n", "\n", "def detect_coordinate_system(coordinates):\n", "    if not coordinates:\n", "        return {'type': 'Unknown', 'confidence': 'low'}\n", "\n", "    valid_coords = _filter_valid_coordinates(coordinates)\n", "    if valid_coords.empty:\n", "        return {'type': 'Unknown', 'confidence': 'low', 'reason': 'no_valid_coordinates'}\n", "\n", "    ranges = _compute_ranges(valid_coords)\n", "    crs_guess = _infer_crs_from_ranges(ranges)\n", "\n", "    result = {\n", "        **ranges,\n", "        'valid_count': len(valid_coords),\n", "        'total_count': len(coordinates),\n", "        **crs_guess\n", "    }\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Detecting coordinate system...\n", "\n", "Coordinate System Detection Results:\n", "   Type: UTM\n", "   Confidence: high\n", "   Reason: coordinates_in_utm_range\n"]}], "source": ["# Detect coordinate system\n", "print(\"\\nDetecting coordinate system...\")\n", "coord_system_info = detect_coordinate_system(all_coordinates)\n", "\n", "print(f\"\\nCoordinate System Detection Results:\")\n", "print(f\"   Type: {coord_system_info.get('type', 'Unknown')}\")\n", "print(f\"   Confidence: {coord_system_info.get('confidence', 'Unknown')}\")\n", "print(f\"   Reason: {coord_system_info.get('reason', 'No reason provided')}\")\n", "if 'utm_zone_estimate' in coord_system_info:\n", "    print(f\"   UTM Zone: {coord_system_info['utm_zone_estimate']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Statistical Analysis and Valid Range Detection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Calculating valid coordinate ranges...\n", "\n", "Valid Range Detection Results:\n", "   Method: iqr_with_buffer\n", "   X range: 706094.93 to 709157.78\n", "   Y range: 4692702.69 to 4693259.76\n", "   X outliers detected: 83\n", "   Y outliers detected: 66\n", "   Total valid coordinates: 634\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "def _filter_valid_coords(coords):\n", "    df = pd.<PERSON><PERSON><PERSON><PERSON>(coords)\n", "    return df[\n", "        (df['x'] != 0.0) & \n", "        (df['y'] != 0.0) & \n", "        (df['x'].abs() > 1) & \n", "        (df['y'].abs() > 1)\n", "    ]\n", "\n", "def _iqr_bounds(data):\n", "    q1 = np.percentile(data, 25)\n", "    q3 = np.percentile(data, 75)\n", "    iqr = q3 - q1\n", "    return q1 - 1.5 * iqr, q3 + 1.5 * iqr\n", "\n", "def _add_buffer(min_val, max_val, buffer_pct):\n", "    span = max_val - min_val\n", "    return min_val - span * buffer_pct / 100, max_val + span * buffer_pct / 100\n", "\n", "def calculate_valid_ranges(coordinates, method='iqr', buffer_percent=5.0):\n", "    if not coordinates:\n", "        return None\n", "\n", "    valid_coords = _filter_valid_coords(coordinates)\n", "    if len(valid_coords) < 10:\n", "        return None\n", "\n", "    x = valid_coords['x'].values\n", "    y = valid_coords['y'].values\n", "\n", "    if method == 'iqr':\n", "        x_min_iqr, x_max_iqr = _iqr_bounds(x)\n", "        y_min_iqr, y_max_iqr = _iqr_bounds(y)\n", "\n", "        x_min, x_max = _add_buffer(x_min_iqr, x_max_iqr, buffer_percent)\n", "        y_min, y_max = _add_buffer(y_min_iqr, y_max_iqr, buffer_percent)\n", "\n", "        x_outliers = np.sum((x < x_min_iqr) | (x > x_max_iqr))\n", "        y_outliers = np.sum((y < y_min_iqr) | (y > y_max_iqr))\n", "\n", "        return {\n", "            'method': 'iqr_with_buffer',\n", "            'x_min': x_min,\n", "            'x_max': x_max,\n", "            'y_min': y_min,\n", "            'y_max': y_max,\n", "            'x_outliers_detected': int(x_outliers),\n", "            'y_outliers_detected': int(y_outliers),\n", "            'total_valid_coords': len(valid_coords),\n", "            'buffer_percent': buffer_percent\n", "        }\n", "\n", "# Calculate valid ranges\n", "print(\"Calculating valid coordinate ranges...\")\n", "valid_ranges = calculate_valid_ranges(all_coordinates)\n", "\n", "if valid_ranges:\n", "    print(f\"\\nValid Range Detection Results:\")\n", "    print(f\"   Method: {valid_ranges['method']}\")\n", "    print(f\"   X range: {valid_ranges['x_min']:.2f} to {valid_ranges['x_max']:.2f}\")\n", "    print(f\"   Y range: {valid_ranges['y_min']:.2f} to {valid_ranges['y_max']:.2f}\")\n", "    print(f\"   X outliers detected: {valid_ranges['x_outliers_detected']}\")\n", "    print(f\"   Y outliers detected: {valid_ranges['y_outliers_detected']}\")\n", "    print(f\"   Total valid coordinates: {valid_ranges['total_valid_coords']}\")\n", "else:\n", "    print(\"Could not calculate valid ranges\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Create Site Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output directory created: ../../../output_runs/coordinate_analysis/coordinate_analysis_20250701_160921\n", "Configuration saved: site_coordinate_config_20250701_160921.json\n"]}], "source": ["from datetime import datetime\n", "import json\n", "from pathlib import Path\n", "\n", "# Set output paths\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "run_dir = Path(output_dir) / f\"coordinate_analysis_{timestamp}\"\n", "run_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"Output directory created: {run_dir}\")\n", "\n", "# Build config dictionary\n", "site_config = {\n", "    \"analysis_info\": {\n", "        \"timestamp\": timestamp,\n", "        \"target_files\": target_files,\n", "        \"files_analyzed\": list(file_analysis.keys()),\n", "        \"total_coordinates_extracted\": len(all_coordinates)\n", "    },\n", "    \"coordinate_system\": coord_system_info,\n", "    \"valid_ranges\": valid_ranges,\n", "    \"file_analysis\": file_analysis,\n", "    \"usage_instructions\": {\n", "        \"for_cad_extraction\": \"Use valid_ranges to filter coordinates\",\n", "        \"for_alignment\": \"Use coordinate_system info for spatial reference\",\n", "        \"for_validation\": \"Validate coordinates using the defined ranges\"\n", "    }\n", "}\n", "\n", "# Save to JSON\n", "config_path = run_dir / f\"site_coordinate_config_{timestamp}.json\"\n", "with open(config_path, 'w') as f:\n", "    json.dump(site_config, f, indent=2, default=str)\n", "\n", "print(f\"Configuration saved: {config_path.name}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Files Analyzed:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Coordinates</th>\n", "      <th>entity_types</th>\n", "    </tr>\n", "    <tr>\n", "      <th>File</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf</th>\n", "      <td>634</td>\n", "      <td>{'HATCH_SCS_Fabbricato - Manufatto Stimato': 2...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                           Coordinates  \\\n", "File                                                     \n", "GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf          634   \n", "\n", "                                                                                entity_types  \n", "File                                                                                          \n", "GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf  {'HATCH_SCS_Fabbricato - Manufatto Stimato': 2...  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Coordinate System Detection:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Attribute</th>\n", "      <th>Value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Type</td>\n", "      <td>UTM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Confidence</td>\n", "      <td>high</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Reason</td>\n", "      <td>coordinates_in_utm_range</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Attribute                     Value\n", "0        Type                       UTM\n", "1  Confidence                      high\n", "2      Reason  coordinates_in_utm_range"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Valid Coordinate Ranges:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Metric</th>\n", "      <th>Value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>X Range Min</td>\n", "      <td>7.060949e+05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>X Range Max</td>\n", "      <td>7.091578e+05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Y Range Min</td>\n", "      <td>4.692703e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Y Range Max</td>\n", "      <td>4.693260e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>X Outliers</td>\n", "      <td>8.300000e+01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Y Outliers</td>\n", "      <td>6.600000e+01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Total Valid Coordinates</td>\n", "      <td>6.340000e+02</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    Metric         Value\n", "0              X Range Min  7.060949e+05\n", "1              X Range Max  7.091578e+05\n", "2              Y Range Min  4.692703e+06\n", "3              Y Range Max  4.693260e+06\n", "4               X Outliers  8.300000e+01\n", "5               Y Outliers  6.600000e+01\n", "6  Total Valid Coordinates  6.340000e+02"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# FILE ANALYSIS SUMMARY\n", "files_df = pd.DataFrame.from_dict(file_analysis, orient='index')\n", "files_df.index.name = \"File\"\n", "files_df = files_df.rename(columns={'coordinate_count': 'Coordinates'})\n", "print(\"\\nFiles Analyzed:\")\n", "display(files_df)\n", "\n", "# COORDINATE SYSTEM DETECTION SUMMARY\n", "cs_df = pd.DataFrame({\n", "    \"Attribute\": [\"Type\", \"Confidence\", \"Reason\"],\n", "    \"Value\": [\n", "        coord_system_info.get(\"type\", \"Unknown\"),\n", "        coord_system_info.get(\"confidence\", \"Unknown\"),\n", "        coord_system_info.get(\"reason\", \"Not specified\")\n", "    ]\n", "})\n", "print(\"\\nCoordinate System Detection:\")\n", "display(cs_df)\n", "\n", "# VALID RANGE DETECTION SUMMARY\n", "if valid_ranges:\n", "    ranges_df = pd.DataFrame({\n", "        \"Metric\": [\n", "            \"X Range Min\", \"X Range Max\",\n", "            \"Y Range Min\", \"Y Range Max\",\n", "            \"X Outliers\", \"Y Outliers\",\n", "            \"Total Valid Coordinates\"\n", "        ],\n", "        \"Value\": [\n", "            valid_ranges['x_min'],\n", "            valid_ranges['x_max'],\n", "            valid_ranges['y_min'],\n", "            valid_ranges['y_max'],\n", "            valid_ranges['x_outliers_detected'],\n", "            valid_ranges['y_outliers_detected'],\n", "            valid_ranges['total_valid_coords']\n", "        ]\n", "    })\n", "    print(\"\\nValid Coordinate Ranges:\")\n", "    display(ranges_df)\n", "else:\n", "    print(\"\\nNo valid coordinate ranges available.\")\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}