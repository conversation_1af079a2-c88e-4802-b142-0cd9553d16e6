# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import ezdxf
import json
from datetime import datetime
from collections import defaultdict
import seaborn as sns
import logging
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

logger.info("Libraries imported successfully")
logger.info(f"Analysis started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Configuration
target_files = [
    "GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf"
    #"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg"
]

search_path = "../../../data/raw"
output_dir = "../../../output_runs/coordinate_analysis"

print("Configuration:")
print(f"-  Target files: {target_files}")
print(f"-  Search path: {search_path}")
print(f"-  Output directory: {output_dir}")

print("Finding CAD files...")

found_files = []
for target_file in target_files:
    result = !find {search_path} -name "{target_file}" 2>/dev/null
    if result:
        file_path = Path(result[0])
        found_files.append(file_path)
        print(f"Found: {file_path.name}")
    else:
        print(f"Not found: {target_file}")

print(f"\nFiles to analyze: {len(found_files)}")



# Quick ogrinfo analysis if available
ogrinfo_check = !which ogrinfo 2>/dev/null
if ogrinfo_check:
    print("Running ogrinfo analysis...")
    for file_path in found_files:
        if file_path.suffix.lower() == '.dxf':
            print(f"\n{file_path.name}:")
            !ogrinfo -so "{file_path}" | head -20
else:
    print("\nogrinfo not available - proceeding with Python analysis")

from collections import defaultdict
import ezdxf

def extract_point_coordinates(entity, layer):
    if hasattr(entity.dxf, 'location'):
        pt = entity.dxf.location
        return [{
            'x': pt.x, 'y': pt.y, 'z': pt.z,
            'entity_type': 'POINT',
            'layer': layer,
            'source': 'point_location'
        }]
    return []

def extract_insert_coordinates(entity, layer):
    if hasattr(entity.dxf, 'insert'):
        pt = entity.dxf.insert
        return [{
            'x': pt.x, 'y': pt.y, 'z': pt.z,
            'entity_type': 'INSERT',
            'layer': layer,
            'source': 'insert_point'
        }]
    return []

def extract_line_coordinates(entity, layer):
    if hasattr(entity.dxf, 'start') and hasattr(entity.dxf, 'end'):
        start = entity.dxf.start
        end = entity.dxf.end
        return [
            {'x': start.x, 'y': start.y, 'z': start.z,
             'entity_type': 'LINE', 'layer': layer, 'source': 'line_start'},
            {'x': end.x, 'y': end.y, 'z': end.z,
             'entity_type': 'LINE', 'layer': layer, 'source': 'line_end'}
        ]
    return []

def extract_polyline_center(entity, layer):
    try:
        bbox = entity.bbox()
        if bbox:
            x = (bbox[0].x + bbox[1].x) / 2
            y = (bbox[0].y + bbox[1].y) / 2
            z = (bbox[0].z + bbox[1].z) / 2
            return [{
                'x': x, 'y': y, 'z': z,
                'entity_type': entity.dxftype(),
                'layer': layer,
                'source': 'polyline_center'
            }]
    except:
        pass
    return []

def extract_all_coordinates(file_path):
    print(f"\nAnalyzing coordinates in: {file_path.name}")

    try:
        doc = ezdxf.readfile(file_path)
        modelspace = doc.modelspace()
    except Exception as e:
        print(f"   Error: {e}")
        return [], {}

    coordinates = []
    entity_info = defaultdict(int)

    for entity in modelspace:
        entity_type = entity.dxftype()
        layer = getattr(entity.dxf, 'layer', 'UNKNOWN')
        entity_info[f"{entity_type}_{layer}"] += 1

        if entity_type == 'POINT':
            coordinates += extract_point_coordinates(entity, layer)
        elif entity_type == 'INSERT':
            coordinates += extract_insert_coordinates(entity, layer)
        elif entity_type == 'LINE':
            coordinates += extract_line_coordinates(entity, layer)
        elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
            coordinates += extract_polyline_center(entity, layer)

    print(f"   Extracted {len(coordinates)} coordinate points")
    print(f"   Entity-layer types counted: {len(entity_info)}")

    return coordinates, dict(entity_info)

logger.info("Coordinate extraction functions are ready")

# Extract coordinates from all files
print("Extracting coordinates from all CAD files...")

all_coordinates = []
file_analysis = {}

for file_path in found_files:
    if file_path.suffix.lower() == '.dxf':  # Process DXF files
        coords, entity_info = extract_all_coordinates(file_path)
        
        # Add file source to each coordinate
        for coord in coords:
            coord['source_file'] = file_path.name
        
        all_coordinates.extend(coords)
        file_analysis[file_path.name] = {
            'coordinate_count': len(coords),
            'entity_types': entity_info
        }
    else:
        print(f"Skipping {file_path.name} (DWG - convert to DXF first)")

print(f"\nTotal coordinates extracted: {len(all_coordinates)}")
print(f"Files processed: {len(file_analysis)}")

import pandas as pd

def _filter_valid_coordinates(coordinates):
    df = pd.DataFrame(coordinates)
    return df[
        (df['x'] != 0.0) &
        (df['y'] != 0.0) &
        (df['x'].abs() > 1) &
        (df['y'].abs() > 1)
    ]

def _compute_ranges(df):
    x_min, x_max = df['x'].min(), df['x'].max()
    y_min, y_max = df['y'].min(), df['y'].max()
    return {
        'x_min': x_min, 'x_max': x_max, 'x_range': x_max - x_min,
        'y_min': y_min, 'y_max': y_max, 'y_range': y_max - y_min
    }

def _infer_crs_from_ranges(ranges):
    x_min, y_min = ranges['x_min'], ranges['y_min']
    x_range, y_range = ranges['x_range'], ranges['y_range']

    if 100_000 <= x_min <= 900_000 and 1_000_000 <= y_min <= 10_000_000:
        result = {'type': 'UTM', 'confidence': 'high', 'reason': 'coordinates_in_utm_range'}
        if 600_000 <= x_min <= 800_000:
            result['utm_zone_estimate'] = '32N or 33N (Europe)'
        return result

    elif -180 <= x_min <= 180 and -90 <= y_min <= 90:
        return {'type': 'Geographic', 'confidence': 'high', 'reason': 'coordinates_in_geographic_range'}

    elif x_range < 100_000 and y_range < 100_000:
        return {'type': 'Local', 'confidence': 'medium', 'reason': 'small_coordinate_ranges_suggest_local_system'}

    return {'type': 'Unknown', 'confidence': 'low', 'reason': 'coordinates_outside_known_patterns'}

def detect_coordinate_system(coordinates):
    if not coordinates:
        return {'type': 'Unknown', 'confidence': 'low'}

    valid_coords = _filter_valid_coordinates(coordinates)
    if valid_coords.empty:
        return {'type': 'Unknown', 'confidence': 'low', 'reason': 'no_valid_coordinates'}

    ranges = _compute_ranges(valid_coords)
    crs_guess = _infer_crs_from_ranges(ranges)

    result = {
        **ranges,
        'valid_count': len(valid_coords),
        'total_count': len(coordinates),
        **crs_guess
    }
    return result

# Detect coordinate system
print("\nDetecting coordinate system...")
coord_system_info = detect_coordinate_system(all_coordinates)

print(f"\nCoordinate System Detection Results:")
print(f"   Type: {coord_system_info.get('type', 'Unknown')}")
print(f"   Confidence: {coord_system_info.get('confidence', 'Unknown')}")
print(f"   Reason: {coord_system_info.get('reason', 'No reason provided')}")
if 'utm_zone_estimate' in coord_system_info:
    print(f"   UTM Zone: {coord_system_info['utm_zone_estimate']}")

import numpy as np
import pandas as pd

def _filter_valid_coords(coords):
    df = pd.DataFrame(coords)
    return df[
        (df['x'] != 0.0) & 
        (df['y'] != 0.0) & 
        (df['x'].abs() > 1) & 
        (df['y'].abs() > 1)
    ]

def _iqr_bounds(data):
    q1 = np.percentile(data, 25)
    q3 = np.percentile(data, 75)
    iqr = q3 - q1
    return q1 - 1.5 * iqr, q3 + 1.5 * iqr

def _add_buffer(min_val, max_val, buffer_pct):
    span = max_val - min_val
    return min_val - span * buffer_pct / 100, max_val + span * buffer_pct / 100

def calculate_valid_ranges(coordinates, method='iqr', buffer_percent=5.0):
    if not coordinates:
        return None

    valid_coords = _filter_valid_coords(coordinates)
    if len(valid_coords) < 10:
        return None

    x = valid_coords['x'].values
    y = valid_coords['y'].values

    if method == 'iqr':
        x_min_iqr, x_max_iqr = _iqr_bounds(x)
        y_min_iqr, y_max_iqr = _iqr_bounds(y)

        x_min, x_max = _add_buffer(x_min_iqr, x_max_iqr, buffer_percent)
        y_min, y_max = _add_buffer(y_min_iqr, y_max_iqr, buffer_percent)

        x_outliers = np.sum((x < x_min_iqr) | (x > x_max_iqr))
        y_outliers = np.sum((y < y_min_iqr) | (y > y_max_iqr))

        return {
            'method': 'iqr_with_buffer',
            'x_min': x_min,
            'x_max': x_max,
            'y_min': y_min,
            'y_max': y_max,
            'x_outliers_detected': int(x_outliers),
            'y_outliers_detected': int(y_outliers),
            'total_valid_coords': len(valid_coords),
            'buffer_percent': buffer_percent
        }

# Calculate valid ranges
print("Calculating valid coordinate ranges...")
valid_ranges = calculate_valid_ranges(all_coordinates)

if valid_ranges:
    print(f"\nValid Range Detection Results:")
    print(f"   Method: {valid_ranges['method']}")
    print(f"   X range: {valid_ranges['x_min']:.2f} to {valid_ranges['x_max']:.2f}")
    print(f"   Y range: {valid_ranges['y_min']:.2f} to {valid_ranges['y_max']:.2f}")
    print(f"   X outliers detected: {valid_ranges['x_outliers_detected']}")
    print(f"   Y outliers detected: {valid_ranges['y_outliers_detected']}")
    print(f"   Total valid coordinates: {valid_ranges['total_valid_coords']}")
else:
    print("Could not calculate valid ranges")

from datetime import datetime
import json
from pathlib import Path

# Set output paths
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
run_dir = Path(output_dir) / f"coordinate_analysis_{timestamp}"
run_dir.mkdir(parents=True, exist_ok=True)

print(f"Output directory created: {run_dir}")

# Build config dictionary
site_config = {
    "analysis_info": {
        "timestamp": timestamp,
        "target_files": target_files,
        "files_analyzed": list(file_analysis.keys()),
        "total_coordinates_extracted": len(all_coordinates)
    },
    "coordinate_system": coord_system_info,
    "valid_ranges": valid_ranges,
    "file_analysis": file_analysis,
    "usage_instructions": {
        "for_cad_extraction": "Use valid_ranges to filter coordinates",
        "for_alignment": "Use coordinate_system info for spatial reference",
        "for_validation": "Validate coordinates using the defined ranges"
    }
}

# Save to JSON
config_path = run_dir / f"site_coordinate_config_{timestamp}.json"
with open(config_path, 'w') as f:
    json.dump(site_config, f, indent=2, default=str)

print(f"Configuration saved: {config_path.name}")

# FILE ANALYSIS SUMMARY
files_df = pd.DataFrame.from_dict(file_analysis, orient='index')
files_df.index.name = "File"
files_df = files_df.rename(columns={'coordinate_count': 'Coordinates'})
print("\nFiles Analyzed:")
display(files_df)

# COORDINATE SYSTEM DETECTION SUMMARY
cs_df = pd.DataFrame({
    "Attribute": ["Type", "Confidence", "Reason"],
    "Value": [
        coord_system_info.get("type", "Unknown"),
        coord_system_info.get("confidence", "Unknown"),
        coord_system_info.get("reason", "Not specified")
    ]
})
print("\nCoordinate System Detection:")
display(cs_df)

# VALID RANGE DETECTION SUMMARY
if valid_ranges:
    ranges_df = pd.DataFrame({
        "Metric": [
            "X Range Min", "X Range Max",
            "Y Range Min", "Y Range Max",
            "X Outliers", "Y Outliers",
            "Total Valid Coordinates"
        ],
        "Value": [
            valid_ranges['x_min'],
            valid_ranges['x_max'],
            valid_ranges['y_min'],
            valid_ranges['y_max'],
            valid_ranges['x_outliers_detected'],
            valid_ranges['y_outliers_detected'],
            valid_ranges['total_valid_coords']
        ]
    })
    print("\nValid Coordinate Ranges:")
    display(ranges_df)
else:
    print("\nNo valid coordinate ranges available.")

