{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CAD Coordinate System Analysis\n", "\n", "This notebook analyzes CAD files to automatically detect coordinate systems and valid coordinate ranges.\n", "\n", "## Purpose:\n", "- **Detect coordinate system type** (UTM, Geographic, Local)\n", "- **Find valid coordinate ranges** using statistical analysis\n", "- **Generate site configuration** for other notebooks to use\n", "- **No hard-coding** - adapts to any site automatically\n", "\n", "## Outputs:\n", "- **site_coordinate_config.json**: Coordinate system info and valid ranges\n", "- **coordinate_analysis_report.txt**: Human-readable analysis report\n", "- **coordinate_distribution_plots.png**: Visual analysis of coordinate distribution\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Setup and Configuration"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Libraries imported successfully\n", "📅 Analysis started: 2025-07-01 15:51:38\n"]}], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import ezdxf\n", "import json\n", "from datetime import datetime\n", "from collections import defaultdict\n", "import seaborn as sns\n", "\n", "print(\"✅ Libraries imported successfully\")\n", "print(f\"📅 Analysis started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📋 Configuration:\n", "   Target files: ['GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf', 'GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg']\n", "   Search path: ../../../data/raw\n", "   Output directory: ../../../output_runs/coordinate_analysis\n"]}], "source": ["# Configuration\n", "target_files = [\n", "    \"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\",\n", "    \"GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg\"\n", "]\n", "\n", "search_path = \"../../../data/raw\"\n", "output_dir = \"../../../output_runs/coordinate_analysis\"\n", "\n", "print(\"📋 Configuration:\")\n", "print(f\"   Target files: {target_files}\")\n", "print(f\"   Search path: {search_path}\")\n", "print(f\"   Output directory: {output_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Find and Analyze CAD Files"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Finding CAD files...\n", "✅ Found: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\n", "✅ Found: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg\n", "\n", "📊 Files to analyze: 2\n", "\n", "🔧 Running ogrinfo analysis...\n", "\n", "📋 GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf:\n", "INFO: Open of `../../../data/raw/motali_de_castro/cad/GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf'\n", "      using driver `DXF' successful.\n", "1: entities\n"]}], "source": ["print(\"🔍 Finding CAD files...\")\n", "\n", "found_files = []\n", "for target_file in target_files:\n", "    result = !find {search_path} -name \"{target_file}\" 2>/dev/null\n", "    if result:\n", "        file_path = Path(result[0])\n", "        found_files.append(file_path)\n", "        print(f\"✅ Found: {file_path.name}\")\n", "    else:\n", "        print(f\"❌ Not found: {target_file}\")\n", "\n", "print(f\"\\n📊 Files to analyze: {len(found_files)}\")\n", "\n", "# Quick ogrinfo analysis if available\n", "ogrinfo_check = !which ogrinfo 2>/dev/null\n", "if ogrinfo_check:\n", "    print(\"\\n🔧 Running ogrinfo analysis...\")\n", "    for file_path in found_files:\n", "        if file_path.suffix.lower() == '.dxf':\n", "            print(f\"\\n📋 {file_path.name}:\")\n", "            !ogrinfo -so \"{file_path}\" | head -20\n", "else:\n", "    print(\"\\nℹ️ ogrinfo not available - proceeding with Python analysis\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Extract All Coordinates for Analysis"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 Coordinate extraction function ready\n"]}], "source": ["def extract_all_coordinates(file_path):\n", "    \"\"\"\n", "    Extract ALL coordinates from a CAD file for coordinate system analysis.\n", "    No filtering - we want to see the full distribution.\n", "    \"\"\"\n", "    print(f\"\\n📊 Analyzing coordinates in: {file_path.name}\")\n", "    \n", "    try:\n", "        doc = ezdxf.readfile(file_path)\n", "        modelspace = doc.modelspace()\n", "        \n", "        coordinates = []\n", "        entity_info = defaultdict(int)\n", "        \n", "        for entity in modelspace:\n", "            entity_type = entity.dxftype()\n", "            layer = getattr(entity.dxf, 'layer', 'UNKNOWN')\n", "            entity_info[f\"{entity_type}_{layer}\"] += 1\n", "            \n", "            # Extract coordinates from different entity types\n", "            if entity_type == 'POINT':\n", "                if hasattr(entity.dxf, 'location'):\n", "                    pt = entity.dxf.location\n", "                    coordinates.append({\n", "                        'x': pt.x, 'y': pt.y, 'z': pt.z,\n", "                        'entity_type': entity_type,\n", "                        'layer': layer,\n", "                        'source': 'point_location'\n", "                    })\n", "            \n", "            elif entity_type == 'INSERT':\n", "                if hasattr(entity.dxf, 'insert'):\n", "                    pt = entity.dxf.insert\n", "                    coordinates.append({\n", "                        'x': pt.x, 'y': pt.y, 'z': pt.z,\n", "                        'entity_type': entity_type,\n", "                        'layer': layer,\n", "                        'source': 'insert_point'\n", "                    })\n", "            \n", "            elif entity_type == 'LINE':\n", "                if hasattr(entity.dxf, 'start') and hasattr(entity.dxf, 'end'):\n", "                    start = entity.dxf.start\n", "                    end = entity.dxf.end\n", "                    coordinates.extend([\n", "                        {'x': start.x, 'y': start.y, 'z': start.z,\n", "                         'entity_type': entity_type, 'layer': layer, 'source': 'line_start'},\n", "                        {'x': end.x, 'y': end.y, 'z': end.z,\n", "                         'entity_type': entity_type, 'layer': layer, 'source': 'line_end'}\n", "                    ])\n", "            \n", "            elif entity_type in ['LWPOLYLINE', 'POLYLINE']:\n", "                try:\n", "                    # Get bounding box center\n", "                    bbox = entity.bbox()\n", "                    if bbox:\n", "                        center_x = (bbox[0].x + bbox[1].x) / 2\n", "                        center_y = (bbox[0].y + bbox[1].y) / 2\n", "                        center_z = (bbox[0].z + bbox[1].z) / 2\n", "                        coordinates.append({\n", "                            'x': center_x, 'y': center_y, 'z': center_z,\n", "                            'entity_type': entity_type,\n", "                            'layer': layer,\n", "                            'source': 'polyline_center'\n", "                        })\n", "                except:\n", "                    pass\n", "        \n", "        print(f\"   📍 Extracted {len(coordinates)} coordinate points\")\n", "        print(f\"   🏗️ Entity types found: {len(entity_info)}\")\n", "        \n", "        return coordinates, dict(entity_info)\n", "        \n", "    except Exception as e:\n", "        print(f\"   ❌ Error: {e}\")\n", "        return [], {}\n", "\n", "print(\"🔧 Coordinate extraction function ready\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Extracting coordinates from all CAD files...\n", "\n", "📊 Analyzing coordinates in: GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dxf\n", "   📍 Extracted 634 coordinate points\n", "   🏗️ Entity types found: 70\n", "⏭️ Skipping GRE.EEC.R.00.IT.P.12645.00.133.00_MAP.dwg (DWG - convert to DXF first)\n", "\n", "📊 Total coordinates extracted: 634\n", "📁 Files processed: 1\n"]}], "source": ["# Extract coordinates from all files\n", "print(\"🔍 Extracting coordinates from all CAD files...\")\n", "\n", "all_coordinates = []\n", "file_analysis = {}\n", "\n", "for file_path in found_files:\n", "    if file_path.suffix.lower() == '.dxf':  # Process DXF files\n", "        coords, entity_info = extract_all_coordinates(file_path)\n", "        \n", "        # Add file source to each coordinate\n", "        for coord in coords:\n", "            coord['source_file'] = file_path.name\n", "        \n", "        all_coordinates.extend(coords)\n", "        file_analysis[file_path.name] = {\n", "            'coordinate_count': len(coords),\n", "            'entity_types': entity_info\n", "        }\n", "    else:\n", "        print(f\"⏭️ Skipping {file_path.name} (DWG - convert to DXF first)\")\n", "\n", "print(f\"\\n📊 Total coordinates extracted: {len(all_coordinates)}\")\n", "print(f\"📁 Files processed: {len(file_analysis)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Coordinate System Detection"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 Detecting coordinate system...\n", "📊 Coordinate analysis:\n", "   Valid coordinates: 634 / 634\n", "   X range: 352456.35 to 1317413.28 (span: 964956.93)\n", "   Y range: 4581497.52 to 5748421.94 (span: 1166924.42)\n", "\n", "📍 Coordinate System Detection Results:\n", "   Type: UTM\n", "   Confidence: high\n", "   Reason: coordinates_in_utm_range\n"]}], "source": ["def detect_coordinate_system(coordinates):\n", "    \"\"\"\n", "    Automatically detect coordinate system type based on coordinate ranges.\n", "    \"\"\"\n", "    if not coordinates:\n", "        return {'type': 'unknown', 'confidence': 'low'}\n", "    \n", "    # Convert to numpy for analysis\n", "    coords_df = pd.DataFrame(coordinates)\n", "    \n", "    # Remove obvious invalid coordinates\n", "    valid_coords = coords_df[\n", "        (coords_df['x'] != 0.0) & \n", "        (coords_df['y'] != 0.0) & \n", "        (coords_df['x'].abs() > 1) & \n", "        (coords_df['y'].abs() > 1)\n", "    ]\n", "    \n", "    if len(valid_coords) == 0:\n", "        return {'type': 'unknown', 'confidence': 'low', 'reason': 'no_valid_coordinates'}\n", "    \n", "    x_min, x_max = valid_coords['x'].min(), valid_coords['x'].max()\n", "    y_min, y_max = valid_coords['y'].min(), valid_coords['y'].max()\n", "    \n", "    x_range = x_max - x_min\n", "    y_range = y_max - y_min\n", "    \n", "    print(f\"📊 Coordinate analysis:\")\n", "    print(f\"   Valid coordinates: {len(valid_coords)} / {len(coordinates)}\")\n", "    print(f\"   X range: {x_min:.2f} to {x_max:.2f} (span: {x_range:.2f})\")\n", "    print(f\"   Y range: {y_min:.2f} to {y_max:.2f} (span: {y_range:.2f})\")\n", "    \n", "    # Detection logic\n", "    detection_result = {\n", "        'x_min': x_min, 'x_max': x_max, 'x_range': x_range,\n", "        'y_min': y_min, 'y_max': y_max, 'y_range': y_range,\n", "        'valid_count': len(valid_coords),\n", "        'total_count': len(coordinates)\n", "    }\n", "    \n", "    # UTM Detection (typical ranges)\n", "    if (100000 <= x_min <= 900000 and 1000000 <= y_min <= 10000000):\n", "        detection_result.update({\n", "            'type': 'UTM',\n", "            'confidence': 'high',\n", "            'reason': 'coordinates_in_utm_range'\n", "        })\n", "        \n", "        # Try to detect UTM zone\n", "        if 600000 <= x_min <= 800000:\n", "            detection_result['utm_zone_estimate'] = '32N or 33N (Europe)'\n", "    \n", "    # Geographic/Decimal Degrees Detection\n", "    elif (-180 <= x_min <= 180 and -90 <= y_min <= 90):\n", "        detection_result.update({\n", "            'type': 'Geographic',\n", "            'confidence': 'high',\n", "            'reason': 'coordinates_in_geographic_range'\n", "        })\n", "    \n", "    # Local/Project Coordinate System\n", "    elif (x_range < 100000 and y_range < 100000):\n", "        detection_result.update({\n", "            'type': 'Local',\n", "            'confidence': 'medium',\n", "            'reason': 'small_coordinate_ranges_suggest_local_system'\n", "        })\n", "    \n", "    # Unknown\n", "    else:\n", "        detection_result.update({\n", "            'type': 'Unknown',\n", "            'confidence': 'low',\n", "            'reason': 'coordinates_outside_known_patterns'\n", "        })\n", "    \n", "    return detection_result\n", "\n", "# Detect coordinate system\n", "print(\"\\n🔍 Detecting coordinate system...\")\n", "coord_system_info = detect_coordinate_system(all_coordinates)\n", "\n", "print(f\"\\n📍 Coordinate System Detection Results:\")\n", "print(f\"   Type: {coord_system_info.get('type', 'Unknown')}\")\n", "print(f\"   Confidence: {coord_system_info.get('confidence', 'Unknown')}\")\n", "print(f\"   Reason: {coord_system_info.get('reason', 'No reason provided')}\")\n", "if 'utm_zone_estimate' in coord_system_info:\n", "    print(f\"   UTM Zone: {coord_system_info['utm_zone_estimate']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Statistical Analysis and Valid Range Detection"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 Calculating valid coordinate ranges...\n", "\n", "📍 Valid Range Detection Results:\n", "   Method: iqr_with_buffer\n", "   X range: 706094.93 to 709157.78\n", "   Y range: 4692702.69 to 4693259.76\n", "   X outliers detected: 83\n", "   Y outliers detected: 66\n", "   Total valid coordinates: 634\n"]}], "source": ["def calculate_valid_ranges(coordinates, method='iqr'):\n", "    \"\"\"\n", "    Calculate valid coordinate ranges using statistical methods.\n", "    \"\"\"\n", "    if not coordinates:\n", "        return None\n", "    \n", "    coords_df = pd.DataFrame(coordinates)\n", "    \n", "    # Remove obvious invalid coordinates\n", "    valid_coords = coords_df[\n", "        (coords_df['x'] != 0.0) & \n", "        (coords_df['y'] != 0.0) & \n", "        (coords_df['x'].abs() > 1) & \n", "        (coords_df['y'].abs() > 1)\n", "    ]\n", "    \n", "    if len(valid_coords) < 10:\n", "        print(\"⚠️ Too few valid coordinates for statistical analysis\")\n", "        return None\n", "    \n", "    x_coords = valid_coords['x'].values\n", "    y_coords = valid_coords['y'].values\n", "    \n", "    if method == 'iqr':\n", "        # Interquartile Range method (robust to outliers)\n", "        def iqr_bounds(data):\n", "            Q1 = np.percentile(data, 25)\n", "            Q3 = np.percentile(data, 75)\n", "            IQR = Q3 - Q1\n", "            lower = Q1 - 1.5 * IQR\n", "            upper = Q3 + 1.5 * IQR\n", "            return lower, upper\n", "        \n", "        x_min_iqr, x_max_iqr = iqr_bounds(x_coords)\n", "        y_min_iqr, y_max_iqr = iqr_bounds(y_coords)\n", "        \n", "        # Add 5% buffer for safety\n", "        x_range = x_max_iqr - x_min_iqr\n", "        y_range = y_max_iqr - y_min_iqr\n", "        \n", "        x_min_final = x_min_iqr - 0.05 * x_range\n", "        x_max_final = x_max_iqr + 0.05 * x_range\n", "        y_min_final = y_min_iqr - 0.05 * y_range\n", "        y_max_final = y_max_iqr + 0.05 * y_range\n", "        \n", "        # Count outliers\n", "        x_outliers = np.sum((x_coords < x_min_iqr) | (x_coords > x_max_iqr))\n", "        y_outliers = np.sum((y_coords < y_min_iqr) | (y_coords > y_max_iqr))\n", "        \n", "        return {\n", "            'method': 'iqr_with_buffer',\n", "            'x_min': x_min_final,\n", "            'x_max': x_max_final,\n", "            'y_min': y_min_final,\n", "            'y_max': y_max_final,\n", "            'x_outliers_detected': int(x_outliers),\n", "            'y_outliers_detected': int(y_outliers),\n", "            'total_valid_coords': len(valid_coords),\n", "            'buffer_percent': 5.0\n", "        }\n", "\n", "# Calculate valid ranges\n", "print(\"\\n📊 Calculating valid coordinate ranges...\")\n", "valid_ranges = calculate_valid_ranges(all_coordinates)\n", "\n", "if valid_ranges:\n", "    print(f\"\\n📍 Valid Range Detection Results:\")\n", "    print(f\"   Method: {valid_ranges['method']}\")\n", "    print(f\"   X range: {valid_ranges['x_min']:.2f} to {valid_ranges['x_max']:.2f}\")\n", "    print(f\"   Y range: {valid_ranges['y_min']:.2f} to {valid_ranges['y_max']:.2f}\")\n", "    print(f\"   X outliers detected: {valid_ranges['x_outliers_detected']}\")\n", "    print(f\"   Y outliers detected: {valid_ranges['y_outliers_detected']}\")\n", "    print(f\"   Total valid coordinates: {valid_ranges['total_valid_coords']}\")\n", "else:\n", "    print(\"❌ Could not calculate valid ranges\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Create Site Configuration"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💾 Creating site configuration...\n", "📁 Output directory: ../../../output_runs/coordinate_analysis/coordinate_analysis_20250701_155139\n", "✅ Site configuration saved: site_coordinate_config_20250701_155139.json\n", "✅ Analysis report saved: coordinate_analysis_report_20250701_155139.txt\n", "\n", "🎯 COORDINATE SYSTEM ANALYSIS COMPLETE!\n", "📋 Next steps:\n", "   1. Use site_coordinate_config_20250701_155139.json in other notebooks\n", "   2. Apply coordinate filtering using detected ranges\n", "   3. Reference coordinate system type for alignment\n", "\n", "📅 Analysis completed: 2025-07-01 15:51:39\n"]}], "source": ["# Create comprehensive site configuration\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_path = Path(output_dir)\n", "run_output_dir = output_path / f\"coordinate_analysis_{timestamp}\"\n", "run_output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"💾 Creating site configuration...\")\n", "print(f\"📁 Output directory: {run_output_dir}\")\n", "\n", "# Build comprehensive configuration\n", "site_config = {\n", "    'analysis_info': {\n", "        'timestamp': timestamp,\n", "        'target_files': target_files,\n", "        'files_analyzed': list(file_analysis.keys()),\n", "        'total_coordinates_extracted': len(all_coordinates)\n", "    },\n", "    'coordinate_system': coord_system_info,\n", "    'valid_ranges': valid_ranges,\n", "    'file_analysis': file_analysis,\n", "    'usage_instructions': {\n", "        'for_cad_extraction': 'Use valid_ranges to filter coordinates during extraction',\n", "        'for_alignment': 'Use coordinate_system info to understand coordinate reference',\n", "        'for_validation': 'Use valid_ranges to validate extracted coordinates'\n", "    }\n", "}\n", "\n", "# Save site configuration\n", "config_file = run_output_dir / f\"site_coordinate_config_{timestamp}.json\"\n", "with open(config_file, 'w') as f:\n", "    json.dump(site_config, f, indent=2, default=str)\n", "\n", "print(f\"✅ Site configuration saved: {config_file.name}\")\n", "\n", "# Create human-readable report\n", "report_file = run_output_dir / f\"coordinate_analysis_report_{timestamp}.txt\"\n", "with open(report_file, 'w') as f:\n", "    f.write(f\"CAD Coordinate System Analysis Report\\n\")\n", "    f.write(f\"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\")\n", "    f.write(f\"=\" * 50 + \"\\n\\n\")\n", "    \n", "    f.write(f\"FILES ANALYZED:\\n\")\n", "    for filename, info in file_analysis.items():\n", "        f.write(f\"  - {filename}: {info['coordinate_count']} coordinates\\n\")\n", "    \n", "    f.write(f\"\\nCOORDINATE SYSTEM DETECTION:\\n\")\n", "    f.write(f\"  Type: {coord_system_info.get('type', 'Unknown')}\\n\")\n", "    f.write(f\"  Confidence: {coord_system_info.get('confidence', 'Unknown')}\\n\")\n", "    f.write(f\"  Reason: {coord_system_info.get('reason', 'No reason')}\\n\")\n", "    \n", "    if valid_ranges:\n", "        f.write(f\"\\nVALID COORDINATE RANGES:\\n\")\n", "        f.write(f\"  X: {valid_ranges['x_min']:.2f} to {valid_ranges['x_max']:.2f}\\n\")\n", "        f.write(f\"  Y: {valid_ranges['y_min']:.2f} to {valid_ranges['y_max']:.2f}\\n\")\n", "        f.write(f\"  Outliers removed: {valid_ranges['x_outliers_detected']} (X), {valid_ranges['y_outliers_detected']} (Y)\\n\")\n", "    \n", "    f.write(f\"\\nUSAGE RECOMMENDATIONS:\\n\")\n", "    f.write(f\"  1. Use this configuration in CAD extraction notebooks\\n\")\n", "    f.write(f\"  2. Apply valid_ranges for coordinate filtering\\n\")\n", "    f.write(f\"  3. Reference coordinate_system type for alignment workflows\\n\")\n", "\n", "print(f\"✅ Analysis report saved: {report_file.name}\")\n", "\n", "print(f\"\\n🎯 COORDINATE SYSTEM ANALYSIS COMPLETE!\")\n", "print(f\"📋 Next steps:\")\n", "print(f\"   1. Use {config_file.name} in other notebooks\")\n", "print(f\"   2. Apply coordinate filtering using detected ranges\")\n", "print(f\"   3. Reference coordinate system type for alignment\")\n", "\n", "print(f\"\\n📅 Analysis completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}