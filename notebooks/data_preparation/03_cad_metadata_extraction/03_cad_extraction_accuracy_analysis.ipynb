# Papermill parameters
project_type = "motali_de_castro"
site_name = "main_site"
reference_file = "../../../data/reference/castro_cad.csv"
ground_truth_file = "../../../data/reference/montalto_di_castro_ground_truth.csv"
extracted_file = "../../../data/raw/motali_de_castro/cad/enhanced_output/cad_extraction_pile_20250630_143601.csv"
output_dir = "../../../output_runs/validation"

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from datetime import datetime
import json
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# Set up plotting
plt.style.use('default')
sns.set_palette("husl")

print(f"CAD Extraction Accuracy Analysis - {project_type.title()}")
print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print("=" * 60)

# Load reference data (castro_cad.csv)
reference_df = pd.read_csv(reference_file)
print(f"Reference data loaded: {len(reference_df)} entities")
print(f"Reference columns: {list(reference_df.columns)}")
print(f"\nReference data sample:")
display(reference_df.head())

# Analyze reference data patterns
print(f"\nReference data analysis:")
print(f"Unique names: {reference_df['Name'].nunique()}")
print(f"Name patterns: {reference_df['Name'].value_counts().head(10)}")

# Load extracted CAD data
extracted_df = pd.read_csv(extracted_file)
print(f"Extracted data loaded: {len(extracted_df)} entities")
print(f"Extracted columns: {list(extracted_df.columns)}")
print(f"\nExtracted data sample:")
display(extracted_df.head())

# Analyze extracted data patterns
print(f"\nExtracted data analysis:")
print(f"Entity types: {extracted_df['entity_type'].value_counts()}")
print(f"\nLayer names: {extracted_df['layer_name'].value_counts().head(10)}")
print(f"\nClassifications: {extracted_df['classification'].value_counts()}")

# Load ground truth specifications
ground_truth_df = pd.read_csv(ground_truth_file)
print(f"Ground truth loaded: {len(ground_truth_df)} specifications")
display(ground_truth_df)

# Create ground truth lookup
ground_truth = {}
for _, row in ground_truth_df.iterrows():
    key = row['Features'].lower().replace(' ', '_')
    ground_truth[key] = {
        'planned': row['Planned'],
        'unit': row['Unit'],
        'category': row['Categories'],
        'notes': row.get('Notes', '')
    }

print(f"\nKey ground truth targets:")
for key, value in ground_truth.items():
    if key in ['piling', 'tracker', 'modules']:
        print(f"  {key}: {value['planned']} {value['unit']}")

# Compare coordinate systems and ranges
print("COORDINATE SYSTEM COMPARISON")
print("=" * 40)

# Reference coordinates
ref_x_range = (reference_df['X'].min(), reference_df['X'].max())
ref_y_range = (reference_df['Y'].min(), reference_df['Y'].max())

print(f"Reference data (castro_cad.csv):")
print(f"  X range: {ref_x_range[0]:.2f} to {ref_x_range[1]:.2f}")
print(f"  Y range: {ref_y_range[0]:.2f} to {ref_y_range[1]:.2f}")
print(f"  Coordinate system: Appears to be UTM (7-digit coordinates)")

# Extracted coordinates
ext_x_range = (extracted_df['x_coord'].min(), extracted_df['x_coord'].max())
ext_y_range = (extracted_df['y_coord'].min(), extracted_df['y_coord'].max())

print(f"\nExtracted data:")
print(f"  X range: {ext_x_range[0]:.2f} to {ext_x_range[1]:.2f}")
print(f"  Y range: {ext_y_range[0]:.2f} to {ext_y_range[1]:.2f}")
print(f"  Coordinate system: Mixed (some very large values suggest different CRS)")

# Check for coordinate overlap
x_overlap = (max(ref_x_range[0], ext_x_range[0]), min(ref_x_range[1], ext_x_range[1]))
y_overlap = (max(ref_y_range[0], ext_y_range[0]), min(ref_y_range[1], ext_y_range[1]))

print(f"\nCoordinate overlap analysis:")
if x_overlap[0] <= x_overlap[1] and y_overlap[0] <= y_overlap[1]:
    print(f"  ✅ Coordinate systems have overlap")
    print(f"  X overlap: {x_overlap[0]:.2f} to {x_overlap[1]:.2f}")
    print(f"  Y overlap: {y_overlap[0]:.2f} to {y_overlap[1]:.2f}")
else:
    print(f"  ❌ No coordinate overlap - different coordinate systems")
    print(f"  This indicates a coordinate transformation issue")

# Create spatial distribution plots
fig, axes = plt.subplots(1, 2, figsize=(15, 6))

# Reference data spatial distribution
axes[0].scatter(reference_df['X'], reference_df['Y'], alpha=0.6, s=20, c='blue')
axes[0].set_title('Reference Data Spatial Distribution\n(castro_cad.csv)')
axes[0].set_xlabel('X Coordinate')
axes[0].set_ylabel('Y Coordinate')
axes[0].grid(True, alpha=0.3)
axes[0].ticklabel_format(style='scientific', axis='both', scilimits=(0,0))

# Filter extracted data to reasonable coordinate range for visualization
extracted_filtered = extracted_df[
    (extracted_df['x_coord'] > 0) & 
    (extracted_df['x_coord'] < 1e7) &
    (extracted_df['y_coord'] > 0) & 
    (extracted_df['y_coord'] < 1e7)
]

# Extracted data spatial distribution
axes[1].scatter(extracted_filtered['x_coord'], extracted_filtered['y_coord'], 
               alpha=0.6, s=20, c='red')
axes[1].set_title('Extracted Data Spatial Distribution\n(Filtered for reasonable coordinates)')
axes[1].set_xlabel('X Coordinate')
axes[1].set_ylabel('Y Coordinate')
axes[1].grid(True, alpha=0.3)
axes[1].ticklabel_format(style='scientific', axis='both', scilimits=(0,0))

plt.tight_layout()
plt.show()

print(f"Spatial distribution analysis:")
print(f"  Reference points: {len(reference_df)}")
print(f"  Extracted points (filtered): {len(extracted_filtered)}")
print(f"  Extracted points (total): {len(extracted_df)}")
print(f"  Coordinate filtering removed: {len(extracted_df) - len(extracted_filtered)} points")

# Analyze reference data patterns
print("REFERENCE DATA PATTERN ANALYSIS")
print("=" * 40)

# Extract pattern information from reference names
reference_patterns = defaultdict(int)
for name in reference_df['Name']:
    if 'TRJ-52' in name:
        if 'INT' in name:
            reference_patterns['TRJ-52-INT'] += 1
        elif 'EXT' in name:
            reference_patterns['TRJ-52-EXT'] += 1
        elif 'EDGE' in name:
            reference_patterns['TRJ-52-EDGE'] += 1
    elif 'TRJ-26' in name:
        if 'INT' in name:
            reference_patterns['TRJ-26-INT'] += 1
        elif 'EXT' in name:
            reference_patterns['TRJ-26-EXT'] += 1

print("Reference data patterns:")
for pattern, count in sorted(reference_patterns.items()):
    print(f"  {pattern}: {count}")

total_ref_trackers = sum(reference_patterns.values())
print(f"\nTotal reference tracker-related entities: {total_ref_trackers}")

# Analyze extracted data patterns
print("EXTRACTED DATA PATTERN ANALYSIS")
print("=" * 40)

# Analyze tracker-related extractions
tracker_layers = extracted_df[extracted_df['layer_name'].str.contains('Tracker', na=False)]
tracker_patterns = tracker_layers['layer_name'].value_counts()

print("Extracted tracker patterns:")
for pattern, count in tracker_patterns.items():
    print(f"  {pattern}: {count}")

print(f"\nTotal extracted tracker entities: {len(tracker_layers)}")

# Analyze pile-related extractions
pile_layers = extracted_df[extracted_df['layer_name'].str.contains('PILE', na=False)]
pile_patterns = pile_layers['layer_name'].value_counts()

print(f"\nExtracted pile patterns:")
for pattern, count in pile_patterns.items():
    print(f"  {pattern}: {count}")

print(f"\nTotal extracted pile entities: {len(pile_layers)}")

# Calculate accuracy metrics
print("ACCURACY ASSESSMENT")
print("=" * 40)

# Tracker accuracy
expected_trackers = ground_truth['tracker']['planned']
reference_trackers = total_ref_trackers
extracted_trackers = len(tracker_layers)

print(f"Tracker Detection Accuracy:")
print(f"  Expected (ground truth): {expected_trackers}")
print(f"  Reference (castro_cad.csv): {reference_trackers}")
print(f"  Extracted (automated): {extracted_trackers}")
print(f"  Reference vs Ground Truth: {(reference_trackers/expected_trackers)*100:.1f}%")
print(f"  Extracted vs Ground Truth: {(extracted_trackers/expected_trackers)*100:.1f}%")
print(f"  Extracted vs Reference: {(extracted_trackers/reference_trackers)*100:.1f}%")

# Foundation pile accuracy
expected_piles = ground_truth['piling']['planned']
extracted_piles = len(pile_layers)

print(f"\nFoundation Pile Detection Accuracy:")
print(f"  Expected (ground truth): {expected_piles}")
print(f"  Extracted (automated): {extracted_piles}")
print(f"  Extracted vs Ground Truth: {(extracted_piles/expected_piles)*100:.1f}%")

# Overall entity detection
print(f"\nOverall Entity Detection:")
print(f"  Reference entities: {len(reference_df)}")
print(f"  Extracted entities: {len(extracted_df)}")
print(f"  Extraction ratio: {(len(extracted_df)/len(reference_df))*100:.1f}%")

# Create detailed comparison table
comparison_data = {
    'Infrastructure Type': ['Trackers', 'Foundation Piles', 'Module Support Piles', 'Roads', 'Fencing', 'Electrical'],
    'Ground Truth': [543, 4199, 23764, 3220.93, 3873.1, 2047],
    'Reference (castro_cad.csv)': [total_ref_trackers, 0, 0, 0, 0, 0],
    'Extracted (Automated)': [extracted_trackers, extracted_piles, 23972, 0, 0, 0],
    'Unit': ['#', '#', '#', 'meters', 'meters', 'meters']
}

comparison_df = pd.DataFrame(comparison_data)

# Calculate accuracy percentages
comparison_df['Reference Accuracy (%)'] = (
    comparison_df['Reference (castro_cad.csv)'] / comparison_df['Ground Truth'] * 100
).round(1)

comparison_df['Extraction Accuracy (%)'] = (
    comparison_df['Extracted (Automated)'] / comparison_df['Ground Truth'] * 100
).round(1)

print("DETAILED ACCURACY COMPARISON")
print("=" * 60)
display(comparison_df)

# Identify critical gaps
print("\nCRITICAL GAPS IDENTIFIED:")
print("=" * 30)

gaps = []
for idx, row in comparison_df.iterrows():
    if row['Extraction Accuracy (%)'] < 50:
        gaps.append({
            'type': row['Infrastructure Type'],
            'accuracy': row['Extraction Accuracy (%)'],
            'missing': row['Ground Truth'] - row['Extracted (Automated)']
        })

for gap in gaps:
    print(f"❌ {gap['type']}: {gap['accuracy']:.1f}% accuracy, missing {gap['missing']:.0f} entities")

# Visualization of accuracy comparison
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# Accuracy comparison bar chart
x_pos = np.arange(len(comparison_df))
width = 0.35

axes[0,0].bar(x_pos - width/2, comparison_df['Reference Accuracy (%)'], width, 
              label='Reference (castro_cad.csv)', alpha=0.8, color='blue')
axes[0,0].bar(x_pos + width/2, comparison_df['Extraction Accuracy (%)'], width, 
              label='Extracted (Automated)', alpha=0.8, color='red')
axes[0,0].set_xlabel('Infrastructure Type')
axes[0,0].set_ylabel('Accuracy (%)')
axes[0,0].set_title('Accuracy Comparison by Infrastructure Type')
axes[0,0].set_xticks(x_pos)
axes[0,0].set_xticklabels(comparison_df['Infrastructure Type'], rotation=45, ha='right')
axes[0,0].legend()
axes[0,0].grid(True, alpha=0.3)

# Entity count comparison
axes[0,1].bar(x_pos - width/2, comparison_df['Reference (castro_cad.csv)'], width, 
              label='Reference', alpha=0.8, color='blue')
axes[0,1].bar(x_pos + width/2, comparison_df['Extracted (Automated)'], width, 
              label='Extracted', alpha=0.8, color='red')
axes[0,1].set_xlabel('Infrastructure Type')
axes[0,1].set_ylabel('Entity Count')
axes[0,1].set_title('Entity Count Comparison')
axes[0,1].set_xticks(x_pos)
axes[0,1].set_xticklabels(comparison_df['Infrastructure Type'], rotation=45, ha='right')
axes[0,1].legend()
axes[0,1].grid(True, alpha=0.3)
axes[0,1].set_yscale('log')  # Log scale due to large differences

# Layer distribution in extracted data
layer_counts = extracted_df['layer_name'].value_counts().head(10)
axes[1,0].barh(range(len(layer_counts)), layer_counts.values, alpha=0.8)
axes[1,0].set_yticks(range(len(layer_counts)))
axes[1,0].set_yticklabels(layer_counts.index)
axes[1,0].set_xlabel('Entity Count')
axes[1,0].set_title('Top 10 Extracted Layers')
axes[1,0].grid(True, alpha=0.3)

# Entity type distribution
entity_counts = extracted_df['entity_type'].value_counts()
axes[1,1].pie(entity_counts.values, labels=entity_counts.index, autopct='%1.1f%%')
axes[1,1].set_title('Extracted Entity Type Distribution')

plt.tight_layout()
plt.show()

# Generate comprehensive recommendations
print("RECOMMENDATIONS FOR IMPROVING CAD EXTRACTION ACCURACY")
print("=" * 60)

recommendations = {
    'Critical Issues': [
        'Foundation pile detection accuracy is only 11.1% (466 vs 4199 expected)',
        'No road, fencing, or electrical infrastructure detected',
        'Coordinate system inconsistencies between reference and extracted data',
        'Large coordinate values suggest CRS transformation issues'
    ],
    'Immediate Actions': [
        'Review and enhance foundation pile classification rules',
        'Implement proper coordinate system transformation (EPSG:32633)',
        'Add detection for road, fencing, and electrical layers',
        'Validate CAD layer naming conventions and entity types'
    ],
    'Technical Improvements': [
        'Enhance LINE entity processing for foundation piles',
        'Implement multi-layer infrastructure detection',
        'Add geometric validation for extracted entities',
        'Improve classification confidence scoring'
    ],
    'Validation Enhancements': [
        'Implement spatial proximity validation with reference data',
        'Add coordinate system validation checks',
        'Create automated accuracy reporting',
        'Establish extraction quality thresholds'
    ]
}

for category, items in recommendations.items():
    print(f"\n{category}:")
    for i, item in enumerate(items, 1):
        print(f"  {i}. {item}")

# Calculate overall accuracy score
accuracy_scores = comparison_df['Extraction Accuracy (%)'].fillna(0)
overall_accuracy = accuracy_scores.mean()

print(f"\nOVERALL ASSESSMENT:")
print(f"=" * 20)
print(f"Overall Accuracy Score: {overall_accuracy:.1f}%")

if overall_accuracy >= 80:
    status = "✅ Excellent"
elif overall_accuracy >= 60:
    status = "✅ Good"
elif overall_accuracy >= 40:
    status = "⚠️ Needs Improvement"
else:
    status = "❌ Critical Issues"

print(f"Status: {status}")
print(f"\nNext Steps: Focus on foundation pile detection and coordinate system alignment")

# Save detailed analysis results
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
output_path = Path(output_dir)
output_path.mkdir(parents=True, exist_ok=True)

# Save comparison results
comparison_file = output_path / f"cad_extraction_accuracy_comparison_{timestamp}.csv"
comparison_df.to_csv(comparison_file, index=False)

# Save detailed analysis report
analysis_report = {
    'metadata': {
        'timestamp': timestamp,
        'project': project_type,
        'analysis_type': 'cad_extraction_accuracy'
    },
    'data_sources': {
        'reference_file': reference_file,
        'extracted_file': extracted_file,
        'ground_truth_file': ground_truth_file
    },
    'summary_statistics': {
        'overall_accuracy': float(overall_accuracy),
        'reference_entities': len(reference_df),
        'extracted_entities': len(extracted_df),
        'coordinate_overlap': x_overlap[0] <= x_overlap[1] and y_overlap[0] <= y_overlap[1]
    },
    'accuracy_by_type': comparison_df.to_dict('records'),
    'recommendations': recommendations,
    'critical_gaps': gaps
}

report_file = output_path / f"cad_extraction_accuracy_report_{timestamp}.json"
with open(report_file, 'w') as f:
    json.dump(analysis_report, f, indent=2, default=str)

print(f"Analysis results saved:")
print(f"  Comparison CSV: {comparison_file.name}")
print(f"  Detailed report: {report_file.name}")
print(f"  Output directory: {output_path}")