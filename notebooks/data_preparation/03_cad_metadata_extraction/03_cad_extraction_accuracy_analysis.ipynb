{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CAD Metadata Extraction Accuracy Analysis\n", "\n", "This notebook analyzes the accuracy of CAD metadata extraction by comparing:\n", "1. Reference data from `castro_cad.csv`\n", "2. Extracted CAD data from automated processing\n", "3. Ground truth specifications\n", "\n", "**Objective**: Assess extraction accuracy and identify areas for improvement"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Papermill parameters\n", "project_type = \"motali_de_castro\"\n", "site_name = \"main_site\"\n", "reference_file = \"../../../data/reference/castro_cad.csv\"\n", "ground_truth_file = \"../../../data/reference/montalto_di_castro_ground_truth.csv\"\n", "extracted_file = \"../../../data/raw/motali_de_castro/cad/enhanced_output/cad_extraction_pile_20250630_143601.csv\"\n", "output_dir = \"../../../output_runs/validation\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CAD Extraction Accuracy Analysis - <PERSON><PERSON><PERSON>_<PERSON>\n", "Timestamp: 2025-07-01 17:14:57\n", "============================================================\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "from datetime import datetime\n", "import json\n", "from collections import defaultdict\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up plotting\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(f\"CAD Extraction Accuracy Analysis - {project_type.title()}\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(\"=\" * 60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load Reference and Extracted Data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reference data loaded: 543 entities\n", "Reference columns: ['FID', 'Name', 'X', 'Y']\n", "\n", "Reference data sample:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>FID</th>\n", "      <th>Name</th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>TRJ-52-<PERSON>D<PERSON></td>\n", "      <td>707913.0523</td>\n", "      <td>4692670.217</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>TRJ-52-<PERSON>D<PERSON></td>\n", "      <td>707922.6523</td>\n", "      <td>4692698.635</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>TRJ-52-<PERSON>D<PERSON></td>\n", "      <td>707927.4523</td>\n", "      <td>4692696.296</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>TRJ-52-<PERSON>D<PERSON></td>\n", "      <td>707826.6523</td>\n", "      <td>4692634.961</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>TRJ-52-<PERSON>D<PERSON></td>\n", "      <td>707831.4523</td>\n", "      <td>4692645.141</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   FID         Name            X            Y\n", "0    0  TRJ-52-<PERSON><PERSON><PERSON>  707913.0523  4692670.217\n", "1    1  TRJ-52-<PERSON><PERSON><PERSON>  707922.6523  4692698.635\n", "2    2  TRJ-52-<PERSON><PERSON><PERSON>  707927.4523  4692696.296\n", "3    3  TRJ-52-<PERSON><PERSON><PERSON>  707826.6523  4692634.961\n", "4    4  TRJ-52-<PERSON><PERSON><PERSON>  707831.4523  4692645.141"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Reference data analysis:\n", "Unique names: 5\n", "Name patterns: Name\n", "TRJ-52-INT     247\n", "TRJ-26-INT     106\n", "TRJ-52-<PERSON><PERSON><PERSON>     96\n", "TRJ-26-EXT      66\n", "TRJ-52-<PERSON>XT      28\n", "Name: count, dtype: int64\n"]}], "source": ["# Load reference data (castro_cad.csv)\n", "reference_df = pd.read_csv(reference_file)\n", "print(f\"Reference data loaded: {len(reference_df)} entities\")\n", "print(f\"Reference columns: {list(reference_df.columns)}\")\n", "print(f\"\\nReference data sample:\")\n", "display(reference_df.head())\n", "\n", "# Analyze reference data patterns\n", "print(f\"\\nReference data analysis:\")\n", "print(f\"Unique names: {reference_df['Name'].nunique()}\")\n", "print(f\"Name patterns: {reference_df['Name'].value_counts().head(10)}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracted data loaded: 1419 entities\n", "Extracted columns: ['entity_id', 'entity_type', 'layer_name', 'color', 'linetype', 'lineweight', 'x_coord', 'y_coord', 'z_coord', 'geometry_type', 'classification', 'extraction_timestamp', 'source_file', 'source_space', 'point_count', 'is_closed', 'text_content', 'text_height', 'text_rotation', 'start_x', 'start_y', 'start_z', 'end_x', 'end_y', 'end_z', 'length', 'radius', 'block_name', 'rotation', 'scale_x', 'scale_y', 'scale_z']\n", "\n", "Extracted data sample:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>entity_id</th>\n", "      <th>entity_type</th>\n", "      <th>layer_name</th>\n", "      <th>color</th>\n", "      <th>linetype</th>\n", "      <th>lineweight</th>\n", "      <th>x_coord</th>\n", "      <th>y_coord</th>\n", "      <th>z_coord</th>\n", "      <th>geometry_type</th>\n", "      <th>...</th>\n", "      <th>end_x</th>\n", "      <th>end_y</th>\n", "      <th>end_z</th>\n", "      <th>length</th>\n", "      <th>radius</th>\n", "      <th>block_name</th>\n", "      <th>rotation</th>\n", "      <th>scale_x</th>\n", "      <th>scale_y</th>\n", "      <th>scale_z</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3987</td>\n", "      <td>MTEXT</td>\n", "      <td>SCS_Legenda</td>\n", "      <td>256</td>\n", "      <td>Continuous</td>\n", "      <td>-1</td>\n", "      <td>719371.639849</td>\n", "      <td>4.694003e+06</td>\n", "      <td>0.0</td>\n", "      <td>text</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>398B</td>\n", "      <td>MTEXT</td>\n", "      <td>SCS_Legenda</td>\n", "      <td>256</td>\n", "      <td>Continuous</td>\n", "      <td>-1</td>\n", "      <td>719371.639849</td>\n", "      <td>4.693995e+06</td>\n", "      <td>0.0</td>\n", "      <td>text</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5041</td>\n", "      <td>INSERT</td>\n", "      <td>CVT_Tracker 1x52 int</td>\n", "      <td>256</td>\n", "      <td>BYLAYER</td>\n", "      <td>-1</td>\n", "      <td>719369.582362</td>\n", "      <td>4.694113e+06</td>\n", "      <td>0.0</td>\n", "      <td>insert</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1P52@55DEG INTERNE</td>\n", "      <td>270.0</td>\n", "      <td>0.54</td>\n", "      <td>0.54</td>\n", "      <td>0.54</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>5042</td>\n", "      <td>INSERT</td>\n", "      <td>CVT_Tracker 1x52 ext</td>\n", "      <td>256</td>\n", "      <td>BYLAYER</td>\n", "      <td>-1</td>\n", "      <td>719369.858772</td>\n", "      <td>4.694104e+06</td>\n", "      <td>0.0</td>\n", "      <td>insert</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1P52@55DEG ESTERNE</td>\n", "      <td>270.0</td>\n", "      <td>0.54</td>\n", "      <td>0.54</td>\n", "      <td>0.54</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5043</td>\n", "      <td>INSERT</td>\n", "      <td>CVT_Tracker 1X52 Edge</td>\n", "      <td>256</td>\n", "      <td>BYLAYER</td>\n", "      <td>-1</td>\n", "      <td>719370.073906</td>\n", "      <td>4.694096e+06</td>\n", "      <td>0.0</td>\n", "      <td>insert</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1P52@55DEG EDGE INF</td>\n", "      <td>270.0</td>\n", "      <td>0.54</td>\n", "      <td>0.54</td>\n", "      <td>0.54</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 32 columns</p>\n", "</div>"], "text/plain": ["  entity_id entity_type             layer_name  color    linetype  lineweight  \\\n", "0      3987       MTEXT            SCS_Legenda    256  Continuous          -1   \n", "1      398B       MTEXT            SCS_Legenda    256  Continuous          -1   \n", "2      5041      INSERT   CVT_Tracker 1x52 int    256     BYLAYER          -1   \n", "3      5042      INSERT   CVT_Tracker 1x52 ext    256     BYLAYER          -1   \n", "4      5043      INSERT  CVT_Tracker 1X52 Edge    256     BYLAYER          -1   \n", "\n", "         x_coord       y_coord  z_coord geometry_type  ... end_x end_y end_z  \\\n", "0  719371.639849  4.694003e+06      0.0          text  ...   NaN   NaN   NaN   \n", "1  719371.639849  4.693995e+06      0.0          text  ...   NaN   NaN   NaN   \n", "2  719369.582362  4.694113e+06      0.0        insert  ...   NaN   NaN   NaN   \n", "3  719369.858772  4.694104e+06      0.0        insert  ...   NaN   NaN   NaN   \n", "4  719370.073906  4.694096e+06      0.0        insert  ...   NaN   NaN   NaN   \n", "\n", "  length  radius           block_name rotation  scale_x  scale_y  scale_z  \n", "0    NaN     NaN                  NaN      NaN      NaN      NaN      NaN  \n", "1    NaN     NaN                  NaN      NaN      NaN      NaN      NaN  \n", "2    NaN     NaN   1P52@55DEG INTERNE    270.0     0.54     0.54     0.54  \n", "3    NaN     NaN   1P52@55DEG ESTERNE    270.0     0.54     0.54     0.54  \n", "4    NaN     NaN  1P52@55DEG EDGE INF    270.0     0.54     0.54     0.54  \n", "\n", "[5 rows x 32 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Extracted data analysis:\n", "Entity types: entity_type\n", "INSERT    576\n", "LINE      466\n", "ARC       280\n", "MTEXT      72\n", "CIRCLE     25\n", "Name: count, dtype: int64\n", "\n", "Layer names: layer_name\n", "CVT - PILE LATERAL       330\n", "CVT_Tracker 1x52 int     248\n", "CVT - PILE DRIVE         240\n", "CVT - Pile END           178\n", "CVT_Tracker 1x26 int     106\n", "CVT_Tracker 1X52 Edge     97\n", "CVT_Tracker 1x26 ext      68\n", "CVT_Tracker 1x52 ext      29\n", "0                         25\n", "PDF4_Text                 15\n", "Name: count, dtype: int64\n", "\n", "Classifications: classification\n", "pile    1419\n", "Name: count, dtype: int64\n"]}], "source": ["# Load extracted CAD data\n", "extracted_df = pd.read_csv(extracted_file)\n", "print(f\"Extracted data loaded: {len(extracted_df)} entities\")\n", "print(f\"Extracted columns: {list(extracted_df.columns)}\")\n", "print(f\"\\nExtracted data sample:\")\n", "display(extracted_df.head())\n", "\n", "# Analyze extracted data patterns\n", "print(f\"\\nExtracted data analysis:\")\n", "print(f\"Entity types: {extracted_df['entity_type'].value_counts()}\")\n", "print(f\"\\nLayer names: {extracted_df['layer_name'].value_counts().head(10)}\")\n", "print(f\"\\nClassifications: {extracted_df['classification'].value_counts()}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ground truth loaded: 20 specifications\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON>.No</th>\n", "      <th>Categories</th>\n", "      <th>Features</th>\n", "      <th>Unit</th>\n", "      <th>Planned</th>\n", "      <th>Notes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Structures</td>\n", "      <td>Road</td>\n", "      <td>meters</td>\n", "      <td>3220.93</td>\n", "      <td>Site access and internal roads</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>Structures</td>\n", "      <td>Piling</td>\n", "      <td>#</td>\n", "      <td>4199.00</td>\n", "      <td>Main foundation piles (structural)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>Structures</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>#</td>\n", "      <td>23764.00</td>\n", "      <td>Solar panel modules</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>Structures</td>\n", "      <td>Tracker</td>\n", "      <td>#</td>\n", "      <td>543.00</td>\n", "      <td>Solar tracking systems</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>Structures</td>\n", "      <td><PERSON>le <PERSON></td>\n", "      <td>#</td>\n", "      <td>4199.00</td>\n", "      <td>Pile installation holes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>6</td>\n", "      <td>Structures</td>\n", "      <td>Block_Boundary</td>\n", "      <td>#</td>\n", "      <td>3.00</td>\n", "      <td>Site boundary blocks</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>7</td>\n", "      <td>Structures</td>\n", "      <td>Transformer Unit</td>\n", "      <td>#</td>\n", "      <td>3.00</td>\n", "      <td>Electrical transformer units</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>8</td>\n", "      <td>Structures</td>\n", "      <td>Fence Chainlink</td>\n", "      <td>meters</td>\n", "      <td>3873.10</td>\n", "      <td>Perimeter fencing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>9</td>\n", "      <td>Structures</td>\n", "      <td>User Cabin Foundation Execution</td>\n", "      <td>#</td>\n", "      <td>1.00</td>\n", "      <td>User facility foundation work</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>10</td>\n", "      <td>Structures</td>\n", "      <td>User Cabin Foundation Installation</td>\n", "      <td>#</td>\n", "      <td>1.00</td>\n", "      <td>User facility foundation installation</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>11</td>\n", "      <td>Structures</td>\n", "      <td>Delivery Booth Foundation Execution</td>\n", "      <td>#</td>\n", "      <td>1.00</td>\n", "      <td>Delivery facility foundation work</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>12</td>\n", "      <td>Structures</td>\n", "      <td>Delivery Booth Foundation Installation</td>\n", "      <td>#</td>\n", "      <td>1.00</td>\n", "      <td>Delivery facility foundation installation</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>13</td>\n", "      <td>Electrical Works</td>\n", "      <td>Low Voltage Backfill</td>\n", "      <td>Mtr</td>\n", "      <td>2047.00</td>\n", "      <td>Low voltage cable backfilling</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>14</td>\n", "      <td>Electrical Works</td>\n", "      <td>High Voltage Backfill</td>\n", "      <td>Mtr</td>\n", "      <td>1806.00</td>\n", "      <td>High voltage cable backfilling</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>15</td>\n", "      <td>Electrical Works</td>\n", "      <td>Low Voltage Trenching</td>\n", "      <td>Mtr</td>\n", "      <td>2047.00</td>\n", "      <td>Low voltage cable trenching</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>16</td>\n", "      <td>Electrical Works</td>\n", "      <td>High Voltage Trenching</td>\n", "      <td>Mtr</td>\n", "      <td>1806.00</td>\n", "      <td>High voltage cable trenching</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>17</td>\n", "      <td>Site Logistics</td>\n", "      <td>Vehicles</td>\n", "      <td>#</td>\n", "      <td>35.00</td>\n", "      <td>Construction and maintenance vehicles</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>18</td>\n", "      <td>Site Logistics</td>\n", "      <td>Structures</td>\n", "      <td>#</td>\n", "      <td>34.00</td>\n", "      <td>Temporary site structures</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>19</td>\n", "      <td>Site Logistics</td>\n", "      <td>Pa<PERSON><PERSON> positioning</td>\n", "      <td>#</td>\n", "      <td>42.00</td>\n", "      <td>Material pallet positions</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>20</td>\n", "      <td>Site Logistics</td>\n", "      <td>Coil medium voltage cable</td>\n", "      <td>#</td>\n", "      <td>0.00</td>\n", "      <td>Medium voltage cable coils (not specified)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    S.No        Categories                                Features    Unit  \\\n", "0      1        Structures                                    Road  meters   \n", "1      2        Structures                                  Piling       #   \n", "2      3        Structures                                 Modules       #   \n", "3      4        Structures                                 Tracker       #   \n", "4      5        Structures                               Pile Hole       #   \n", "5      6        Structures                          Block_Boundary       #   \n", "6      7        Structures                        Transformer Unit       #   \n", "7      8        Structures                         Fence Chainlink  meters   \n", "8      9        Structures         User Cabin Foundation Execution       #   \n", "9     10        Structures      User Cabin Foundation Installation       #   \n", "10    11        Structures     Delivery Booth Foundation Execution       #   \n", "11    12        Structures  Delivery Booth Foundation Installation       #   \n", "12    13  Electrical Works                    Low Voltage Backfill     Mtr   \n", "13    14  Electrical Works                   High Voltage Backfill     Mtr   \n", "14    15  Electrical Works                   Low Voltage Trenching     Mtr   \n", "15    16  Electrical Works                  High Voltage Trenching     Mtr   \n", "16    17    Site Logistics                                Vehicles       #   \n", "17    18    Site Logistics                              Structures       #   \n", "18    19    Site Logistics                      Pallet positioning       #   \n", "19    20    Site Logistics               Coil medium voltage cable       #   \n", "\n", "     Planned                                       Notes  \n", "0    3220.93              Site access and internal roads  \n", "1    4199.00          Main foundation piles (structural)  \n", "2   23764.00                         Solar panel modules  \n", "3     543.00                      Solar tracking systems  \n", "4    4199.00                     <PERSON>le installation holes  \n", "5       3.00                        Site boundary blocks  \n", "6       3.00                Electrical transformer units  \n", "7    3873.10                           Perimeter fencing  \n", "8       1.00               User facility foundation work  \n", "9       1.00       User facility foundation installation  \n", "10      1.00           Delivery facility foundation work  \n", "11      1.00   Delivery facility foundation installation  \n", "12   2047.00               Low voltage cable backfilling  \n", "13   1806.00              High voltage cable backfilling  \n", "14   2047.00                 Low voltage cable trenching  \n", "15   1806.00                High voltage cable trenching  \n", "16     35.00       Construction and maintenance vehicles  \n", "17     34.00                   Temporary site structures  \n", "18     42.00                   Material pallet positions  \n", "19      0.00  Medium voltage cable coils (not specified)  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Key ground truth targets:\n", "  piling: 4199.0 #\n", "  modules: 23764.0 #\n", "  tracker: 543.0 #\n"]}], "source": ["# Load ground truth specifications\n", "ground_truth_df = pd.read_csv(ground_truth_file)\n", "print(f\"Ground truth loaded: {len(ground_truth_df)} specifications\")\n", "display(ground_truth_df)\n", "\n", "# Create ground truth lookup\n", "ground_truth = {}\n", "for _, row in ground_truth_df.iterrows():\n", "    key = row['Features'].lower().replace(' ', '_')\n", "    ground_truth[key] = {\n", "        'planned': row['Planned'],\n", "        'unit': row['Unit'],\n", "        'category': row['Categories'],\n", "        'notes': row.get('Notes', '')\n", "    }\n", "\n", "print(f\"\\nKey ground truth targets:\")\n", "for key, value in ground_truth.items():\n", "    if key in ['piling', 'tracker', 'modules']:\n", "        print(f\"  {key}: {value['planned']} {value['unit']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Coordinate System Analysis"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["COORDINATE SYSTEM COMPARISON\n", "========================================\n", "Reference data (castro_cad.csv):\n", "  X range: 706922.11 to 708111.25\n", "  Y range: 4692597.34 to 4693099.32\n", "  Coordinate system: Appears to be UTM (7-digit coordinates)\n", "\n", "Extracted data:\n", "  X range: -203.11 to 1317413.28\n", "  Y range: -70.97 to 5748421.94\n", "  Coordinate system: Mixed (some very large values suggest different CRS)\n", "\n", "Coordinate overlap analysis:\n", "  ✅ Coordinate systems have overlap\n", "  X overlap: 706922.11 to 708111.25\n", "  Y overlap: 4692597.34 to 4693099.32\n"]}], "source": ["# Compare coordinate systems and ranges\n", "print(\"COORDINATE SYSTEM COMPARISON\")\n", "print(\"=\" * 40)\n", "\n", "# Reference coordinates\n", "ref_x_range = (reference_df['X'].min(), reference_df['X'].max())\n", "ref_y_range = (reference_df['Y'].min(), reference_df['Y'].max())\n", "\n", "print(f\"Reference data (castro_cad.csv):\")\n", "print(f\"  X range: {ref_x_range[0]:.2f} to {ref_x_range[1]:.2f}\")\n", "print(f\"  Y range: {ref_y_range[0]:.2f} to {ref_y_range[1]:.2f}\")\n", "print(f\"  Coordinate system: Appears to be UTM (7-digit coordinates)\")\n", "\n", "# Extracted coordinates\n", "ext_x_range = (extracted_df['x_coord'].min(), extracted_df['x_coord'].max())\n", "ext_y_range = (extracted_df['y_coord'].min(), extracted_df['y_coord'].max())\n", "\n", "print(f\"\\nExtracted data:\")\n", "print(f\"  X range: {ext_x_range[0]:.2f} to {ext_x_range[1]:.2f}\")\n", "print(f\"  Y range: {ext_y_range[0]:.2f} to {ext_y_range[1]:.2f}\")\n", "print(f\"  Coordinate system: Mixed (some very large values suggest different CRS)\")\n", "\n", "# Check for coordinate overlap\n", "x_overlap = (max(ref_x_range[0], ext_x_range[0]), min(ref_x_range[1], ext_x_range[1]))\n", "y_overlap = (max(ref_y_range[0], ext_y_range[0]), min(ref_y_range[1], ext_y_range[1]))\n", "\n", "print(f\"\\nCoordinate overlap analysis:\")\n", "if x_overlap[0] <= x_overlap[1] and y_overlap[0] <= y_overlap[1]:\n", "    print(f\"  ✅ Coordinate systems have overlap\")\n", "    print(f\"  X overlap: {x_overlap[0]:.2f} to {x_overlap[1]:.2f}\")\n", "    print(f\"  Y overlap: {y_overlap[0]:.2f} to {y_overlap[1]:.2f}\")\n", "else:\n", "    print(f\"  ❌ No coordinate overlap - different coordinate systems\")\n", "    print(f\"  This indicates a coordinate transformation issue\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Spatial Distribution Comparison"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Spatial distribution analysis:\n", "  Reference points: 543\n", "  Extracted points (filtered): 1132\n", "  Extracted points (total): 1419\n", "  Coordinate filtering removed: 287 points\n"]}], "source": ["# Create spatial distribution plots\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Reference data spatial distribution\n", "axes[0].scatter(reference_df['X'], reference_df['Y'], alpha=0.6, s=20, c='blue')\n", "axes[0].set_title('Reference Data Spatial Distribution\\n(castro_cad.csv)')\n", "axes[0].set_xlabel('X Coordinate')\n", "axes[0].set_ylabel('Y Coordinate')\n", "axes[0].grid(True, alpha=0.3)\n", "axes[0].ticklabel_format(style='scientific', axis='both', scilimits=(0,0))\n", "\n", "# Filter extracted data to reasonable coordinate range for visualization\n", "extracted_filtered = extracted_df[\n", "    (extracted_df['x_coord'] > 0) & \n", "    (extracted_df['x_coord'] < 1e7) &\n", "    (extracted_df['y_coord'] > 0) & \n", "    (extracted_df['y_coord'] < 1e7)\n", "]\n", "\n", "# Extracted data spatial distribution\n", "axes[1].scatter(extracted_filtered['x_coord'], extracted_filtered['y_coord'], \n", "               alpha=0.6, s=20, c='red')\n", "axes[1].set_title('Extracted Data Spatial Distribution\\n(Filtered for reasonable coordinates)')\n", "axes[1].set_xlabel('X Coordinate')\n", "axes[1].set_ylabel('Y Coordinate')\n", "axes[1].grid(True, alpha=0.3)\n", "axes[1].ticklabel_format(style='scientific', axis='both', scilimits=(0,0))\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"Spatial distribution analysis:\")\n", "print(f\"  Reference points: {len(reference_df)}\")\n", "print(f\"  Extracted points (filtered): {len(extracted_filtered)}\")\n", "print(f\"  Extracted points (total): {len(extracted_df)}\")\n", "print(f\"  Coordinate filtering removed: {len(extracted_df) - len(extracted_filtered)} points\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Entity Type and Classification Analysis"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["REFERENCE DATA PATTERN ANALYSIS\n", "========================================\n", "Reference data patterns:\n", "  TRJ-26-EXT: 66\n", "  TRJ-26-INT: 106\n", "  TRJ-52-EDGE: 96\n", "  TRJ-52-EXT: 28\n", "  TRJ-52-INT: 247\n", "\n", "Total reference tracker-related entities: 543\n"]}], "source": ["# Analyze reference data patterns\n", "print(\"REFERENCE DATA PATTERN ANALYSIS\")\n", "print(\"=\" * 40)\n", "\n", "# Extract pattern information from reference names\n", "reference_patterns = defaultdict(int)\n", "for name in reference_df['Name']:\n", "    if 'TRJ-52' in name:\n", "        if 'INT' in name:\n", "            reference_patterns['TRJ-52-INT'] += 1\n", "        <PERSON><PERSON> '<PERSON><PERSON>' in name:\n", "            reference_patterns['TRJ-52-EXT'] += 1\n", "        elif 'ED<PERSON>' in name:\n", "            reference_patterns['TRJ-52-EDGE'] += 1\n", "    elif 'TRJ-26' in name:\n", "        if 'INT' in name:\n", "            reference_patterns['TRJ-26-INT'] += 1\n", "        <PERSON><PERSON> '<PERSON><PERSON>' in name:\n", "            reference_patterns['TRJ-26-EXT'] += 1\n", "\n", "print(\"Reference data patterns:\")\n", "for pattern, count in sorted(reference_patterns.items()):\n", "    print(f\"  {pattern}: {count}\")\n", "\n", "total_ref_trackers = sum(reference_patterns.values())\n", "print(f\"\\nTotal reference tracker-related entities: {total_ref_trackers}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["EXTRACTED DATA PATTERN ANALYSIS\n", "========================================\n", "Extracted tracker patterns:\n", "  CVT_Tracker 1x52 int: 248\n", "  CVT_Tracker 1x26 int: 106\n", "  CVT_Tracker 1X52 Edge: 97\n", "  CVT_Tracker 1x26 ext: 68\n", "  CVT_Tracker 1x52 ext: 29\n", "\n", "Total extracted tracker entities: 548\n", "\n", "Extracted pile patterns:\n", "  CVT - PILE LATERAL: 330\n", "  CVT - PILE DRIVE: 240\n", "\n", "Total extracted pile entities: 570\n"]}], "source": ["# Analyze extracted data patterns\n", "print(\"EXTRACTED DATA PATTERN ANALYSIS\")\n", "print(\"=\" * 40)\n", "\n", "# Analyze tracker-related extractions\n", "tracker_layers = extracted_df[extracted_df['layer_name'].str.contains('Tracker', na=False)]\n", "tracker_patterns = tracker_layers['layer_name'].value_counts()\n", "\n", "print(\"Extracted tracker patterns:\")\n", "for pattern, count in tracker_patterns.items():\n", "    print(f\"  {pattern}: {count}\")\n", "\n", "print(f\"\\nTotal extracted tracker entities: {len(tracker_layers)}\")\n", "\n", "# Analyze pile-related extractions\n", "pile_layers = extracted_df[extracted_df['layer_name'].str.contains('PILE', na=False)]\n", "pile_patterns = pile_layers['layer_name'].value_counts()\n", "\n", "print(f\"\\nExtracted pile patterns:\")\n", "for pattern, count in pile_patterns.items():\n", "    print(f\"  {pattern}: {count}\")\n", "\n", "print(f\"\\nTotal extracted pile entities: {len(pile_layers)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Accuracy Assessment"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ACCURACY ASSESSMENT\n", "========================================\n", "Tracker Detection Accuracy:\n", "  Expected (ground truth): 543.0\n", "  Reference (castro_cad.csv): 543\n", "  Extracted (automated): 548\n", "  Reference vs Ground Truth: 100.0%\n", "  Extracted vs Ground Truth: 100.9%\n", "  Extracted vs Reference: 100.9%\n", "\n", "Foundation Pile Detection Accuracy:\n", "  Expected (ground truth): 4199.0\n", "  Extracted (automated): 570\n", "  Extracted vs Ground Truth: 13.6%\n", "\n", "Overall Entity Detection:\n", "  Reference entities: 543\n", "  Extracted entities: 1419\n", "  Extraction ratio: 261.3%\n"]}], "source": ["# Calculate accuracy metrics\n", "print(\"ACCURACY ASSESSMENT\")\n", "print(\"=\" * 40)\n", "\n", "# Tracker accuracy\n", "expected_trackers = ground_truth['tracker']['planned']\n", "reference_trackers = total_ref_trackers\n", "extracted_trackers = len(tracker_layers)\n", "\n", "print(f\"Tracker Detection Accuracy:\")\n", "print(f\"  Expected (ground truth): {expected_trackers}\")\n", "print(f\"  Reference (castro_cad.csv): {reference_trackers}\")\n", "print(f\"  Extracted (automated): {extracted_trackers}\")\n", "print(f\"  Reference vs Ground Truth: {(reference_trackers/expected_trackers)*100:.1f}%\")\n", "print(f\"  Extracted vs Ground Truth: {(extracted_trackers/expected_trackers)*100:.1f}%\")\n", "print(f\"  Extracted vs Reference: {(extracted_trackers/reference_trackers)*100:.1f}%\")\n", "\n", "# Foundation pile accuracy\n", "expected_piles = ground_truth['piling']['planned']\n", "extracted_piles = len(pile_layers)\n", "\n", "print(f\"\\nFoundation Pile Detection Accuracy:\")\n", "print(f\"  Expected (ground truth): {expected_piles}\")\n", "print(f\"  Extracted (automated): {extracted_piles}\")\n", "print(f\"  Extracted vs Ground Truth: {(extracted_piles/expected_piles)*100:.1f}%\")\n", "\n", "# Overall entity detection\n", "print(f\"\\nOverall Entity Detection:\")\n", "print(f\"  Reference entities: {len(reference_df)}\")\n", "print(f\"  Extracted entities: {len(extracted_df)}\")\n", "print(f\"  Extraction ratio: {(len(extracted_df)/len(reference_df))*100:.1f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Detailed Comparison and Gap Analysis"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DETAILED ACCURACY COMPARISON\n", "============================================================\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Infrastructure Type</th>\n", "      <th>Ground Truth</th>\n", "      <th>Reference (castro_cad.csv)</th>\n", "      <th>Extracted (Automated)</th>\n", "      <th>Unit</th>\n", "      <th>Reference Accuracy (%)</th>\n", "      <th>Extraction Accuracy (%)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Trackers</td>\n", "      <td>543.00</td>\n", "      <td>543</td>\n", "      <td>548</td>\n", "      <td>#</td>\n", "      <td>100.0</td>\n", "      <td>100.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Foundation Piles</td>\n", "      <td>4199.00</td>\n", "      <td>0</td>\n", "      <td>570</td>\n", "      <td>#</td>\n", "      <td>0.0</td>\n", "      <td>13.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Module Support Piles</td>\n", "      <td>23764.00</td>\n", "      <td>0</td>\n", "      <td>23972</td>\n", "      <td>#</td>\n", "      <td>0.0</td>\n", "      <td>100.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Roads</td>\n", "      <td>3220.93</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>meters</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Fencing</td>\n", "      <td>3873.10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>meters</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Electrical</td>\n", "      <td>2047.00</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>meters</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Infrastructure Type  Ground Truth  Reference (castro_cad.csv)  \\\n", "0              Trackers        543.00                         543   \n", "1      Foundation Piles       4199.00                           0   \n", "2  Module Support Piles      23764.00                           0   \n", "3                 Roads       3220.93                           0   \n", "4               Fencing       3873.10                           0   \n", "5            Electrical       2047.00                           0   \n", "\n", "   Extracted (Automated)    Unit  Reference Accuracy (%)  \\\n", "0                    548       #                   100.0   \n", "1                    570       #                     0.0   \n", "2                  23972       #                     0.0   \n", "3                      0  meters                     0.0   \n", "4                      0  meters                     0.0   \n", "5                      0  meters                     0.0   \n", "\n", "   Extraction Accuracy (%)  \n", "0                    100.9  \n", "1                     13.6  \n", "2                    100.9  \n", "3                      0.0  \n", "4                      0.0  \n", "5                      0.0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "CRITICAL GAPS IDENTIFIED:\n", "==============================\n", "❌ Foundation Piles: 13.6% accuracy, missing 3629 entities\n", "❌ Roads: 0.0% accuracy, missing 3221 entities\n", "❌ Fencing: 0.0% accuracy, missing 3873 entities\n", "❌ Electrical: 0.0% accuracy, missing 2047 entities\n"]}], "source": ["# Create detailed comparison table\n", "comparison_data = {\n", "    'Infrastructure Type': ['Trackers', 'Foundation Piles', 'Module Support Piles', 'Roads', 'Fencing', 'Electrical'],\n", "    'Ground Truth': [543, 4199, 23764, 3220.93, 3873.1, 2047],\n", "    'Reference (castro_cad.csv)': [total_ref_trackers, 0, 0, 0, 0, 0],\n", "    'Extracted (Automated)': [extracted_trackers, extracted_piles, 23972, 0, 0, 0],\n", "    'Unit': ['#', '#', '#', 'meters', 'meters', 'meters']\n", "}\n", "\n", "comparison_df = pd.DataFrame(comparison_data)\n", "\n", "# Calculate accuracy percentages\n", "comparison_df['Reference Accuracy (%)'] = (\n", "    comparison_df['Reference (castro_cad.csv)'] / comparison_df['Ground Truth'] * 100\n", ").round(1)\n", "\n", "comparison_df['Extraction Accuracy (%)'] = (\n", "    comparison_df['Extracted (Automated)'] / comparison_df['Ground Truth'] * 100\n", ").round(1)\n", "\n", "print(\"DETAILED ACCURACY COMPARISON\")\n", "print(\"=\" * 60)\n", "display(comparison_df)\n", "\n", "# Identify critical gaps\n", "print(\"\\nCRITICAL GAPS IDENTIFIED:\")\n", "print(\"=\" * 30)\n", "\n", "gaps = []\n", "for idx, row in comparison_df.iterrows():\n", "    if row['Extraction Accuracy (%)'] < 50:\n", "        gaps.append({\n", "            'type': row['Infrastructure Type'],\n", "            'accuracy': row['Extraction Accuracy (%)'],\n", "            'missing': row['Ground Truth'] - row['Extracted (Automated)']\n", "        })\n", "\n", "for gap in gaps:\n", "    print(f\"❌ {gap['type']}: {gap['accuracy']:.1f}% accuracy, missing {gap['missing']:.0f} entities\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualization of accuracy comparison\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# Accuracy comparison bar chart\n", "x_pos = np.arange(len(comparison_df))\n", "width = 0.35\n", "\n", "axes[0,0].bar(x_pos - width/2, comparison_df['Reference Accuracy (%)'], width, \n", "              label='Reference (castro_cad.csv)', alpha=0.8, color='blue')\n", "axes[0,0].bar(x_pos + width/2, comparison_df['Extraction Accuracy (%)'], width, \n", "              label='Extracted (Automated)', alpha=0.8, color='red')\n", "axes[0,0].set_xlabel('Infrastructure Type')\n", "axes[0,0].set_ylabel('Accuracy (%)')\n", "axes[0,0].set_title('Accuracy Comparison by Infrastructure Type')\n", "axes[0,0].set_xticks(x_pos)\n", "axes[0,0].set_xticklabels(comparison_df['Infrastructure Type'], rotation=45, ha='right')\n", "axes[0,0].legend()\n", "axes[0,0].grid(True, alpha=0.3)\n", "\n", "# Entity count comparison\n", "axes[0,1].bar(x_pos - width/2, comparison_df['Reference (castro_cad.csv)'], width, \n", "              label='Reference', alpha=0.8, color='blue')\n", "axes[0,1].bar(x_pos + width/2, comparison_df['Extracted (Automated)'], width, \n", "              label='Extracted', alpha=0.8, color='red')\n", "axes[0,1].set_xlabel('Infrastructure Type')\n", "axes[0,1].set_ylabel('Entity Count')\n", "axes[0,1].set_title('Entity Count Comparison')\n", "axes[0,1].set_xticks(x_pos)\n", "axes[0,1].set_xticklabels(comparison_df['Infrastructure Type'], rotation=45, ha='right')\n", "axes[0,1].legend()\n", "axes[0,1].grid(True, alpha=0.3)\n", "axes[0,1].set_yscale('log')  # Log scale due to large differences\n", "\n", "# Layer distribution in extracted data\n", "layer_counts = extracted_df['layer_name'].value_counts().head(10)\n", "axes[1,0].barh(range(len(layer_counts)), layer_counts.values, alpha=0.8)\n", "axes[1,0].set_yticks(range(len(layer_counts)))\n", "axes[1,0].set_yticklabels(layer_counts.index)\n", "axes[1,0].set_xlabel('Entity Count')\n", "axes[1,0].set_title('Top 10 Extracted Layers')\n", "axes[1,0].grid(True, alpha=0.3)\n", "\n", "# Entity type distribution\n", "entity_counts = extracted_df['entity_type'].value_counts()\n", "axes[1,1].pie(entity_counts.values, labels=entity_counts.index, autopct='%1.1f%%')\n", "axes[1,1].set_title('Extracted Entity Type Distribution')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Recommendations and Action Items"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RECOM<PERSON><PERSON>ATIONS FOR IMPROVING CAD EXTRACTION ACCURACY\n", "============================================================\n", "\n", "Critical Issues:\n", "  1. Foundation pile detection accuracy is only 11.1% (466 vs 4199 expected)\n", "  2. No road, fencing, or electrical infrastructure detected\n", "  3. Coordinate system inconsistencies between reference and extracted data\n", "  4. Large coordinate values suggest CRS transformation issues\n", "\n", "Immediate Actions:\n", "  1. Review and enhance foundation pile classification rules\n", "  2. Implement proper coordinate system transformation (EPSG:32633)\n", "  3. Add detection for road, fencing, and electrical layers\n", "  4. Validate CAD layer naming conventions and entity types\n", "\n", "Technical Improvements:\n", "  1. Enhance LINE entity processing for foundation piles\n", "  2. Implement multi-layer infrastructure detection\n", "  3. Add geometric validation for extracted entities\n", "  4. Improve classification confidence scoring\n", "\n", "Validation Enhancements:\n", "  1. Implement spatial proximity validation with reference data\n", "  2. Add coordinate system validation checks\n", "  3. Create automated accuracy reporting\n", "  4. Establish extraction quality thresholds\n", "\n", "OVERALL ASSESSMENT:\n", "====================\n", "Overall Accuracy Score: 35.9%\n", "Status: ❌ Critical Issues\n", "\n", "Next Steps: Focus on foundation pile detection and coordinate system alignment\n"]}], "source": ["# Generate comprehensive recommendations\n", "print(\"RECOM<PERSON><PERSON>ATIONS FOR IMPROVING CAD EXTRACTION ACCURACY\")\n", "print(\"=\" * 60)\n", "\n", "recommendations = {\n", "    'Critical Issues': [\n", "        'Foundation pile detection accuracy is only 11.1% (466 vs 4199 expected)',\n", "        'No road, fencing, or electrical infrastructure detected',\n", "        'Coordinate system inconsistencies between reference and extracted data',\n", "        'Large coordinate values suggest CRS transformation issues'\n", "    ],\n", "    'Immediate Actions': [\n", "        'Review and enhance foundation pile classification rules',\n", "        'Implement proper coordinate system transformation (EPSG:32633)',\n", "        'Add detection for road, fencing, and electrical layers',\n", "        'Validate CAD layer naming conventions and entity types'\n", "    ],\n", "    'Technical Improvements': [\n", "        'Enhance LINE entity processing for foundation piles',\n", "        'Implement multi-layer infrastructure detection',\n", "        'Add geometric validation for extracted entities',\n", "        'Improve classification confidence scoring'\n", "    ],\n", "    'Validation Enhancements': [\n", "        'Implement spatial proximity validation with reference data',\n", "        'Add coordinate system validation checks',\n", "        'Create automated accuracy reporting',\n", "        'Establish extraction quality thresholds'\n", "    ]\n", "}\n", "\n", "for category, items in recommendations.items():\n", "    print(f\"\\n{category}:\")\n", "    for i, item in enumerate(items, 1):\n", "        print(f\"  {i}. {item}\")\n", "\n", "# Calculate overall accuracy score\n", "accuracy_scores = comparison_df['Extraction Accuracy (%)'].fillna(0)\n", "overall_accuracy = accuracy_scores.mean()\n", "\n", "print(f\"\\nOVERALL ASSESSMENT:\")\n", "print(f\"=\" * 20)\n", "print(f\"Overall Accuracy Score: {overall_accuracy:.1f}%\")\n", "\n", "if overall_accuracy >= 80:\n", "    status = \"✅ Excellent\"\n", "elif overall_accuracy >= 60:\n", "    status = \"✅ Good\"\n", "elif overall_accuracy >= 40:\n", "    status = \"⚠️ Needs Improvement\"\n", "else:\n", "    status = \"❌ Critical Issues\"\n", "\n", "print(f\"Status: {status}\")\n", "print(f\"\\nNext Steps: Focus on foundation pile detection and coordinate system alignment\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Analysis results saved:\n", "  Comparison CSV: cad_extraction_accuracy_comparison_20250701_171457.csv\n", "  Detailed report: cad_extraction_accuracy_report_20250701_171457.json\n", "  Output directory: ../../../output_runs/validation\n"]}], "source": ["# Save detailed analysis results\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_path = Path(output_dir)\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# Save comparison results\n", "comparison_file = output_path / f\"cad_extraction_accuracy_comparison_{timestamp}.csv\"\n", "comparison_df.to_csv(comparison_file, index=False)\n", "\n", "# Save detailed analysis report\n", "analysis_report = {\n", "    'metadata': {\n", "        'timestamp': timestamp,\n", "        'project': project_type,\n", "        'analysis_type': 'cad_extraction_accuracy'\n", "    },\n", "    'data_sources': {\n", "        'reference_file': reference_file,\n", "        'extracted_file': extracted_file,\n", "        'ground_truth_file': ground_truth_file\n", "    },\n", "    'summary_statistics': {\n", "        'overall_accuracy': float(overall_accuracy),\n", "        'reference_entities': len(reference_df),\n", "        'extracted_entities': len(extracted_df),\n", "        'coordinate_overlap': x_overlap[0] <= x_overlap[1] and y_overlap[0] <= y_overlap[1]\n", "    },\n", "    'accuracy_by_type': comparison_df.to_dict('records'),\n", "    'recommendations': recommendations,\n", "    'critical_gaps': gaps\n", "}\n", "\n", "report_file = output_path / f\"cad_extraction_accuracy_report_{timestamp}.json\"\n", "with open(report_file, 'w') as f:\n", "    json.dump(analysis_report, f, indent=2, default=str)\n", "\n", "print(f\"Analysis results saved:\")\n", "print(f\"  Comparison CSV: {comparison_file.name}\")\n", "print(f\"  Detailed report: {report_file.name}\")\n", "print(f\"  Output directory: {output_path}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}