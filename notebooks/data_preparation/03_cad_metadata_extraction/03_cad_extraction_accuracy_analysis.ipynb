{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CAD Metadata Extraction Accuracy Analysis\n", "\n", "This notebook analyzes the accuracy of CAD metadata extraction by comparing:\n", "1. Reference data from `castro_cad.csv`\n", "2. Extracted CAD data from automated processing\n", "3. Ground truth specifications\n", "\n", "**Objective**: Assess extraction accuracy and identify areas for improvement"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Papermill parameters\n", "project_type = \"motali_de_castro\"\n", "site_name = \"main_site\"\n", "reference_file = \"../../../data/reference/castro_cad.csv\"\n", "ground_truth_file = \"../../../data/reference/montalto_di_castro_ground_truth.csv\"\n", "extracted_file = \"../../../data/raw/motali_de_castro/cad/enhanced_output/cad_extraction_pile_20250630_143601.csv\"\n", "output_dir = \"../../../output_runs/validation\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "from datetime import datetime\n", "import json\n", "from collections import defaultdict\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up plotting\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(f\"CAD Extraction Accuracy Analysis - {project_type.title()}\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(\"=\" * 60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load Reference and Extracted Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load reference data (castro_cad.csv)\n", "reference_df = pd.read_csv(reference_file)\n", "print(f\"Reference data loaded: {len(reference_df)} entities\")\n", "print(f\"Reference columns: {list(reference_df.columns)}\")\n", "print(f\"\\nReference data sample:\")\n", "display(reference_df.head())\n", "\n", "# Analyze reference data patterns\n", "print(f\"\\nReference data analysis:\")\n", "print(f\"Unique names: {reference_df['Name'].nunique()}\")\n", "print(f\"Name patterns: {reference_df['Name'].value_counts().head(10)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load extracted CAD data\n", "extracted_df = pd.read_csv(extracted_file)\n", "print(f\"Extracted data loaded: {len(extracted_df)} entities\")\n", "print(f\"Extracted columns: {list(extracted_df.columns)}\")\n", "print(f\"\\nExtracted data sample:\")\n", "display(extracted_df.head())\n", "\n", "# Analyze extracted data patterns\n", "print(f\"\\nExtracted data analysis:\")\n", "print(f\"Entity types: {extracted_df['entity_type'].value_counts()}\")\n", "print(f\"\\nLayer names: {extracted_df['layer_name'].value_counts().head(10)}\")\n", "print(f\"\\nClassifications: {extracted_df['classification'].value_counts()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load ground truth specifications\n", "ground_truth_df = pd.read_csv(ground_truth_file)\n", "print(f\"Ground truth loaded: {len(ground_truth_df)} specifications\")\n", "display(ground_truth_df)\n", "\n", "# Create ground truth lookup\n", "ground_truth = {}\n", "for _, row in ground_truth_df.iterrows():\n", "    key = row['Features'].lower().replace(' ', '_')\n", "    ground_truth[key] = {\n", "        'planned': row['Planned'],\n", "        'unit': row['Unit'],\n", "        'category': row['Categories'],\n", "        'notes': row.get('Notes', '')\n", "    }\n", "\n", "print(f\"\\nKey ground truth targets:\")\n", "for key, value in ground_truth.items():\n", "    if key in ['piling', 'tracker', 'modules']:\n", "        print(f\"  {key}: {value['planned']} {value['unit']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Coordinate System Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare coordinate systems and ranges\n", "print(\"COORDINATE SYSTEM COMPARISON\")\n", "print(\"=\" * 40)\n", "\n", "# Reference coordinates\n", "ref_x_range = (reference_df['X'].min(), reference_df['X'].max())\n", "ref_y_range = (reference_df['Y'].min(), reference_df['Y'].max())\n", "\n", "print(f\"Reference data (castro_cad.csv):\")\n", "print(f\"  X range: {ref_x_range[0]:.2f} to {ref_x_range[1]:.2f}\")\n", "print(f\"  Y range: {ref_y_range[0]:.2f} to {ref_y_range[1]:.2f}\")\n", "print(f\"  Coordinate system: Appears to be UTM (7-digit coordinates)\")\n", "\n", "# Extracted coordinates\n", "ext_x_range = (extracted_df['x_coord'].min(), extracted_df['x_coord'].max())\n", "ext_y_range = (extracted_df['y_coord'].min(), extracted_df['y_coord'].max())\n", "\n", "print(f\"\\nExtracted data:\")\n", "print(f\"  X range: {ext_x_range[0]:.2f} to {ext_x_range[1]:.2f}\")\n", "print(f\"  Y range: {ext_y_range[0]:.2f} to {ext_y_range[1]:.2f}\")\n", "print(f\"  Coordinate system: Mixed (some very large values suggest different CRS)\")\n", "\n", "# Check for coordinate overlap\n", "x_overlap = (max(ref_x_range[0], ext_x_range[0]), min(ref_x_range[1], ext_x_range[1]))\n", "y_overlap = (max(ref_y_range[0], ext_y_range[0]), min(ref_y_range[1], ext_y_range[1]))\n", "\n", "print(f\"\\nCoordinate overlap analysis:\")\n", "if x_overlap[0] <= x_overlap[1] and y_overlap[0] <= y_overlap[1]:\n", "    print(f\"  ✅ Coordinate systems have overlap\")\n", "    print(f\"  X overlap: {x_overlap[0]:.2f} to {x_overlap[1]:.2f}\")\n", "    print(f\"  Y overlap: {y_overlap[0]:.2f} to {y_overlap[1]:.2f}\")\n", "else:\n", "    print(f\"  ❌ No coordinate overlap - different coordinate systems\")\n", "    print(f\"  This indicates a coordinate transformation issue\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Spatial Distribution Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create spatial distribution plots\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Reference data spatial distribution\n", "axes[0].scatter(reference_df['X'], reference_df['Y'], alpha=0.6, s=20, c='blue')\n", "axes[0].set_title('Reference Data Spatial Distribution\\n(castro_cad.csv)')\n", "axes[0].set_xlabel('X Coordinate')\n", "axes[0].set_ylabel('Y Coordinate')\n", "axes[0].grid(True, alpha=0.3)\n", "axes[0].ticklabel_format(style='scientific', axis='both', scilimits=(0,0))\n", "\n", "# Filter extracted data to reasonable coordinate range for visualization\n", "extracted_filtered = extracted_df[\n", "    (extracted_df['x_coord'] > 0) & \n", "    (extracted_df['x_coord'] < 1e7) &\n", "    (extracted_df['y_coord'] > 0) & \n", "    (extracted_df['y_coord'] < 1e7)\n", "]\n", "\n", "# Extracted data spatial distribution\n", "axes[1].scatter(extracted_filtered['x_coord'], extracted_filtered['y_coord'], \n", "               alpha=0.6, s=20, c='red')\n", "axes[1].set_title('Extracted Data Spatial Distribution\\n(Filtered for reasonable coordinates)')\n", "axes[1].set_xlabel('X Coordinate')\n", "axes[1].set_ylabel('Y Coordinate')\n", "axes[1].grid(True, alpha=0.3)\n", "axes[1].ticklabel_format(style='scientific', axis='both', scilimits=(0,0))\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"Spatial distribution analysis:\")\n", "print(f\"  Reference points: {len(reference_df)}\")\n", "print(f\"  Extracted points (filtered): {len(extracted_filtered)}\")\n", "print(f\"  Extracted points (total): {len(extracted_df)}\")\n", "print(f\"  Coordinate filtering removed: {len(extracted_df) - len(extracted_filtered)} points\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Entity Type and Classification Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze reference data patterns\n", "print(\"REFERENCE DATA PATTERN ANALYSIS\")\n", "print(\"=\" * 40)\n", "\n", "# Extract pattern information from reference names\n", "reference_patterns = defaultdict(int)\n", "for name in reference_df['Name']:\n", "    if 'TRJ-52' in name:\n", "        if 'INT' in name:\n", "            reference_patterns['TRJ-52-INT'] += 1\n", "        <PERSON><PERSON> '<PERSON><PERSON>' in name:\n", "            reference_patterns['TRJ-52-EXT'] += 1\n", "        elif 'ED<PERSON>' in name:\n", "            reference_patterns['TRJ-52-EDGE'] += 1\n", "    elif 'TRJ-26' in name:\n", "        if 'INT' in name:\n", "            reference_patterns['TRJ-26-INT'] += 1\n", "        <PERSON><PERSON> '<PERSON><PERSON>' in name:\n", "            reference_patterns['TRJ-26-EXT'] += 1\n", "\n", "print(\"Reference data patterns:\")\n", "for pattern, count in sorted(reference_patterns.items()):\n", "    print(f\"  {pattern}: {count}\")\n", "\n", "total_ref_trackers = sum(reference_patterns.values())\n", "print(f\"\\nTotal reference tracker-related entities: {total_ref_trackers}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze extracted data patterns\n", "print(\"EXTRACTED DATA PATTERN ANALYSIS\")\n", "print(\"=\" * 40)\n", "\n", "# Analyze tracker-related extractions\n", "tracker_layers = extracted_df[extracted_df['layer_name'].str.contains('Tracker', na=False)]\n", "tracker_patterns = tracker_layers['layer_name'].value_counts()\n", "\n", "print(\"Extracted tracker patterns:\")\n", "for pattern, count in tracker_patterns.items():\n", "    print(f\"  {pattern}: {count}\")\n", "\n", "print(f\"\\nTotal extracted tracker entities: {len(tracker_layers)}\")\n", "\n", "# Analyze pile-related extractions\n", "pile_layers = extracted_df[extracted_df['layer_name'].str.contains('PILE', na=False)]\n", "pile_patterns = pile_layers['layer_name'].value_counts()\n", "\n", "print(f\"\\nExtracted pile patterns:\")\n", "for pattern, count in pile_patterns.items():\n", "    print(f\"  {pattern}: {count}\")\n", "\n", "print(f\"\\nTotal extracted pile entities: {len(pile_layers)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Accuracy Assessment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate accuracy metrics\n", "print(\"ACCURACY ASSESSMENT\")\n", "print(\"=\" * 40)\n", "\n", "# Tracker accuracy\n", "expected_trackers = ground_truth['tracker']['planned']\n", "reference_trackers = total_ref_trackers\n", "extracted_trackers = len(tracker_layers)\n", "\n", "print(f\"Tracker Detection Accuracy:\")\n", "print(f\"  Expected (ground truth): {expected_trackers}\")\n", "print(f\"  Reference (castro_cad.csv): {reference_trackers}\")\n", "print(f\"  Extracted (automated): {extracted_trackers}\")\n", "print(f\"  Reference vs Ground Truth: {(reference_trackers/expected_trackers)*100:.1f}%\")\n", "print(f\"  Extracted vs Ground Truth: {(extracted_trackers/expected_trackers)*100:.1f}%\")\n", "print(f\"  Extracted vs Reference: {(extracted_trackers/reference_trackers)*100:.1f}%\")\n", "\n", "# Foundation pile accuracy\n", "expected_piles = ground_truth['piling']['planned']\n", "extracted_piles = len(pile_layers)\n", "\n", "print(f\"\\nFoundation Pile Detection Accuracy:\")\n", "print(f\"  Expected (ground truth): {expected_piles}\")\n", "print(f\"  Extracted (automated): {extracted_piles}\")\n", "print(f\"  Extracted vs Ground Truth: {(extracted_piles/expected_piles)*100:.1f}%\")\n", "\n", "# Overall entity detection\n", "print(f\"\\nOverall Entity Detection:\")\n", "print(f\"  Reference entities: {len(reference_df)}\")\n", "print(f\"  Extracted entities: {len(extracted_df)}\")\n", "print(f\"  Extraction ratio: {(len(extracted_df)/len(reference_df))*100:.1f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Detailed Comparison and Gap Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create detailed comparison table\n", "comparison_data = {\n", "    'Infrastructure Type': ['Trackers', 'Foundation Piles', 'Module Support Piles', 'Roads', 'Fencing', 'Electrical'],\n", "    'Ground Truth': [543, 4199, 23764, 3220.93, 3873.1, 2047],\n", "    'Reference (castro_cad.csv)': [total_ref_trackers, 0, 0, 0, 0, 0],\n", "    'Extracted (Automated)': [extracted_trackers, extracted_piles, 23972, 0, 0, 0],\n", "    'Unit': ['#', '#', '#', 'meters', 'meters', 'meters']\n", "}\n", "\n", "comparison_df = pd.DataFrame(comparison_data)\n", "\n", "# Calculate accuracy percentages\n", "comparison_df['Reference Accuracy (%)'] = (\n", "    comparison_df['Reference (castro_cad.csv)'] / comparison_df['Ground Truth'] * 100\n", ").round(1)\n", "\n", "comparison_df['Extraction Accuracy (%)'] = (\n", "    comparison_df['Extracted (Automated)'] / comparison_df['Ground Truth'] * 100\n", ").round(1)\n", "\n", "print(\"DETAILED ACCURACY COMPARISON\")\n", "print(\"=\" * 60)\n", "display(comparison_df)\n", "\n", "# Identify critical gaps\n", "print(\"\\nCRITICAL GAPS IDENTIFIED:\")\n", "print(\"=\" * 30)\n", "\n", "gaps = []\n", "for idx, row in comparison_df.iterrows():\n", "    if row['Extraction Accuracy (%)'] < 50:\n", "        gaps.append({\n", "            'type': row['Infrastructure Type'],\n", "            'accuracy': row['Extraction Accuracy (%)'],\n", "            'missing': row['Ground Truth'] - row['Extracted (Automated)']\n", "        })\n", "\n", "for gap in gaps:\n", "    print(f\"❌ {gap['type']}: {gap['accuracy']:.1f}% accuracy, missing {gap['missing']:.0f} entities\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualization of accuracy comparison\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# Accuracy comparison bar chart\n", "x_pos = np.arange(len(comparison_df))\n", "width = 0.35\n", "\n", "axes[0,0].bar(x_pos - width/2, comparison_df['Reference Accuracy (%)'], width, \n", "              label='Reference (castro_cad.csv)', alpha=0.8, color='blue')\n", "axes[0,0].bar(x_pos + width/2, comparison_df['Extraction Accuracy (%)'], width, \n", "              label='Extracted (Automated)', alpha=0.8, color='red')\n", "axes[0,0].set_xlabel('Infrastructure Type')\n", "axes[0,0].set_ylabel('Accuracy (%)')\n", "axes[0,0].set_title('Accuracy Comparison by Infrastructure Type')\n", "axes[0,0].set_xticks(x_pos)\n", "axes[0,0].set_xticklabels(comparison_df['Infrastructure Type'], rotation=45, ha='right')\n", "axes[0,0].legend()\n", "axes[0,0].grid(True, alpha=0.3)\n", "\n", "# Entity count comparison\n", "axes[0,1].bar(x_pos - width/2, comparison_df['Reference (castro_cad.csv)'], width, \n", "              label='Reference', alpha=0.8, color='blue')\n", "axes[0,1].bar(x_pos + width/2, comparison_df['Extracted (Automated)'], width, \n", "              label='Extracted', alpha=0.8, color='red')\n", "axes[0,1].set_xlabel('Infrastructure Type')\n", "axes[0,1].set_ylabel('Entity Count')\n", "axes[0,1].set_title('Entity Count Comparison')\n", "axes[0,1].set_xticks(x_pos)\n", "axes[0,1].set_xticklabels(comparison_df['Infrastructure Type'], rotation=45, ha='right')\n", "axes[0,1].legend()\n", "axes[0,1].grid(True, alpha=0.3)\n", "axes[0,1].set_yscale('log')  # Log scale due to large differences\n", "\n", "# Layer distribution in extracted data\n", "layer_counts = extracted_df['layer_name'].value_counts().head(10)\n", "axes[1,0].barh(range(len(layer_counts)), layer_counts.values, alpha=0.8)\n", "axes[1,0].set_yticks(range(len(layer_counts)))\n", "axes[1,0].set_yticklabels(layer_counts.index)\n", "axes[1,0].set_xlabel('Entity Count')\n", "axes[1,0].set_title('Top 10 Extracted Layers')\n", "axes[1,0].grid(True, alpha=0.3)\n", "\n", "# Entity type distribution\n", "entity_counts = extracted_df['entity_type'].value_counts()\n", "axes[1,1].pie(entity_counts.values, labels=entity_counts.index, autopct='%1.1f%%')\n", "axes[1,1].set_title('Extracted Entity Type Distribution')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Recommendations and Action Items"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate comprehensive recommendations\n", "print(\"RECOM<PERSON><PERSON>ATIONS FOR IMPROVING CAD EXTRACTION ACCURACY\")\n", "print(\"=\" * 60)\n", "\n", "recommendations = {\n", "    'Critical Issues': [\n", "        'Foundation pile detection accuracy is only 11.1% (466 vs 4199 expected)',\n", "        'No road, fencing, or electrical infrastructure detected',\n", "        'Coordinate system inconsistencies between reference and extracted data',\n", "        'Large coordinate values suggest CRS transformation issues'\n", "    ],\n", "    'Immediate Actions': [\n", "        'Review and enhance foundation pile classification rules',\n", "        'Implement proper coordinate system transformation (EPSG:32633)',\n", "        'Add detection for road, fencing, and electrical layers',\n", "        'Validate CAD layer naming conventions and entity types'\n", "    ],\n", "    'Technical Improvements': [\n", "        'Enhance LINE entity processing for foundation piles',\n", "        'Implement multi-layer infrastructure detection',\n", "        'Add geometric validation for extracted entities',\n", "        'Improve classification confidence scoring'\n", "    ],\n", "    'Validation Enhancements': [\n", "        'Implement spatial proximity validation with reference data',\n", "        'Add coordinate system validation checks',\n", "        'Create automated accuracy reporting',\n", "        'Establish extraction quality thresholds'\n", "    ]\n", "}\n", "\n", "for category, items in recommendations.items():\n", "    print(f\"\\n{category}:\")\n", "    for i, item in enumerate(items, 1):\n", "        print(f\"  {i}. {item}\")\n", "\n", "# Calculate overall accuracy score\n", "accuracy_scores = comparison_df['Extraction Accuracy (%)'].fillna(0)\n", "overall_accuracy = accuracy_scores.mean()\n", "\n", "print(f\"\\nOVERALL ASSESSMENT:\")\n", "print(f\"=\" * 20)\n", "print(f\"Overall Accuracy Score: {overall_accuracy:.1f}%\")\n", "\n", "if overall_accuracy >= 80:\n", "    status = \"✅ Excellent\"\n", "elif overall_accuracy >= 60:\n", "    status = \"✅ Good\"\n", "elif overall_accuracy >= 40:\n", "    status = \"⚠️ Needs Improvement\"\n", "else:\n", "    status = \"❌ Critical Issues\"\n", "\n", "print(f\"Status: {status}\")\n", "print(f\"\\nNext Steps: Focus on foundation pile detection and coordinate system alignment\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save detailed analysis results\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_path = Path(output_dir)\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# Save comparison results\n", "comparison_file = output_path / f\"cad_extraction_accuracy_comparison_{timestamp}.csv\"\n", "comparison_df.to_csv(comparison_file, index=False)\n", "\n", "# Save detailed analysis report\n", "analysis_report = {\n", "    'metadata': {\n", "        'timestamp': timestamp,\n", "        'project': project_type,\n", "        'analysis_type': 'cad_extraction_accuracy'\n", "    },\n", "    'data_sources': {\n", "        'reference_file': reference_file,\n", "        'extracted_file': extracted_file,\n", "        'ground_truth_file': ground_truth_file\n", "    },\n", "    'summary_statistics': {\n", "        'overall_accuracy': float(overall_accuracy),\n", "        'reference_entities': len(reference_df),\n", "        'extracted_entities': len(extracted_df),\n", "        'coordinate_overlap': x_overlap[0] <= x_overlap[1] and y_overlap[0] <= y_overlap[1]\n", "    },\n", "    'accuracy_by_type': comparison_df.to_dict('records'),\n", "    'recommendations': recommendations,\n", "    'critical_gaps': gaps\n", "}\n", "\n", "report_file = output_path / f\"cad_extraction_accuracy_report_{timestamp}.json\"\n", "with open(report_file, 'w') as f:\n", "    json.dump(analysis_report, f, indent=2, default=str)\n", "\n", "print(f\"Analysis results saved:\")\n", "print(f\"  Comparison CSV: {comparison_file.name}\")\n", "print(f\"  Detailed report: {report_file.name}\")\n", "print(f\"  Output directory: {output_path}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}