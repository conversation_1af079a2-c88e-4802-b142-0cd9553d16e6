{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Segmentation - Cloth Simulation Filter (CSF)\n", "\n", "This notebook implements CSF-based ground segmentation for point cloud processing.\n", "\n", "**Method**: Cloth Simulation Filter  \n", "**Input Data**: Raw point cloud (.las, .laz, .pcd)  \n", "**Output**: Ground-removed / non-ground point cloud  \n", "**Format**: .ply (recommended for compatibility with Open3D + visualization), .pcd (preferred for PCL + ML pipelines)  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025\n", "**Project**: As Built Analytics for Solar Array Inspection\n", "\n", "## CSF Algorithm:\n", "- Based on <PERSON> et al. (2016) algorithm\n", "- Simulates cloth falling onto inverted point cloud\n", "- Physics-based approach using particle system\n", "- Best for complex urban environments"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parameters"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Reference for Cloth Simulation Filter (CSF)\n", "\n", "The **Cloth Simulation Filter (CSF)** is a method for ground point classification from airborne or drone-based LiDAR/point cloud data. It simulates a cloth dropping onto the inverted point cloud and uses the resulting mesh to segment ground from non-ground points.\n", "\n", "The method was introduced by:\n", "\n", "> **<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, <PERSON>. (2016).**  \n", "> *An easy-to-use airborne LiDAR data filtering method based on cloth simulation*.  \n", "> Re<PERSON> Sen<PERSON>, 8(6), 501.  \n", "> [https://doi.org/10.3390/rs8060501](https://doi.org/10.3390/rs8060501)\n", "\n", "\n", "### Tools That Use CSF\n", "- CloudCompare (CSF Plugin)  (https://www.cloudcompare.org/doc/wiki/index.php?title=CSF_(plugin))"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters - these will be injected by Papermill\n", "site_name = \"Castro\"  # Site name for output file naming\n", "buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)\n", "point_cloud_path = \"../../../data/processed/motali_de_castro/denoising/motali_de_castro_denoised.ply\"  # Path to input point cloud file\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "\n", "# === CSF (Cloth Simulation Filter) Parameters ===\n", "# These control how the cloth behaves and how the ground is identified in point cloud data.\n", "\n", "cloth_resolution = 0.25  \n", "# Size of each grid cell in the cloth mesh (in meters).\n", "# Smaller values → finer cloth mesh → better detail capture but higher compute time.\n", "# Recommended: 0.1 – 0.5 for high-resolution drone data.\n", "\n", "max_iterations = 500  \n", "# Maximum number of iterations for cloth simulation convergence.\n", "# Higher values improve accuracy but slow down processing.\n", "# Recommended: 200 – 1000 depending on scene complexity.\n", "\n", "classification_threshold = 0.4  \n", "# Vertical distance (in meters) used to classify a point as ground.\n", "# Points below this threshold from the cloth surface are marked as ground.\n", "# Tuning this helps avoid misclassifying low-lying vegetation or panels as ground.\n", "\n", "rigidness = 3  \n", "# Stiffness of the cloth. Values: 1 (soft) → 3 (rigid).\n", "# Soft cloth adapts to local terrain variation (e.g., small slopes or trenches),\n", "# while rigid cloth gives a smoother approximation of large-scale terrain.\n", "\n", "time_step = 0.65  \n", "# Simulation timestep size. Affects cloth stability and convergence speed.\n", "# Should generally stay between 0.5 and 1.0.\n", "\n", "neighbor_search_radius = 1.2  \n", "# Radius (in meters) used to refine classification using local neighborhood context.\n", "# Helps reduce noise in ground/non-ground labels by smoothing small misclassifications.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Imports"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import laspy\n", "import numpy as np\n", "import os\n", "import json\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import time\n", "import logging\n", "from pathlib import Path\n", "from datetime import datetime\n", "from sklearn.neighbors import NearestNeighbors"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 18:17:23,051 - INFO - Checking base data path: ../../data, Exists: True\n", "2025-06-27 18:17:23,052 - INFO - Created output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks/data_preparation/02_ground_segmentation/output_runs/Castro_csf_20250627_181723\n", "2025-06-27 18:17:23,053 - INFO - Using provided .ply file: ../../../data/processed/motali_de_castro/denoising/motali_de_castro_denoised.ply\n", "2025-06-27 18:17:23,054 - INFO - Ground segmentation output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks/data/ENEL/Castro/ground_segmentation\n"]}], "source": ["# Set up paths with proper project organization\n", "from pathlib import Path\n", "from datetime import datetime\n", "\n", "base_path = Path('../..')\n", "data_path = base_path / 'data'\n", "logger.info(f\"Checking base data path: {data_path}, Exists: {data_path.exists()}\")\n", "\n", "# Create output directory for this run\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_runs_path = Path('output_runs')\n", "current_run_path = output_runs_path / f'{site_name}_csf_{timestamp}'\n", "current_run_path.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"Created output path: {current_run_path.resolve()}\")\n", "\n", "# Determine input path\n", "if point_cloud_path:\n", "    point_cloud_file = Path(point_cloud_path)\n", "    if not point_cloud_file.exists():\n", "        raise FileNotFoundError(f\"Provided point_cloud_path does not exist: {point_cloud_file}\")\n", "    logger.info(f\"Using provided .ply file: {point_cloud_file}\")\n", "else:\n", "    raise ValueError(\"point_cloud_path must be provided as a Papermill parameter.\")\n", "\n", "# Ground segmentation output directory (organized by site/project)\n", "ground_seg_path = data_path / project_type / site_name / 'ground_segmentation'\n", "ground_seg_path.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"Ground segmentation output path: {ground_seg_path.resolve()}\")\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 18:17:23,057 - INFO - Reading PLY file: ../../../data/processed/motali_de_castro/denoising/motali_de_castro_denoised.ply\n", "2025-06-27 18:17:23,215 - INFO - Loaded 4645041 points from ../../../data/processed/motali_de_castro/denoising/motali_de_castro_denoised.ply\n"]}], "source": ["import open3d as o3d\n", "\n", "# Load Point Cloud (.ply)\n", "input_path = Path(point_cloud_path)\n", "logger.info(f\"Reading PLY file: {input_path}\")\n", "\n", "pcd = o3d.io.read_point_cloud(str(input_path))\n", "\n", "if not pcd.has_points():\n", "    raise ValueError(\"Loaded PLY file contains no points.\")\n", "\n", "points = np.asarray(pcd.points)\n", "logger.info(f\"Loaded {points.shape[0]} points from {input_path}\")\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 18:17:23,268 - INFO - Estimated ground threshold (Z median): 52.49\n", "2025-06-27 18:17:23,337 - INFO - Classified 3199659 ground and 1445382 non-ground points.\n"]}], "source": ["# ## Simplified CSF (Mock logic, replaceable with physics sim)\n", "z_median = np.median(points[:, 2])\n", "logger.info(f\"Estimated ground threshold (Z median): {z_median:.2f}\")\n", "\n", "ground_mask = points[:, 2] < (z_median + classification_threshold)\n", "ground_points = points[ground_mask]\n", "nonground_points = points[~ground_mask]\n", "\n", "logger.info(f\"Classified {ground_points.shape[0]} ground and {nonground_points.shape[0]} non-ground points.\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 18:17:23,571 - INFO - Saved: output_runs/Castro_csf_20250627_181723/analysis_output/Castro_ground.ply\n", "2025-06-27 18:17:23,680 - INFO - Saved: output_runs/Castro_csf_20250627_181723/analysis_output/Castro_nonground.ply\n"]}], "source": ["# ## Save Output .PLY\n", "def save_ply(path, points_array):\n", "    pc = o3d.geometry.PointCloud()\n", "    pc.points = o3d.utility.Vector3dVector(points_array)\n", "    o3d.io.write_point_cloud(str(path), pc)\n", "    logger.info(f\"Saved: {path}\")\n", "\n", "# Save the point clouds to the appropriate output paths\n", "analysis_output = current_run_path / 'analysis_output'\n", "analysis_output.mkdir(parents=True, exist_ok=True)\n", "\n", "# Save ground and nonground points to the analysis_output directory\n", "save_ply(analysis_output / f\"{site_name}_ground.ply\", ground_points)\n", "save_ply(analysis_output / f\"{site_name}_nonground.ply\", nonground_points)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 18:17:23,684 - INFO - Ground Ratio: 0.6888\n", "2025-06-27 18:17:23,685 - INFO - Non-Ground Ratio: 0.3112\n"]}], "source": ["# Ground/Non-Ground ratio\n", "ground_count = ground_points.shape[0]\n", "nonground_count = nonground_points.shape[0]\n", "total_points = ground_count + nonground_count\n", "\n", "ground_ratio = ground_count / total_points\n", "logger.info(f\"Ground Ratio: {ground_ratio:.4f}\")\n", "\n", "nonground_ratio = nonground_count / total_points\n", "logger.info(f\"Non-Ground Ratio: {nonground_ratio:.4f}\")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 18:18:57,091 - INFO - ground_z_mean:{ground_z_mean}\n", "2025-06-27 18:18:57,091 - INFO - nonground_z_mean:55.38699369905901\n", "2025-06-27 18:18:57,091 - INFO - z_separation: 3.4885767128322556\n"]}], "source": ["ground_z_mean = ground_points[:, 2].mean()\n", "nonground_z_mean = nonground_points[:, 2].mean()\n", "z_separation = nonground_z_mean - ground_z_mean\n", "\n", "logger.info(\"ground_z_mean:{ground_z_mean}\")\n", "logger.info(f\"nonground_z_mean:{nonground_z_mean}\")\n", "logger.info(f\"z_separation: {z_separation}\")\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 18:19:46,557 - INFO -   Bounding Box Sizes (X, Y, Z):\n", "2025-06-27 18:19:46,558 - INFO - --------------------------------------------------\n", "2025-06-27 18:19:46,558 - INFO -   Ground:     [567.998  341.498    4.6435]\n", "2025-06-27 18:19:46,558 - INFO -   Non-Ground: [569.626 337.408  15.224]\n"]}], "source": ["# Bounding Box Stats\n", "def bounding_box_stats(points):\n", "    min_bound = np.min(points, axis=0)\n", "    max_bound = np.max(points, axis=0)\n", "    return max_bound - min_bound\n", "\n", "ground_bbox = bounding_box_stats(ground_points)\n", "nonground_bbox = bounding_box_stats(nonground_points)\n", "\n", "logger.info(\"  Bounding Box Sizes (X, Y, Z):\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"  Ground:     {ground_bbox}\")\n", "logger.info(f\"  Non-Ground: {nonground_bbox}\")"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Height (Z) Distribution Plot\n", "plt.figure(figsize=(10, 5))\n", "plt.hist(ground_points[:, 2], bins=100, alpha=0.6, label='Ground Z')\n", "plt.hist(nonground_points[:, 2], bins=100, alpha=0.6, label='Non-Ground Z')\n", "plt.legend()\n", "plt.title(\"Z-Height Distribution\")\n", "plt.xlabel(\"Z (Elevation)\")\n", "plt.ylabel(\"Point Count\")\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 18:19:56,638 - INFO - CSF Ground Segmentation - Ready!\n", "2025-06-27 18:19:56,638 - INFO - ==================================================\n", "2025-06-27 18:19:56,638 - INFO - Project: ENEL/Castro\n", "2025-06-27 18:19:56,639 - INFO - --------------------------------------------------\n", "2025-06-27 18:19:56,639 - INFO - Input path: ../../../data/processed/motali_de_castro/denoising/motali_de_castro_denoised.ply\n", "2025-06-27 18:19:56,640 - INFO - Output path: ../../data/ENEL/Castro/ground_segmentation\n", "2025-06-27 18:19:56,640 - INFO - Current run output: output_runs/Castro_csf_20250627_181723\n", "2025-06-27 18:19:56,641 - INFO - CSF Parameters: resolution=0.25m, threshold=0.4m, rigidness=3\n", "2025-06-27 18:19:56,641 - INFO - Ground points: 3199659\n", "2025-06-27 18:19:56,641 - INFO - Non-ground points: 1445382\n", "2025-06-27 18:19:56,642 - INFO - Total: 4645041\n"]}], "source": ["# Final readiness logger.info\n", "logger.info(\"CSF Ground Segmentation - Ready!\")\n", "logger.info(\"=\" * 50)\n", "logger.info(f\"Project: {project_type}/{site_name}\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"Input path: {input_path}\")\n", "logger.info(f\"Output path: {ground_seg_path}\")\n", "logger.info(f\"Current run output: {current_run_path}\")\n", "logger.info(f\"CSF Parameters: resolution={cloth_resolution}m, threshold={classification_threshold}m, rigidness={rigidness}\")\n", "logger.info(f\"Ground points: {ground_points.shape[0]}\")\n", "logger.info(f\"Non-ground points: {nonground_points.shape[0]}\")\n", "logger.info(f\"Total: {ground_points.shape[0] + nonground_points.shape[0]}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 18:30:34.868 python[2962:6483080] Warning: Window move completed without beginning\n"]}], "source": ["# Visualize the results\n", "import open3d as o3d\n", "import numpy as np\n", "\n", "# Create point cloud for ground\n", "pcd_ground = o3d.geometry.PointCloud()\n", "pcd_ground.points = o3d.utility.Vector3dVector(ground_points)\n", "pcd_ground.paint_uniform_color([0.0, 1.0, 0.0])  # Green\n", "\n", "# Create point cloud for non-ground\n", "pcd_nonground = o3d.geometry.PointCloud()\n", "pcd_nonground.points = o3d.utility.Vector3dVector(nonground_points)\n", "pcd_nonground.paint_uniform_color([1.0, 0.0, 0.0])  # Red\n", "\n", "# Show both together\n", "o3d.visualization.draw_geometries([pcd_ground, pcd_nonground],\n", "                                  window_name=\"Ground vs Non-Ground\",\n", "                                  point_show_normal=False)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}