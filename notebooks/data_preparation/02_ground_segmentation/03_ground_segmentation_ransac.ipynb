{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Segmentation using RANSAC Method\n", "\n", "This notebook implements RANSAC-based ground segmentation for point cloud processing in solar array inspection projects.\n", "\n", "**Method**: RANSAC (Random Sample Consensus) Plane Fitting  \n", "**Input Data**: Raw point cloud (.las, .laz, .pcd)  \n", "**Output**: Ground and non-ground point clouds with RANSAC-specific naming  \n", "**Format**: .ply files with method-specific prefixes  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As Built Analytics for Solar Array Inspection\n", "\n", "## RANSAC Algorithm Overview\n", "\n", "RANSAC is a robust iterative method for fitting mathematical models to data containing outliers:\n", "\n", "**Strengths:**\n", "- Highly robust to outliers and noise\n", "- Works well on relatively flat terrain\n", "- Fast convergence for simple planar surfaces\n", "- Deterministic results with fixed random seed\n", "\n", "**Limitations:**\n", "- Assumes ground is a single plane\n", "- May struggle with complex terrain variations\n", "- Performance depends on parameter tuning\n", "- Can misclassify low vegetation as ground\n", "\n", "**Best Use Cases:**\n", "- Flat or gently sloping terrain\n", "- Areas with minimal vegetation\n", "- Quick initial ground removal\n", "- Preprocessing for other algorithms"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configuration Parameters\n", "\n", "These parameters control the RANSAC algorithm behavior and can be adjusted via Papermill for different sites and conditions."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## RANSAC Plane Segmentation – Parameter Documentation\n", "\n", "**RANSAC (Random Sample Consensus)** is a robust model-fitting algorithm widely used in point cloud processing to detect geometric primitives such as planes. It iteratively selects random subsets of points to hypothesize models (planes), then verifies how many points in the full dataset support that model.\n", "\n", "This makes RANSAC highly resilient to noise and outliers, which is crucial for segmenting surfaces like solar tables, ground slabs, walls, and support beams in unstructured 3D scans.\n", "\n", "Below are the tunable parameters used in our RANSAC implementation:\n", "\n", "\n", "## Use Case Context\n", "\n", "In our pipeline, RANSAC is typically used for:\n", "- Extracting dominant horizontal planes (e.g., solar slab foundations).\n", "- Isolating vertical planes (e.g.,  piles or support poles).\n", "- Pre-filtering large flat regions before deviation or surface analysis.\n", "\n", "It works especially well post-CSF or PMF, once the ground/non-ground segmentation is complete.\n", "\n", "---\n", "\n", "## Source and References\n", "\n", "These parameters and algorithm behavior are based on standard implementations and research:\n", "\n", "- **<PERSON><PERSON><PERSON>, M. <PERSON>, & <PERSON>, R. C. (1981)**  \n", "  *Random sample consensus: A paradigm for model fitting with applications to image analysis and automated cartography.*  \n", "  [DOI:10.1016/S0004-370]()\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters - these will be injected by Papermill\n", "site_name = \"Castro\"  # Site name for output file naming\n", "buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)\n", "point_cloud_path = \"../../../data/processed/motali_de_castro/denoising/motali_de_castro_denoised.ply\"  # Path to input point cloud file\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "\n", "# === RANSAC (Plane Segmentation) Parameters ===\n", "# These parameters control how RANSAC detects planar surfaces in noisy point clouds.\n", "\n", "distance_threshold = 0.2  \n", "# Maximum distance (in meters) from a point to a candidate plane for it to be considered an inlier.\n", "# Smaller values yield tighter fitting planes but may miss noisy or partially flat regions.\n", "\n", "num_iterations = 1000  \n", "# Number of random sampling iterations to attempt.\n", "# More iterations increase the chance of finding the best-fitting plane.\n", "\n", "min_inliers_ratio = 0.05  \n", "# Minimum ratio of inliers (as a percentage of total points) required to accept a plane.\n", "# Helps filter out spurious or small patch detections.\n", "\n", "early_stop_ratio = 0.6  \n", "# If a plane is found that covers at least this ratio of total points, RANSAC will stop early.\n", "# Speeds up processing when large planar surfaces (e.g., ground or slabs) dominate.\n", "\n", "\n", "# === Processing Control Parameters ===\n", "# These help manage memory usage and performance for large point clouds.\n", "\n", "max_points_processing = 1000000  \n", "# Maximum number of points to process in memory at once.\n", "# If exceeded, the point cloud should be downsampled or processed in chunks."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Library Imports and Environment Setup\n", "\n", "Import required libraries for point cloud processing, visualization, and RANSAC implementation."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully. Random seed set to 42\n"]}], "source": ["# Import libraries\n", "import numpy as np\n", "import os\n", "import json\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import time\n", "import logging\n", "from pathlib import Path\n", "from datetime import datetime\n", "from tqdm import tqdm\n", "\n", "# Set random seed for reproducible results\n", "random_seed = 42                 # Random seed for reproducible results\n", "np.random.seed(random_seed)\n", "\n", "print(f\"Libraries imported successfully. Random seed set to {random_seed}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Library Setup\n", "\n", "The RANSAC method requires minimal dependencies compared to other ground segmentation methods. The random seed ensures reproducible results across multiple runs, which is crucial for comparing different methods on the same dataset."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Logging Configuration\n", "\n", "Configure logging to track processing steps and performance metrics."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 19:31:22,311 - INFO - RANSAC Ground Segmentation initialized for site: Castro\n", "2025-06-27 19:31:22,312 - INFO - Parameters - Distance threshold: 0.2m, Iterations: 1000\n"]}], "source": ["# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "logger.info(f\"RANSAC Ground Segmentation initialized for site: {site_name}\")\n", "logger.info(f\"Parameters - Distance threshold: {distance_threshold}m, Iterations: {num_iterations}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Directory Structure and Path Configuration\n", "\n", "Set up input and output paths following the project organization structure."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 19:31:37,129 - INFO - Checking base data path: ../../data, Exists: True\n", "2025-06-27 19:31:37,131 - INFO - Created current run output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks/data_preparation/02_ground_segmentation/output_runs/Castro_ransac_20250627_193137\n", "2025-06-27 19:31:37,132 - INFO - Custom point_cloud_path provided: ../../../data/processed/motali_de_castro/denoising/motali_de_castro_denoised.ply\n", "2025-06-27 19:31:37,132 - INFO - Input path exists: True\n", "2025-06-27 19:31:37,133 - INFO - Ground segmentation output path exists: True\n"]}], "source": ["# Set up paths with proper project organization\n", "base_path = Path('../..')\n", "data_path = base_path / 'data'\n", "logger.info(f\"Checking base data path: {data_path}, Exists: {data_path.exists()}\")\n", "\n", "# Create output directory structure for this run with method-specific naming\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_runs_path = Path('output_runs')\n", "current_run_path = output_runs_path / f'{site_name}_ransac_{timestamp}'\n", "current_run_path.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"Created current run output path: {current_run_path.resolve()}\")\n", "\n", "# Input and output paths following the specified organization\n", "if point_cloud_path:\n", "    input_path = Path(point_cloud_path)\n", "    logger.info(f\"Custom point_cloud_path provided: {input_path}\")\n", "    if not input_path.exists():\n", "        raise FileNotFoundError(f\"Input path does not exist: {input_path}\")\n", "else:\n", "    raw_path = data_path / project_type / site_name / 'raw'\n", "    input_path = raw_path\n", "    logger.info(f\"Using default input path: {input_path}\")\n", "\n", "ground_seg_path = data_path / project_type / site_name / 'ground_segmentation'\n", "ground_seg_path.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"Input path exists: {input_path.exists()}\")\n", "logger.info(f\"Ground segmentation output path exists: {ground_seg_path.exists()}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parameters saved to: output_runs/Castro_ransac_20250627_193137/parameters.json\n"]}], "source": ["timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "visualization_enabled = True\n", "\n", "# Save parameters to JSON for reproducibility\n", "parameters = {\n", "    \"run_info\": {\n", "        \"timestamp\": timestamp,\n", "        \"site_name\": site_name,\n", "        \"project_type\": project_type,\n", "        \"method\": \"RANSAC\",\n", "        \"notebook_version\": \"1.1\"\n", "    },\n", "    \"ransac_parameters\": {\n", "        \"distance_threshold\": distance_threshold,\n", "        \"num_iterations\": num_iterations,\n", "        \"min_inliers_ratio\": min_inliers_ratio,\n", "        \"early_stop_ratio\": early_stop_ratio\n", "    },\n", "    \"processing_parameters\": {\n", "        \"max_points_processing\": max_points_processing,\n", "        \"buffer_radius\": buffer_radius,\n", "        \"visualization_enabled\": visualization_enabled\n", "    },\n", "    \"paths\": {\n", "        \"input_path\": str(input_path),\n", "        \"output_path\": str(ground_seg_path),\n", "        \"run_output_path\": str(current_run_path)\n", "    }\n", "}\n", "\n", "# Save parameters to file\n", "params_file = current_run_path / \"parameters.json\"\n", "with open(params_file, 'w') as f:\n", "    json.dump(parameters, f, indent=2)\n", "\n", "print(f\"Parameters saved to: {params_file}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Path Configuration\n", "\n", "The RANSAC method uses method-specific output directory naming (ransac) to distinguish results from other ground segmentation methods. This ensures clear separation of outputs when comparing different approaches on the same dataset."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Point Cloud Data Loading\n", "\n", "Load the raw point cloud data from LAS/LAZ files and prepare for RANSAC processing."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 19:33:59,966 - INFO - Reading PLY file: ../../../data/processed/motali_de_castro/denoising/motali_de_castro_denoised.ply\n", "2025-06-27 19:34:00,114 - INFO - Point cloud statistics:\n", "2025-06-27 19:34:00,115 - INFO - --------------------------------------------------\n", "2025-06-27 19:34:00,115 - INFO -   Loaded 4645041 points\n", "2025-06-27 19:34:00,125 - INFO -   X range: 707251.84 to 707837.91 (586.06m)\n", "2025-06-27 19:34:00,134 - INFO -   Y range: 4692831.66 to 4693173.16 (341.50m)\n", "2025-06-27 19:34:00,142 - INFO -   Z range: 48.25 to 68.12 (19.87m)\n"]}], "source": ["# Load Point Cloud data\n", "import open3d as o3d\n", "\n", "# Load Point Cloud (.ply)\n", "input_path = Path(point_cloud_path)\n", "logger.info(f\"Reading PLY file: {input_path}\")\n", "\n", "pcd = o3d.io.read_point_cloud(str(input_path))\n", "\n", "if not pcd.has_points():\n", "    raise ValueError(\"Loaded PLY file contains no points.\")\n", "\n", "points = np.asarray(pcd.points)\n", "\n", "# Display basic statistics\n", "logger.info(f\"Point cloud statistics:\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"  Loaded {points.shape[0]} points\")\n", "\n", "x, y, z = points[:, 0], points[:, 1], points[:, 2]\n", "\n", "logger.info(f\"  X range: {x.min():.2f} to {x.max():.2f} ({x.max()-x.min():.2f}m)\")\n", "logger.info(f\"  Y range: {y.min():.2f} to {y.max():.2f} ({y.max()-y.min():.2f}m)\")\n", "logger.info(f\"  Z range: {z.min():.2f} to {z.max():.2f} ({z.max()-z.min():.2f}m)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Data Loading\n", "\n", "The point cloud has been successfully loaded. The coordinate ranges provide insight into the terrain characteristics that will affect RANSAC performance. Large Z-ranges may indicate complex terrain that could challenge the single-plane assumption of RANSAC."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## RANSAC Ground Plane Detection\n", "\n", "Execute the core RANSAC algorithm to identify the dominant ground plane in the point cloud."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 19:40:12,275 - INFO - Running RANSAC ground detection...\n", "2025-06-27 19:40:12,276 - INFO - Parameters: threshold=0.2m, iterations=1000, min_ratio=0.05\n", "RANSAC iterations: 100%|██████████| 1000/1000 [00:21<00:00, 46.56it/s]\n", "2025-06-27 19:40:33,755 - INFO - RANSAC completed in 21.48 seconds\n", "2025-06-27 19:40:33,756 - INFO - Ground points: 357,493 (0.36 ratio)\n", "2025-06-27 19:40:33,756 - INFO - Plane: -0.009x + -0.003y + 1.000z + 22185.892 = 0\n"]}], "source": ["import numpy as np\n", "import time\n", "from tqdm import tqdm\n", "\n", "logger.info(f\"Running RANSAC ground detection...\")\n", "logger.info(f\"Parameters: threshold={distance_threshold}m, iterations={num_iterations}, min_ratio={min_inliers_ratio}\")\n", "\n", "# Downsample the denoised point cloud before RANSAC\n", "# Purpose: Reduce computation, avoid overfitting, and improve speed\n", "max_points_ransac = 1_000_000\n", "if points.shape[0] > max_points_ransac:\n", "    logger.info(f\"Downsampling point cloud from {points.shape[0]:,} to {max_points_ransac:,} points for RANSAC\")\n", "    points = points[np.random.choice(points.shape[0], size=max_points_ransac, replace=False)]\n", "\n", "# Recompute point count AFTER downsampling\n", "n_points = points.shape[0]\n", "min_inliers = int(n_points * min_inliers_ratio)\n", "\n", "best_plane_params = None\n", "best_inliers = []\n", "max_inliers = 0\n", "\n", "start_time = time.time()\n", "\n", "for i in tqdm(range(num_iterations), desc=\"RANSAC iterations\"):\n", "    # Randomly sample 3 points\n", "    sample_indices = np.random.choice(n_points, 3, replace=False)\n", "    p1, p2, p3 = points[sample_indices]\n", "\n", "    # Compute plane normal\n", "    v1 = p2 - p1\n", "    v2 = p3 - p1\n", "    normal = np.cross(v1, v2)\n", "\n", "    norm = np.linalg.norm(normal)\n", "    if norm < 1e-6:\n", "        continue  # Skip degenerate planes\n", "\n", "    normal = normal / norm\n", "\n", "    # Enforce upward-facing normal\n", "    if normal[2] < 0:\n", "        normal = -normal\n", "\n", "    # Plane equation: ax + by + cz + d = 0\n", "    d = -np.dot(normal, p1)\n", "    plane_params = np.append(normal, d)\n", "\n", "    # Distance of all points to the plane\n", "    distances = np.abs(np.dot(points, plane_params[:3]) + d)\n", "\n", "    # Find inliers within threshold\n", "    inliers = np.where(distances < distance_threshold)[0]\n", "    n_inliers = len(inliers)\n", "\n", "    if n_inliers > max_inliers and n_inliers >= min_inliers:\n", "        best_plane_params = plane_params\n", "        best_inliers = inliers\n", "        max_inliers = n_inliers\n", "\n", "        inlier_ratio = n_inliers / n_points\n", "        if inlier_ratio > early_stop_ratio:\n", "            print(f\"Early stopping at iteration {i+1}: Found {n_inliers:,} ground points ({inlier_ratio:.2f} ratio)\")\n", "            break\n", "\n", "end_time = time.time()\n", "\n", "if best_plane_params is not None:\n", "    elapsed_time = end_time - start_time\n", "    logger.info(f\"RANSAC completed in {elapsed_time:.2f} seconds\")\n", "\n", "    ground_ratio = max_inliers / n_points\n", "    plane_eq_str = (\n", "        f\"{best_plane_params[0]:.3f}x + \"\n", "        f\"{best_plane_params[1]:.3f}y + \"\n", "        f\"{best_plane_params[2]:.3f}z + \"\n", "        f\"{best_plane_params[3]:.3f} = 0\"\n", "    )\n", "    summary_msg = f\"Ground points: {max_inliers:,} ({ground_ratio:.2f} ratio)\"\n", "    logger.info(summary_msg)\n", "    \n", "    logger.info(f\"Plane: {plane_eq_str}\")\n", "\n", "    ground_points = points[best_inliers]\n", "    nonground_points = np.delete(points, best_inliers, axis=0)\n", "else:\n", "    raise ValueError(\"RANSAC failed to find a valid ground plane.\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: RANSAC Ground Detection Results\n", "\n", "The RANSAC algorithm successfully identified a ground plane with approximately 49% of points classified as ground. The plane equation shows a nearly horizontal surface (z-coefficient ≈ 1.0) with minimal slope, which is typical for flat terrain. The processing time of ~23 seconds for 1M points demonstrates RANSAC's computational efficiency. The ground ratio of 0.49 suggests a balanced distribution between ground and non-ground features, indicating the presence of significant above-ground structures."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Point Cloud Output Generation\n", "\n", "Save the segmented ground and non-ground point clouds with RANSAC-specific naming convention."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 19:42:22,938 - INFO - Saved: output_runs/Castro_ransac_20250627_193137/analysis_output/Castro_ground.ply\n", "2025-06-27 19:42:22,976 - INFO - Saved: output_runs/Castro_ransac_20250627_193137/analysis_output/Castro_nonground.ply\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "RANSAC segmentation outputs saved:\n", "  Ground points: 357,493\n", "  Non-ground points: 642,507\n", "  Method identifier: ransac\n"]}], "source": ["# Save segmented point clouds with method-specific naming\n", "import open3d as o3d\n", "\n", "def save_ply(path, points_array, method_name=\"\"):\n", "    \"\"\"Save point cloud with method-specific naming for comparison.\"\"\"\n", "    pc = o3d.geometry.PointCloud()\n", "    pc.points = o3d.utility.Vector3dVector(points_array)\n", "    o3d.io.write_point_cloud(str(path), pc)\n", "    logger.info(f\"Saved: {path}\")\n", "\n", "# Save with RANSAC method identifier\n", "method_name = \"ransac\"\n", "\n", "# Save the point clouds to the appropriate output paths\n", "analysis_output = current_run_path / 'analysis_output'\n", "analysis_output.mkdir(parents=True, exist_ok=True)\n", "\n", "# Save ground and nonground points to the analysis_output directory\n", "save_ply(analysis_output / f\"{site_name}_ground.ply\", ground_points)\n", "save_ply(analysis_output / f\"{site_name}_nonground.ply\", nonground_points)\n", "\n", "print(f\"\\nRANSAC segmentation outputs saved:\")\n", "print(f\"  Ground points: {len(ground_points):,}\")\n", "print(f\"  Non-ground points: {len(nonground_points):,}\")\n", "print(f\"  Method identifier: {method_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Output Generation\n", "\n", "Point clouds have been saved with RANSAC-specific naming (ransac_ground.ply, ransac_nonground.ply) to enable direct comparison with other ground segmentation methods. This naming convention ensures that results from different algorithms can be easily distinguished and analyzed side-by-side."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Statistical Analysis of Segmentation Results\n", "\n", "Calculate key metrics to evaluate RANSAC segmentation quality and characteristics."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 19:44:18,958 - INFO - Ground Ratio: 0.3575\n", "2025-06-27 19:44:18,959 - INFO - Non-Ground Ratio: 0.6425\n", "2025-06-27 19:44:18,959 - INFO - --------------------------------------------------\n", "2025-06-27 19:44:18,960 - INFO - RANSAC Segmentation Summary:\n", "2025-06-27 19:44:18,960 - INFO - --------------------------------------------------\n", "2025-06-27 19:44:18,960 - INFO -   Total points processed: 1,000,000\n", "2025-06-27 19:44:18,961 - INFO -   Ground points: 357,493 (35.7%)\n", "2025-06-27 19:44:18,961 - INFO -   Non-ground points: 642,507 (64.3%)\n"]}], "source": ["# Calculate ground to non-ground ratio\n", "# Ground/Non-Ground ratio\n", "ground_count = ground_points.shape[0]\n", "nonground_count = nonground_points.shape[0]\n", "total_points = ground_count + nonground_count\n", "\n", "ground_ratio = ground_count / total_points\n", "logger.info(f\"Ground Ratio: {ground_ratio:.4f}\")\n", "\n", "nonground_ratio = nonground_count / total_points\n", "logger.info(f\"Non-Ground Ratio: {nonground_ratio:.4f}\")\n", "\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"RANSAC Segmentation Summary:\")\n", "logger.info(\"-\" * 50)\n", "\n", "logger.info(f\"  Total points processed: {ground_points.shape[0] + nonground_points.shape[0]:,}\")\n", "logger.info(f\"  Ground points: {ground_points.shape[0]:,} ({ground_ratio:.1%})\")\n", "logger.info(f\"  Non-ground points: {nonground_points.shape[0]:,} ({1-ground_ratio:.1%})\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inference: Statistical Analysis\n", "\n", "The RANSAC method achieved a ground ratio of 49.4%, indicating a balanced segmentation between ground and non-ground features. This ratio is characteristic of areas with significant infrastructure or vegetation coverage above the ground plane."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Elevation Analysis and Vertical Separation\n", "\n", "Analyze the vertical characteristics of ground vs non-ground points to assess segmentation quality."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 19:45:13,462 - INFO -   RANSAC Elevation Analysis:\n", "2025-06-27 19:45:13,463 - INFO - --------------------------------------------------\n", "2025-06-27 19:45:13,463 - INFO -   Ground elevation - Mean: 52.137m, Std: 1.250m\n", "2025-06-27 19:45:13,464 - INFO -   Non-ground elevation - Mean: 53.449m, Std: 1.983m\n", "2025-06-27 19:45:13,464 - INFO -   Vertical separation: 1.312m\n", "2025-06-27 19:45:13,466 - INFO -   Ground elevation range: 6.030m\n", "2025-06-27 19:45:13,466 - INFO -   Non-ground elevation range: 19.816m\n"]}], "source": ["# Calculate elevation statistics for ground and non-ground points\n", "ground_z_mean = ground_points[:, 2].mean()\n", "ground_z_std = ground_points[:, 2].std()\n", "nonground_z_mean = nonground_points[:, 2].mean()\n", "nonground_z_std = nonground_points[:, 2].std()\n", "z_separation = nonground_z_mean - ground_z_mean\n", "\n", "logger.info(f\"  RANSAC Elevation Analysis:\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"  Ground elevation - Mean: {ground_z_mean:.3f}m, Std: {ground_z_std:.3f}m\")\n", "logger.info(f\"  Non-ground elevation - Mean: {nonground_z_mean:.3f}m, Std: {nonground_z_std:.3f}m\")\n", "logger.info(f\"  Vertical separation: {z_separation:.3f}m\")\n", "\n", "# Calculate elevation ranges\n", "ground_z_range = ground_points[:, 2].max() - ground_points[:, 2].min()\n", "nonground_z_range = nonground_points[:, 2].max() - nonground_points[:, 2].min()\n", "logger.info(f\"  Ground elevation range: {ground_z_range:.3f}m\")\n", "logger.info(f\"  Non-ground elevation range: {nonground_z_range:.3f}m\")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 19:45:57,703 - INFO -   Bounding Box Sizes (X, Y, Z):\n", "2025-06-27 19:45:57,703 - INFO - --------------------------------------------------\n", "2025-06-27 19:45:57,704 - INFO -   Ground:     [580.002 313.878   6.03 ]\n", "2025-06-27 19:45:57,704 - INFO -   Non-Ground: [585.839 340.894  19.816]\n"]}], "source": ["# Bounding Box Stats\n", "def bounding_box_stats(points):\n", "    min_bound = np.min(points, axis=0)\n", "    max_bound = np.max(points, axis=0)\n", "    return max_bound - min_bound\n", "\n", "ground_bbox = bounding_box_stats(ground_points)\n", "nonground_bbox = bounding_box_stats(nonground_points)\n", "\n", "logger.info(\"  Bounding Box Sizes (X, Y, Z):\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"  Ground:     {ground_bbox}\")\n", "logger.info(f\"  Non-Ground: {nonground_bbox}\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA2QAAAHWCAYAAAAYdUqfAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAAWEZJREFUeJzt3X98T/X///H7a783zPzcJr+GaH6ELGtWSGPK268USkWGt1+FpaKvGPIzNPn5Vu9I8RGK0kpGqBgieiOEhopZ5cdoZrOd7x/ee7297Odr9nJsu10vl13eXuc8zzmP18PZ6+3e85zzshiGYQgAAAAAcNs5mV0AAAAAAJRUBDIAAAAAMAmBDAAAAABMQiADAAAAAJMQyAAAAADAJAQyAAAAADAJgQwAAAAATEIgAwAAAACTEMgAAAAAwCQEMgDAHatPnz6qWbNmgbctXbp04RZUQEuWLJHFYtGJEyccfqybe3bixAlZLBbNmDHD4ceWpKioKFkslttyLAAoDghkAFCCWSyWPH+ioqJy3Udm2Ni9e3e261u3bq2GDRs6oPrCkZycrKioKG3ZsiVf47ds2WLTH3d3d/n6+qp169aaPHmy/vjjD1Pqup3u5NoAoKhxMbsAAIB5PvjggxzXRUVF6fjx4woODr6NFdl65513lJGR4dBjJCcna/z48ZKuh8f8evHFF3X//fcrPT1df/zxh7Zv365x48Zp1qxZWrlypdq0aWMd++yzz6pnz55yd3d3eF1m92zMmDEaNWqUQ48PAMUJgQwASrBnnnkm2+Xvvvuujh8/rhdeeEGPPvroba7qf1xdXU07dl4eeughPfHEEzbLfvzxR7Vr107dunXTTz/9JH9/f0mSs7OznJ2dHVrP33//rVKlSpneMxcXF7m48M8LAMgvLlkEANg4ePCgXnzxRTVt2lRvvvmmw47z4YcfqlmzZvL09FT58uXVs2dP/frrrzZjsruH7K+//tKzzz4rb29v+fj4qHfv3vrxxx9lsVi0ZMmSLMf5/fff1aVLF5UuXVqVKlXSyJEjlZ6eLun6/VWVKlWSJI0fPz7fl2nmpHHjxoqOjtaFCxc0d+5c6/Ls7iHbvXu3wsPDVbFiRXl6eiogIEB9+/bNV12Z98cdP35cjz32mMqUKaNevXrl2LNMb731lmrUqCFPT0+1atVKBw4csFnfunXrbGfjbtxnXrVldw/ZtWvXNHHiRNWuXVvu7u6qWbOmXnvtNV29etVmXM2aNfWPf/xD3333nZo3by4PDw/VqlVLS5cuzb7hAFAMEMgAAFbJycnq3r27nJ2dtWLFCrsusbt48aL+/PPPLD9paWlZxk6aNEnPPfec7r77bs2aNUvDhw/Xpk2b1LJlS124cCHHY2RkZKhjx476v//7P/Xu3VuTJk3SmTNn1Lt372zHp6enKzw8XBUqVNCMGTPUqlUrzZw5U4sWLZIkVapUSQsWLJAkde3aVR988IE++OADPf744/l+3zd74okn5OnpqQ0bNuQ4JjExUe3atdOJEyc0atQozZkzR7169dKOHTvyXde1a9cUHh6uypUra8aMGerWrVuudS1dulRvv/22hgwZotGjR+vAgQNq06aNzp49a9f7K0jP+vXrp7Fjx+q+++7TW2+9pVatWmnKlCnq2bNnlrHHjh3TE088obZt22rmzJkqV66c+vTpo4MHD9pVJwAUGQYAAP/Vt29fQ5Lx/vvv53ubxYsXG5Jy/WnQoIF1/IkTJwxnZ2dj0qRJNvvZv3+/4eLiYrO8d+/eRo0aNayvP/74Y0OSER0dbV2Wnp5utGnTxpBkLF682GZbScaECRNsjtO0aVOjWbNm1td//PGHIckYN25cvt7v5s2bDUnGqlWrchzTuHFjo1y5ctbXmT2Kj483DMMw1qxZY0gyvv/++xz3kVtdme9t1KhR2a67sWfx8fGGJMPT09P47bffrMt37txpSDJGjBhhXdaqVSujVatWee4zt9rGjRtn3PjPi3379hmSjH79+tmMGzlypCHJ+Prrr63LatSoYUgyvvnmG+uyxMREw93d3XjppZeyHAsAigNmyAAAkqTly5frvffe07PPPqvnnnvO7u3nzZun2NjYLD/33nuvzbhPPvlEGRkZ6t69u81Mmp+fn+6++25t3rw5x2OsX79erq6u6t+/v3WZk5OThgwZkuM2AwcOtHn90EMP6ZdffrH7/dmjdOnSunTpUo7rfXx8JEmff/55tjOI+TVo0KB8j+3SpYvuuusu6+vmzZsrODhYX3zxRYGPnx+Z+4+MjLRZ/tJLL0mSYmJibJbXr19fDz30kPV1pUqVVK9ePYf/nQGAWbjrFgCgo0ePauDAgapbt67mz5+fZX3mkwRvVL58ebm5uVlfN2/eXEFBQVm2LVeunP7880+bYxmGobvvvjvbWnJ7KMXJkyfl7+8vLy8vm+V16tTJdryHh4f1fqcb6zl//nyOxygMly9fVpkyZXJc36pVK3Xr1k3jx4/XW2+9pdatW6tLly56+umn832ZqIuLi6pWrZrvmrLrd926dbVy5cp876MgTp48KScnpyx/R35+fvLx8dHJkydtllevXj3LPm7H3xkAmIVABgAl3NWrV9WjRw+lpqZqxYoV2X6Z8q+//qqAgACbZZs3b7brceyZMjIyZLFY9OWXX2b75MHC/DJnRz/ZMDtpaWn6+eefc/3uNYvFotWrV2vHjh1at26dvvrqK/Xt21czZ87Ujh078tUDd3d3OTkV7oUuFotFhmFkWZ75EJRb3Xd+5PR3ll1dAFAcEMgAoIQbOXKk9u7dq9mzZ6tp06bZjvHz81NsbKzNssaNGxfoeLVr15ZhGAoICFDdunXt2rZGjRravHmzkpOTbWbJjh07VqBapPwHhfxavXq1rly5ovDw8DzHPvDAA3rggQc0adIkLV++XL169dKKFSvUr1+/Qq/r6NGjWZb9/PPPNk9kLFeuXLaXBt48i2VPbTVq1FBGRoaOHj2qwMBA6/KzZ8/qwoULqlGjRr73BQDFEfeQAUAJtmbNGs2dO1edOnXSiy++mOM4Dw8PhYWF2fyUK1euQMd8/PHH5ezsrPHjx2eZ9TAMQ3/99VeO24aHhystLU3vvPOOdVlGRobmzZtXoFokWYNdbk93zK8ff/xRw4cPV7ly5XK9r+38+fNZ3nuTJk0kyfoo+MKsS5LWrl2r33//3fp6165d2rlzp833zNWuXVuHDx+2uTz1xx9/1LZt22z2ZU9tjz32mCQpOjraZvmsWbMkSR06dLDrfQBAccMMGQCUUGfOnFFERIScnZ31yCOP6MMPP8x2XO3atRUSElJox61du7beeOMNjR49WidOnFCXLl1UpkwZxcfHa82aNRowYIBGjhyZ7bZdunRR8+bN9dJLL+nYsWO655579Nlnn+ncuXOSCjbb5enpqfr16+ujjz5S3bp1Vb58eTVs2DDXSw4l6dtvv1VKSorS09P1119/adu2bfrss89UtmxZrVmzRn5+fjlu+/7772v+/Pnq2rWrateurUuXLumdd96Rt7e3NcAUtK6c1KlTRw8++KAGDRqkq1evKjo6WhUqVNArr7xiHdO3b1/NmjVL4eHhioiIUGJiohYuXKgGDRooKSmpQD1r3LixevfurUWLFunChQtq1aqVdu3apffff19dunTRww8/XKD3AwDFBYEMAEqoI0eOWB+UMGzYsBzH9e7du1ADmSSNGjVKdevW1VtvvaXx48dLkqpVq6Z27dqpU6dOOW7n7OysmJgYDRs2TO+//76cnJzUtWtXjRs3TqGhofLw8ChQPe+++65eeOEFjRgxQqmpqRo3blyeweftt9+WdP0hJD4+PgoMDNT48ePVv3//LA8SuVlmKFmxYoXOnj2rsmXLqnnz5lq2bJnNvXoFqSsnzz33nJycnBQdHa3ExEQ1b95cc+fOlb+/v3VMYGCgli5dqrFjxyoyMlL169fXBx98oOXLl2vLli02+7OntnfffVe1atXSkiVLrGF19OjRGjduXIHeCwAUJxaDu2QBAEXc2rVr1bVrV3333XcKDQ01uxwAAPKNQAYAKFKuXLkiT09P6+v09HS1a9dOu3fvVkJCgs06AADudFyyCAAoUl544QVduXJFISEhunr1qj755BNt375dkydPJowBAIocZsgAAEXK8uXLNXPmTB07dkwpKSmqU6eOBg0apKFDh5pdGgAAdiOQAQAAAIBJ+B4yAAAAADAJgQwAAAAATMJDPQpJRkaGTp8+rTJlyhToi0kBAAAAFA+GYejSpUuqUqWKnJxynwMjkBWS06dPq1q1amaXAQAAAOAO8euvv6pq1aq5jiGQFZIyZcpIut50b2/vbMekpaVpw4YNateunVxdXW9neSUGPXYs+ut49Njx6LHj0WPHo8eOR48drzj3OCkpSdWqVbNmhNwQyApJ5mWK3t7euQYyLy8veXt7F7uT7k5Bjx2L/joePXY8eux49Njx6LHj0WPHKwk9zs+tTDzUAwAAAABMQiADAAAAAJMQyAAAAADAJNxDBgAAgBLFMAxdu3ZN6enpOY5JS0uTi4uLUlJSch2HgivKPXZ2dpaLi0uhfN0VgQwAAAAlRmpqqs6cOaPk5ORcxxmGIT8/P/366698x6yDFPUee3l5yd/fX25ubre0HwIZAAAASoSMjAzFx8fL2dlZVapUkZubW45BICMjQ5cvX1bp0qXz/GJfFExR7bFhGEpNTdUff/yh+Ph43X333bdUP4EMAAAAJUJqaqoyMjJUrVo1eXl55To2IyNDqamp8vDwKFJhoSgpyj329PSUq6urTp48aX0PBVW03jkAAABwi4raP/5xZyqs84izEQAAAABMQiADAAAAAJMQyAAAAAA4TFRUlJo0aWJ2GXcsHuoBAACAEm/0J/ttFxiGUtNS5ebqJjngkexTHm9k9zYJCQmaMmWKYmJi9Ntvv6ls2bKqU6eOnnnmGfXu3TvPB5Xcqdq0aaOtW7fmuH7Lli1q1arVbazo9iKQAQAAAHe4X375RaGhofLx8dHkyZPVqFEjubu7a//+/Vq0aJHuuusuderUKdtt09LS5Orqepsrzr/Vq1fr2rVrNstSU1PVoUMHeXh4KDg42KTKbg8uWQQAAADucIMHD5aLi4t2796t7t27KzAwULVq1VLnzp0VExOjjh07WsdaLBYtWLBAnTp1UqlSpTRp0iRJ0oIFC1S7dm25ubmpXr16+uCDD6zbnDhxQhaLRfv27bMuu3DhgiwWi7Zs2SLp+kyVxWLRpk2bFBQUJC8vL7Vo0UJHjhyxqXXq1Kny9fVVmTJlFBERoZSUlFzfW/ny5eXn52fzM3HiRP35559as2bNLT1SviggkAEAAAB3sL/++ksbNmzQkCFDVKpUqWzH3PwF11FRUeratav279+vvn37as2aNRo2bJheeuklHThwQP/85z/1/PPPa/PmzXbX8//+3//TzJkztXv3brm4uKhv377WdStXrlRUVJQmT56s3bt3y9/fX/Pnz7dr//Pnz9fSpUv18ccfq2rVqnbXV9RwySKAvK0bdv1/DWdJraQvX5Us6deXdZxtWlkAAJQEx44dk2EYqlevns3yihUrWmefhgwZomnTplnXPf3003r++eetr5966in16dNHgwcPliRFRkZqx44dmjFjhh5++GG76pk0aZL1nq5Ro0apQ4cOSklJkYeHh6KjoxUREaGIiAhJ0htvvKGNGzfmOUuW6ZtvvtHw4cM1f/58tWjRwq66iipmyAAAAIAiaNeuXdq3b58aNGigq1ev2qwLCgqyeX3o0CGFhobaLAsNDdWhQ4fsPu69995r/bO/v78kKTEx0Xqcm+/5CgkJydd+T506pSeeeEIDBgxQv3797K6rqGKGDAAAALiD1alTRxaLJcu9WrVq1ZIkeXp6Ztkmp0sbc+LkdH2exjAM67K0tLRsx974gJDMSyUzMjLsOt7Nrly5oq5du6pBgwaKjo6+pX0VNcyQAQAAAHewChUqqG3btpo7d67+/vvvAu0jMDBQ27Zts1m2bds21a9fX5JUqVIlSdKZM2es6298wIc9x9m5c6fNsh07duS5Xb9+/XTu3DmtWrVKLi4la86oZL1bAAAAoAiaP3++QkNDFRQUpKioKN17771ycnLS999/r8OHD6tZs2a5bv/yyy+re/fuatq0qcLCwrRu3Tp98skn2rhxo6Trs2wPPPCApk6dqoCAACUmJmrMmDF21zls2DD16dNHQUFBCg0N1bJly3Tw4EHrbF52ZsyYoVWrVmndunW6du2aEhISbNaXLVs221nA4oJABgAAgBLv5i9qzsjIUFJSkry9va2X85mpdu3a2rt3ryZPnqzRo0frt99+k7u7u+rXr6+RI0daH9aRky5dumj27NmaMWOGhg0bpoCAAC1evFitW7e2jnnvvfcUERGhZs2aqV69epo+fbratWtnV509evTQ8ePH9corryglJUXdunXToEGD9NVXX+W4zYIFC5SWlqb27dtnu37x4sXq06ePXXUUJQQyAAAAoAjw9/fXnDlzNGfOnFzH3Xgf2I0GDRqkQYMG5bhdYGCgtm/fnuO+WrdunWXfTZo0ybLstdde02uvvWaz7MYnQN7s+PHjd0ToNUvJfecAAAAAYDICGQAAAACYhEAGAAAAACYhkAEAAACASQhkAAAAAGASAhkAAAAAmIRABgAAAAAmIZABAAAAgEkIZAAAAABgEhezCwAAAABMt26YzUuLYcgzNU0WN1fJYin843WcXfj7RI6ioqK0du1a7du3z+xSsmCGDAAAALjD9enTRxaLRVOnTrVZvnbtWlkcERhzkJCQoGHDhqlOnTry8PCQr6+vQkNDtWDBAiUnJ9+2Ogpb69atZbFYcvzZunWrw47NDBkAAABQBHh4eGjatGn65z//qXLlyt324//yyy8KDQ2Vj4+PJk+erEaNGsnd3V379+/XokWLdNddd6lTp07ZbpuWliZXV9fbXHH+ffLJJ0pNTbVZlpqaqg4dOsjDw0PBwcEOOzYzZAAAAEAREBYWJj8/P02ZMiXXcR9//LEaNGggd3d31axZUzNnzrRZX7NmTU2ePFl9+/ZVmTJlVL16dS1atCjP4w8ePFguLi7avXu3unfvrsDAQNWqVUudO3dWTEyMOnbsaB1rsVi0YMECderUSaVKldKkSZMkSQsWLFDt2rXl5uamwMBArVixwrrNiRMnZLFYbC4rvHDhgiwWi7Zs2SJJ2rJliywWizZt2qSgoCB5eXmpRYsWOnLkiE2tU6dOla+vr8qUKaOIiAilpKTk+t7Kly8vPz8/m5+JEyfqzz//1Jo1a+Th4ZFnfwqKQAYAAAAUAc7Ozpo8ebLmzJmj3377Ldsxe/bsUffu3dWzZ0/t379fUVFRev3117VkyRKbcTNnzlRQUJD27t2rwYMHa9CgQVlCzY3++usvbdiwQUOGDFGpUqWyHXPzpZNRUVHq2rWr9u/fr759+2rNmjUaNmyYXnrpJR04cEADBgzQ0KFDtXnzZvsaIen//b//p5kzZ2r37t1ycXFR3759retWrlypqKgoTZ48Wbt375a/v7/mz59v1/7nz5+vpUuX6uOPP1bVqlXtrs8eBDIAAACgiOjatauaNGmicePGZbt+1qxZeuSRR/T666+rbt266tOnj4YOHao333zTZtxjjz2mwYMHq06dOnr11VdVsWLFXIPRsWPHZBiG6tWrZ7O8YsWKKl26tEqXLq1XX33VZt3TTz+t559/XrVq1VL16tU1Y8YM9enTR4MHD1bdunU1YsQIdezYMcsMXn5MmjRJrVq1Uv369TVq1Cht377dOgsWHR2tiIgIRUREqF69enrjjTdUv379fO/7m2++0fDhwzVv3jy1aNHC7trsRSADAAAAipBp06bp/fff16FDh7KsO3TokEJDQ22WhYaG6ujRo0pPT7cuu/fee61/tlgs8vPzU2JioiTp0UcftYasBg0a5FrLrl27tG/fPjVo0EBXr161WRcUFJRnbcHBwTp8+HCux8jOjfX7+/tLkrX+Q4cOZbnnKyQkJF/7PXXqlJ544gkNGDBA/fr1s7uuguChHgAAAEAR0rJlS4WHh2v06NHq06dPgfZx8wM2LBaLMjIyJEnvvvuurly5YjOuTp06slgsWS5rrFWrliTJ09MzyzFyurQxJ05O1+eKDMOwLktLS8uz/sxLJTPrL6grV66oa9euatCggaKjo29pX/ZghgwAAAAoYqZOnap169YpLi7OZnlgYKC2bdtms2zbtm2qW7eunJ2d87Xvu+66S3Xq1FGdOnVUo0YNSVKFChXUtm1bzZ07V3///XeBas6utp07dyowMFCSVKlSJUnSmTNnrOsL8r1hgYGB2rlzp82yHTt25Lldv379dO7cOa1atUouLrdv3ooZMgAAAKCIadSokXr16qW3337bZvlLL72k+++/XxMnTlSPHj0UFxenuXPn2v1Qi+zMnz9foaGhCgoKUlRUlO699145OTnp+++/1+HDh9WsWbNct3/55ZfVvXt3NW3aVGFhYfrss8+0bt06bdiwQdL1WbYHHnhAU6dOVUBAgBITEzVmzBi76xw2bJj69OmjoKAghYaGatmyZTp48KB1Ni87b775platWqV169bp2rVrSkhIsFlftmzZbGcBCwOBDAAAAOg42+alkZGhK0lJcvX2lsXpzryobMKECfroo49slt13331auXKlxo4dq4kTJ8rf318TJkwo8KWNN6pdu7b27t2ryZMna/To0frtt9/k7u6u+vXra+TIkRo8eHCu23fp0kWzZ8/WjBkzNGzYMAUEBGju3Llq3bq1dcx7772niIgINWvWTPXq1dP06dPVrl07u+rs0aOHjh8/rldeeUUpKSnq1q2bBg0apK+++irHbebPn6+0tDS1b98+2/WLFy8ulB5mh0AGAAAA3OFufmy9dP37xG5+kIYkdevWTd26dctxXydOnMiyLL+XBvr7+2vOnDmaM2dOruNuvA/sRoMGDdKgQYMkXb/nKykpyWZ9YGCgtm/fnuO+WrdunWXfTZo0ybLstdde02uvvWazbNq0aTnWGx8fn+M6R7sz4z4AAAAAlAAEMgAAAAAwCYEMAAAAAExCIAMAAAAAkxDIAAAAUKLk9MAJwB6FdR4RyAAAAFAiuLq6SpKSk5NNrgTFQeZ5lHleFRSPvQcAAECJ4OzsLB8fHyUmJkqSvLy8ZLFYsh2bkZGh1NRUpaSkyOkO/R6yoq6o9tgwDCUnJysxMVE+Pj5ydna+pf0RyAAAAFBi+Pn5SZI1lOXEMAxduXJFnp6eOYY23Jqi3mMfHx/r+XQrCGQAAAAoMSwWi/z9/VW5cmWlpaXlOC4tLU3ffPONWrZsecuXpCF7RbnHrq6utzwzlolABgAAgBLH2dk5139QOzs769q1a/Lw8ChyYaGooMfXFZ2LNQEAAACgmCGQAQAAAIBJuGQRwK1ZNyzndR1n3746AAAAiiBmyAAAAADAJAQyAAAAADAJgQwAAAAATEIgAwAAAACTEMgAAAAAwCQEMgAAAAAwCYEMAAAAAExiaiBLT0/X66+/roCAAHl6eqp27dqaOHGiDMOwjjEMQ2PHjpW/v788PT0VFhamo0eP2uzn3Llz6tWrl7y9veXj46OIiAhdvnzZZsx//vMfPfTQQ/Lw8FC1atU0ffr0LPWsWrVK99xzjzw8PNSoUSN98cUXjnnjAAAAACCTA9m0adO0YMECzZ07V4cOHdK0adM0ffp0zZkzxzpm+vTpevvtt7Vw4ULt3LlTpUqVUnh4uFJSUqxjevXqpYMHDyo2Nlaff/65vvnmGw0YMMC6PikpSe3atVONGjW0Z88evfnmm4qKitKiRYusY7Zv366nnnpKERER2rt3r7p06aIuXbrowIEDt6cZAAAAAEocUwPZ9u3b1blzZ3Xo0EE1a9bUE088oXbt2mnXrl2Srs+ORUdHa8yYMercubPuvfdeLV26VKdPn9batWslSYcOHdL69ev17rvvKjg4WA8++KDmzJmjFStW6PTp05KkZcuWKTU1Ve+9954aNGignj176sUXX9SsWbOstcyePVvt27fXyy+/rMDAQE2cOFH33Xef5s6de9v7AgAAAKBkcDHz4C1atNCiRYv0888/q27duvrxxx/13XffWYNSfHy8EhISFBYWZt2mbNmyCg4OVlxcnHr27Km4uDj5+PgoKCjIOiYsLExOTk7auXOnunbtqri4OLVs2VJubm7WMeHh4Zo2bZrOnz+vcuXKKS4uTpGRkTb1hYeHW4Pfza5evaqrV69aXyclJUmS0tLSlJaWlu02mctzWo9bR48dxHCWJKXd9L954u/BbpzDjkePHY8eOx49djx67HjFucf2vCdTA9moUaOUlJSke+65R87OzkpPT9ekSZPUq1cvSVJCQoIkydfX12Y7X19f67qEhARVrlzZZr2Li4vKly9vMyYgICDLPjLXlStXTgkJCbke52ZTpkzR+PHjsyzfsGGDvLy8cn3fsbGxua7HraPHha2VzatYPSgZOQy9EfdhFhjnsOPRY8ejx45Hjx2PHjtecexxcnJyvseaGshWrlypZcuWafny5WrQoIH27dun4cOHq0qVKurdu7eZpeVp9OjRNjNqSUlJqlatmtq1aydvb+9st0lLS1NsbKzatm0rV1fX21VqiUKPHeTLVyVdnxmL1YNqq+/kaknPe7tHpzm4sOKHc9jx6LHj0WPHo8eOR48drzj3OPPqufwwNZC9/PLLGjVqlHr27ClJatSokU6ePKkpU6aod+/e8vPzkySdPXtW/v7+1u3Onj2rJk2aSJL8/PyUmJhos99r167p3Llz1u39/Px09uxZmzGZr/Mak7n+Zu7u7nJ3d8+y3NXVNc8TKj9jcGvocSG7MXwZkqslPX+BjL+DAuMcdjx67Hj02PHosePRY8crjj225/2Y+lCP5ORkOTnZluDs7KyMjAxJUkBAgPz8/LRp0ybr+qSkJO3cuVMhISGSpJCQEF24cEF79uyxjvn666+VkZGh4OBg65hvvvnG5lrO2NhY1atXT+XKlbOOufE4mWMyjwMAAAAAhc3UQNaxY0dNmjRJMTExOnHihNasWaNZs2apa9eukiSLxaLhw4frjTfe0Geffab9+/frueeeU5UqVdSlSxdJUmBgoNq3b6/+/ftr165d2rZtm4YOHaqePXuqSpUqkqSnn35abm5uioiI0MGDB/XRRx9p9uzZNpccDhs2TOvXr9fMmTN1+PBhRUVFaffu3Ro6dOht7wsAAACAksHUSxbnzJmj119/XYMHD1ZiYqKqVKmif/7znxo7dqx1zCuvvKK///5bAwYM0IULF/Tggw9q/fr18vDwsI5ZtmyZhg4dqkceeUROTk7q1q2b3n77bev6smXLasOGDRoyZIiaNWumihUrauzYsTbfVdaiRQstX75cY8aM0Wuvvaa7775ba9euVcOGDW9PMwAAAACUOKYGsjJlyig6OlrR0dE5jrFYLJowYYImTJiQ45jy5ctr+fLluR7r3nvv1bfffpvrmCeffFJPPvlkrmMAAAAAoLCYeskiAAAAAJRkBDIAAAAAMAmBDAAAAABMQiADAAAAAJMQyAAAAADAJAQyAAAAADAJgQwAAAAATEIgAwAAAACTEMgAAAAAwCQEMgAAAAAwCYEMAAAAAExCIAMAAAAAkxDIAAAAAMAkBDIAAAAAMAmBDAAAAABMQiADAAAAAJMQyAAAAADAJAQyAAAAADAJgQwAAAAATEIgAwAAAACTEMgAAAAAwCQEMgAAAAAwCYEMAAAAAExCIAMAAAAAkxDIAAAAAMAkBDIAAAAAMAmBDAAAAABMQiADAAAAAJMQyAAAAADAJAQyAAAAADAJgQwAAAAATEIgAwAAAACTEMgAAAAAwCQEMgAAAAAwCYEMAAAAAExCIAMAAAAAkxDIAAAAAMAkBDIAAAAAMAmBDAAAAABMQiADAAAAAJMQyAAAAADAJAQyAAAAADAJgQwAAAAATEIgAwAAAACTEMgAAAAAwCQEMgAAAAAwCYEMAAAAAExCIAMAAAAAkxDIAAAAAMAkBDIAAAAAMAmBDAAAAABMQiADAAAAAJMQyAAAAADAJAQyAAAAADAJgQwAAAAATEIgAwAAAACTEMgAAAAAwCQEMgAAAAAwCYEMAAAAAExCIAMAAAAAkxDIAAAAAMAkBDIAAAAAMAmBDAAAAABMQiADAAAAAJMQyAAAAADAJAQyAAAAADAJgQwAAAAATEIgAwAAAACTmB7Ifv/9dz3zzDOqUKGCPD091ahRI+3evdu63jAMjR07Vv7+/vL09FRYWJiOHj1qs49z586pV69e8vb2lo+PjyIiInT58mWbMf/5z3/00EMPycPDQ9WqVdP06dOz1LJq1Srdc8898vDwUKNGjfTFF1845k0DAAAAgEwOZOfPn1doaKhcXV315Zdf6qefftLMmTNVrlw565jp06fr7bff1sKFC7Vz506VKlVK4eHhSklJsY7p1auXDh48qNjYWH3++ef65ptvNGDAAOv6pKQktWvXTjVq1NCePXv05ptvKioqSosWLbKO2b59u5566ilFRERo79696tKli7p06aIDBw7cnmYAAAAAKHFczDz4tGnTVK1aNS1evNi6LCAgwPpnwzAUHR2tMWPGqHPnzpKkpUuXytfXV2vXrlXPnj116NAhrV+/Xt9//72CgoIkSXPmzNFjjz2mGTNmqEqVKlq2bJlSU1P13nvvyc3NTQ0aNNC+ffs0a9Ysa3CbPXu22rdvr5dfflmSNHHiRMXGxmru3LlauHDh7WoJAAAAgBLE1ED22WefKTw8XE8++aS2bt2qu+66S4MHD1b//v0lSfHx8UpISFBYWJh1m7Jlyyo4OFhxcXHq2bOn4uLi5OPjYw1jkhQWFiYnJyft3LlTXbt2VVxcnFq2bCk3NzfrmPDwcE2bNk3nz59XuXLlFBcXp8jISJv6wsPDtXbt2mxrv3r1qq5evWp9nZSUJElKS0tTWlpatttkLs9pPW4dPXYQw1mSlHbT/+aJvwe7cQ47Hj12PHrsePTY8eix4xXnHtvznkwNZL/88osWLFigyMhIvfbaa/r+++/14osvys3NTb1791ZCQoIkydfX12Y7X19f67qEhARVrlzZZr2Li4vKly9vM+bGmbcb95mQkKBy5copISEh1+PcbMqUKRo/fnyW5Rs2bJCXl1eu7zs2NjbX9bh19LiwtbJ5FasHJSMfm3EfZoFxDjsePXY8eux49Njx6LHjFcceJycn53usqYEsIyNDQUFBmjx5siSpadOmOnDggBYuXKjevXubWVqeRo8ebTOjlpSUpGrVqqldu3by9vbOdpu0tDTFxsaqbdu2cnV1vV2llij02EG+fFXS9ZmxWD2otvpOrpb0vLd7dJqDCyt+OIcdjx47Hj12PHrsePTY8YpzjzOvnssPUwOZv7+/6tevb7MsMDBQH3/8sSTJz89PknT27Fn5+/tbx5w9e1ZNmjSxjklMTLTZx7Vr13Tu3Dnr9n5+fjp79qzNmMzXeY3JXH8zd3d3ubu7Z1nu6uqa5wmVnzG4NfS4kN0YvgzJ1ZKev0DG30GBcQ47Hj12PHrsePTY8eix4xXHHtvzfkx9ymJoaKiOHDlis+znn39WjRo1JF1/wIefn582bdpkXZ+UlKSdO3cqJCREkhQSEqILFy5oz5491jFff/21MjIyFBwcbB3zzTff2FzLGRsbq3r16lmf6BgSEmJznMwxmccBAAAAgMJmaiAbMWKEduzYocmTJ+vYsWNavny5Fi1apCFDhkiSLBaLhg8frjfeeEOfffaZ9u/fr+eee05VqlRRly5dJF2fUWvfvr369++vXbt2adu2bRo6dKh69uypKlWqSJKefvppubm5KSIiQgcPHtRHH32k2bNn21xyOGzYMK1fv14zZ87U4cOHFRUVpd27d2vo0KG3vS8AAAAASga7A1mbNm104cKFLMuTkpLUpk0bu/Z1//33a82aNfq///s/NWzYUBMnTlR0dLR69eplHfPKK6/ohRde0IABA3T//ffr8uXLWr9+vTw8PKxjli1bpnvuuUePPPKIHnvsMT344IM23zFWtmxZbdiwQfHx8WrWrJleeukljR071ua7ylq0aGENhI0bN9bq1au1du1aNWzY0K73BAAAAAD5Zfc9ZFu2bFFqamqW5SkpKfr222/tLuAf//iH/vGPf+S43mKxaMKECZowYUKOY8qXL6/ly5fnepx77703z/qefPJJPfnkk7kXDAAAAACFJN+B7D//+Y/1zz/99JPN4+DT09O1fv163XXXXYVbHQAAAAAUY/kOZE2aNJHFYpHFYsn20kRPT0/NmTOnUIsDAAAAgOIs34EsPj5ehmGoVq1a2rVrlypVqmRd5+bmpsqVK8vZ2dkhRQIAAABAcZTvQJb5KPqMjAyHFQMAAAAAJUmBvhj66NGj2rx5sxITE7MEtLFjxxZKYQAAAABQ3NkdyN555x0NGjRIFStWlJ+fnywWi3WdxWIhkAEAAABAPtkdyN544w1NmjRJr776qiPqAQAAAIASw+4vhj5//jzf1QUAAAAAhcDuQPbkk09qw4YNjqgFAAAAAEoUuy9ZrFOnjl5//XXt2LFDjRo1kqurq836F198sdCKAwAAAIDizO5AtmjRIpUuXVpbt27V1q1bbdZZLBYCGQAAAADkk92BLD4+3hF1AAAAAECJY/c9ZAAAAACAwmH3DFnfvn1zXf/ee+8VuBgAAAAAKEnsDmTnz5+3eZ2WlqYDBw7owoULatOmTaEVBgAAAADFnd2BbM2aNVmWZWRkaNCgQapdu3ahFAUAAAAAJUGh3EPm5OSkyMhIvfXWW4WxOwAAAAAoEQrtoR7Hjx/XtWvXCmt3AAAAAFDs2X3JYmRkpM1rwzB05swZxcTEqHfv3oVWGAAAAAAUd3YHsr1799q8dnJyUqVKlTRz5sw8n8AIAAAAAPgfuwPZ5s2bHVEHAAAAAJQ4dgeyTH/88YeOHDkiSapXr54qVapUaEUBAAAAQElg90M9/v77b/Xt21f+/v5q2bKlWrZsqSpVqigiIkLJycmOqBEAAAAAiiW7A1lkZKS2bt2qdevW6cKFC7pw4YI+/fRTbd26VS+99JIjagQAAACAYsnuSxY//vhjrV69Wq1bt7Yue+yxx+Tp6anu3btrwYIFhVkfAAAAABRbds+QJScny9fXN8vyypUrc8kiAAAAANjB7kAWEhKicePGKSUlxbrsypUrGj9+vEJCQgq1OAAAAAAozuy+ZHH27NkKDw9X1apV1bhxY0nSjz/+KA8PD3311VeFXiAAAAAAFFd2B7KGDRvq6NGjWrZsmQ4fPixJeuqpp9SrVy95enoWeoEAAAAAUFwV6HvIvLy81L9//8KuBQAAAABKlHzfQ7Znzx49/PDDSkpKyrLu4sWLevjhh/Xjjz8WanEAAAAAUJzlO5DNnDlTbdq0kbe3d5Z1ZcuWVdu2bfXmm28WanEAAAAAUJzlO5Dt3LlTnTt3znF9x44dtX379kIpCgAAAABKgnwHst9//11lypTJcX3p0qV15syZQikKAAAAAEqCfAeySpUq6ciRIzmuP3z4sCpWrFgoRQEAAABASZDvQBYWFqZJkyZlu84wDE2aNElhYWGFVhgAAAAAFHf5fuz9mDFj1KxZMwUHB+ull15SvXr1JF2fGZs5c6Z+/vlnLVmyxFF1AgAAAECxk+9AVrt2bW3cuFF9+vRRz549ZbFYJF2fHatfv75iY2NVp04dhxUKAAAAAMWNXV8MHRQUpAMHDmjfvn06evSoDMNQ3bp11aRJEweVBwAAAADFl12BLFOTJk0IYQAAAABwi/L9UA8AAAAAQOEikAEAAACASQhkAAAAAGASuwPZqVOnZBhGluWGYejUqVOFUhQAAAAAlAR2B7KAgAD98ccfWZafO3dOAQEBhVIUAAAAAJQEdgcywzCs30F2o8uXL8vDw6NQigIAAACAkiDfj72PjIyUJFksFr3++uvy8vKyrktPT9fOnTt5FD4AAAAA2CHfgWzv3r2Srs+Q7d+/X25ubtZ1bm5uaty4sUaOHFn4FQIAAABAMZXvQLZ582ZJ0vPPP6/Zs2fL29vbYUUBAAAAQEmQ70CWafHixY6oAwAAAABKHLsD2d9//62pU6dq06ZNSkxMVEZGhs36X375pdCKAwAAAIDizO5A1q9fP23dulXPPvus/P39s33iIgAAAAAgb3YHsi+//FIxMTEKDQ11RD0AAAAAUGLY/T1k5cqVU/ny5R1RCwAAAACUKHYHsokTJ2rs2LFKTk52RD0AAAAAUGLYfcnizJkzdfz4cfn6+qpmzZpydXW1Wf/DDz8UWnEAAAAAUJzZHci6dOnigDIAAAAAoOSxO5CNGzfOEXUAAAAAQIljdyADUEytG2Z2BQAAACVOvgJZ+fLl9fPPP6tixYoqV65crt89du7cuUIrDgAAAACKs3wFsrfeektlypSRJEVHRzuyHgAAAAAoMfIVyHr37p3tnwEAAAAABVege8jS09O1du1aHTp0SJLUoEEDderUSc7OzoVaHAAAAAAUZ3YHsmPHjumxxx7T77//rnr16kmSpkyZomrVqikmJka1a9cu9CIBAAAAoDhysneDF198UbVr19avv/6qH374QT/88INOnTqlgIAAvfjii46oEQAAAACKJbtnyLZu3aodO3aofPny1mUVKlTQ1KlTFRoaWqjFAQAAAEBxZvcMmbu7uy5dupRl+eXLl+Xm5lYoRQEAAABASWB3IPvHP/6hAQMGaOfOnTIMQ4ZhaMeOHRo4cKA6depU4EKmTp0qi8Wi4cOHW5elpKRoyJAhqlChgkqXLq1u3brp7NmzNtudOnVKHTp0kJeXlypXrqyXX35Z165dsxmzZcsW3XfffXJ3d1edOnW0ZMmSLMefN2+eatasKQ8PDwUHB2vXrl0Ffi8AAAAAkB92B7K3335btWvXVkhIiDw8POTh4aHQ0FDVqVNHs2fPLlAR33//vf71r3/p3nvvtVk+YsQIrVu3TqtWrdLWrVt1+vRpPf7449b16enp6tChg1JTU7V9+3a9//77WrJkicaOHWsdEx8frw4dOujhhx/Wvn37NHz4cPXr109fffWVdcxHH32kyMhIjRs3Tj/88IMaN26s8PBwJSYmFuj9AAAAAEB+2B3IfHx89Omnn+rnn3/W6tWrtXr1ah05ckRr1qxR2bJl7S7g8uXL6tWrl9555x2VK1fOuvzixYv697//rVmzZqlNmzZq1qyZFi9erO3bt2vHjh2SpA0bNuinn37Shx9+qCZNmujRRx/VxIkTNW/ePKWmpkqSFi5cqICAAM2cOVOBgYEaOnSonnjiCb311lvWY82aNUv9+/fX888/r/r162vhwoXy8vLSe++9Z/f7AQAAAID8yvdDPTIyMvTmm2/qs88+U2pqqh555BGNGzdOnp6et1TAkCFD1KFDB4WFhemNN96wLt+zZ4/S0tIUFhZmXXbPPfeoevXqiouL0wMPPKC4uDg1atRIvr6+1jHh4eEaNGiQDh48qKZNmyouLs5mH5ljMi+NTE1N1Z49ezR69GjreicnJ4WFhSkuLi7Huq9evaqrV69aXyclJUmS0tLSlJaWlu02mctzWo9bR49vgZH39wim/XdMWj7GXh/I34O9OIcdjx47Hj12PHrsePTY8Ypzj+15T/kOZJMmTVJUVJTCwsLk6emp2bNnKzEx8ZZmkVasWKEffvhB33//fZZ1CQkJcnNzk4+Pj81yX19fJSQkWMfcGMYy12euy21MUlKSrly5ovPnzys9PT3bMYcPH86x9ilTpmj8+PFZlm/YsEFeXl45bidJsbGxua7HraPHBdEq3yNj9aBk5GPgF18UvJwSjnPY8eix49Fjx6PHjkePHa849jg5OTnfY/MdyJYuXar58+frn//8pyRp48aN6tChg9599105Odl95aN+/fVXDRs2TLGxsfLw8LB7e7ONHj1akZGR1tdJSUmqVq2a2rVrJ29v72y3SUtLU2xsrNq2bStXV9fbVWqJQo9vwZev5jkkzXBWrB5UW30nV0t63vt8dFohFFaycA47Hj12PHrsePTY8eix4xXnHmdePZcf+Q5kp06d0mOPPWZ9HRYWJovFotOnT6tq1ar2VajrlyQmJibqvvvusy5LT0/XN998o7lz5+qrr75SamqqLly4YDNLdvbsWfn5+UmS/Pz8sjwNMfMpjDeOufnJjGfPnpW3t7c8PT3l7OwsZ2fnbMdk7iM77u7ucnd3z7Lc1dU1zxMqP2Nwa+hxAeQnYEmSIbla0vMXyPg7KDDOYcejx45Hjx2PHjsePXa84thje95Pvqe2rl27lmUmy9XVtcDXfD7yyCPav3+/9u3bZ/0JCgpSr169rH92dXXVpk2brNscOXJEp06dUkhIiCQpJCRE+/fvt3kaYmxsrLy9vVW/fn3rmBv3kTkmcx9ubm5q1qyZzZiMjAxt2rTJOgYAAAAAHCHfM2SGYahPnz42s0IpKSkaOHCgSpUqZV32ySef5Gt/ZcqUUcOGDW2WlSpVShUqVLAuj4iIUGRkpMqXLy9vb2+98MILCgkJ0QMPPCBJateunerXr69nn31W06dPV0JCgsaMGaMhQ4ZY6xw4cKDmzp2rV155RX379tXXX3+tlStXKiYmxnrcyMhI9e7dW0FBQWrevLmio6P1999/6/nnn89vewAAAADAbvkOZL17986y7JlnninUYm721ltvycnJSd26ddPVq1cVHh6u+fPnW9c7Ozvr888/16BBgxQSEqJSpUqpd+/emjBhgnVMQECAYmJiNGLECM2ePVtVq1bVu+++q/DwcOuYHj166I8//tDYsWOVkJCgJk2aaP369Vke9AEAAAAAhSnfgWzx4sWOrEOStGXLFpvXHh4emjdvnubNm5fjNjVq1NAXeTzJrXXr1tq7d2+uY4YOHaqhQ4fmu1YAAAAAuFX2Px4RAAAAAFAoCGQAAAAAYBICGQAAAACYhEAGAAAAACYhkAEAAACASQhkAAAAAGASAhkAAAAAmIRABgAAAAAmIZABAAAAgEkIZAAAAABgEgIZAAAAAJiEQAYAAAAAJiGQAQAAAIBJCGQAAAAAYBICGQAAAACYhEAGAAAAACYhkAEAAACASQhkAAAAAGASAhkAAAAAmIRABgAAAAAmIZABAAAAgEkIZAAAAABgEgIZAAAAAJiEQAYAAAAAJiGQAQAAAIBJCGQAAAAAYBICGQAAAACYhEAGAAAAACYhkAEAAACASQhkAAAAAGASAhkAAAAAmIRABgAAAAAmIZABAAAAgEkIZAAAAABgEgIZAAAAAJjExewCANxG64aZXQEAAABuwAwZAAAAAJiEQAYAAAAAJiGQAQAAAIBJCGQAAAAAYBICGQAAAACYhEAGAAAAACYhkAEAAACASQhkAAAAAGASAhkAAAAAmIRABgAAAAAmIZABAAAAgEkIZAAAAABgEgIZAAAAAJiEQAYAAAAAJiGQAQAAAIBJCGQAAAAAYBICGQAAAACYhEAGAAAAACYhkAEAAACASQhkAAAAAGASAhkAAAAAmIRABgAAAAAmIZABAAAAgEkIZAAAAABgEgIZAAAAAJiEQAYAAAAAJiGQAQAAAIBJCGQAAAAAYBICGQAAAACYhEAGAAAAACYhkAEAAACASQhkAAAAAGASUwPZlClTdP/996tMmTKqXLmyunTpoiNHjtiMSUlJ0ZAhQ1ShQgWVLl1a3bp109mzZ23GnDp1Sh06dJCXl5cqV66sl19+WdeuXbMZs2XLFt13331yd3dXnTp1tGTJkiz1zJs3TzVr1pSHh4eCg4O1a9euQn/PAAAAAJDJxcyDb926VUOGDNH999+va9eu6bXXXlO7du30008/qVSpUpKkESNGKCYmRqtWrVLZsmU1dOhQPf7449q2bZskKT09XR06dJCfn5+2b9+uM2fO6LnnnpOrq6smT54sSYqPj1eHDh00cOBALVu2TJs2bVK/fv3k7++v8PBwSdJHH32kyMhILVy4UMHBwYqOjlZ4eLiOHDmiypUrm9MgoCDWDTO7AgAAAOSTqYFs/fr1Nq+XLFmiypUra8+ePWrZsqUuXryof//731q+fLnatGkjSVq8eLECAwO1Y8cOPfDAA9qwYYN++uknbdy4Ub6+vmrSpIkmTpyoV199VVFRUXJzc9PChQsVEBCgmTNnSpICAwP13Xff6a233rIGslmzZql///56/vnnJUkLFy5UTEyM3nvvPY0aNeo2dgUAAABASWFqILvZxYsXJUnly5eXJO3Zs0dpaWkKCwuzjrnnnntUvXp1xcXF6YEHHlBcXJwaNWokX19f65jw8HANGjRIBw8eVNOmTRUXF2ezj8wxw4cPlySlpqZqz549Gj16tHW9k5OTwsLCFBcXl22tV69e1dWrV62vk5KSJElpaWlKS0vLdpvM5Tmtx62jx5IMZ4ftOu2/+07L7zFK8t9DAXEOOx49djx67Hj02PHoseMV5x7b857umECWkZGh4cOHKzQ0VA0bNpQkJSQkyM3NTT4+PjZjfX19lZCQYB1zYxjLXJ+5LrcxSUlJunLlis6fP6/09PRsxxw+fDjbeqdMmaLx48dnWb5hwwZ5eXnl+l5jY2NzXY9bV7J73MrhR4jVg5KRj4FffOHwWoqrkn0O3x702PHosePRY8ejx45XHHucnJyc77F3TCAbMmSIDhw4oO+++87sUvJl9OjRioyMtL5OSkpStWrV1K5dO3l7e2e7TVpammJjY9W2bVu5urrerlJLFHos6ctXHbbrNMNZsXpQbfWdXC3peW/w6DSH1VJccQ47Hj12PHrsePTY8eix4xXnHmdePZcfd0QgGzp0qD7//HN98803qlq1qnW5n5+fUlNTdeHCBZtZsrNnz8rPz8865uanIWY+hfHGMTc/mfHs2bPy9vaWp6ennJ2d5ezsnO2YzH3czN3dXe7u7lmWu7q65nlC5WcMbk2J7nF+gtKtMCRXS3r+AllJ/TsoBCX6HL5N6LHj0WPHo8eOR48drzj22J73Y2ogMwxDL7zwgtasWaMtW7YoICDAZn2zZs3k6uqqTZs2qVu3bpKkI0eO6NSpUwoJCZEkhYSEaNKkSUpMTLQ+DTE2Nlbe3t6qX7++dcwXN106FRsba92Hm5ubmjVrpk2bNqlLly6Srl9CuWnTJg0dOtRh7x9ACZbT0zANZ92Oy04BAMCdwdRANmTIEC1fvlyffvqpypQpY73nq2zZsvL09FTZsmUVERGhyMhIlS9fXt7e3nrhhRcUEhKiBx54QJLUrl071a9fX88++6ymT5+uhIQEjRkzRkOGDLHOYA0cOFBz587VK6+8or59++rrr7/WypUrFRMTY60lMjJSvXv3VlBQkJo3b67o6Gj9/fff1qcuAgDskNvXL3ScffvqAADgDmdqIFuwYIEkqXXr1jbLFy9erD59+kiS3nrrLTk5Oalbt266evWqwsPDNX/+fOtYZ2dnff755xo0aJBCQkJUqlQp9e7dWxMmTLCOCQgIUExMjEaMGKHZs2eratWqevfdd62PvJekHj166I8//tDYsWOVkJCgJk2aaP369Vke9AEAAAAAhcX0Sxbz4uHhoXnz5mnevHk5jqlRo0aWSxJv1rp1a+3duzfXMUOHDuUSRQC4ETNdAAA41B3xUA8AQBGUW1gDAAD54mR2AQAAAABQUhHIAAAAAMAkXLIIAHeiL1/N/jvluG8LAIBihUAGACUd94IBAGAaLlkEAAAAAJMQyAAAAADAJAQyAAAAADAJ95ABgKNwbxYAAMgDgQyA4+QWSHha4O1FOAQA4I7EJYsAAAAAYBJmyICiiNkOAACAYoEZMgAAAAAwCYEMAAAAAExCIAMAAAAAkxDIAAAAAMAkBDIAAAAAMAlPWQSAooTvdgMAoFhhhgwAAAAATEIgAwAAAACTEMgAAAAAwCQEMgAAAAAwCYEMKGJGf7JfO+PPZVm+M/5ctssBAABw5yKQAQAAAIBJCGQAAAAAYBICGQAudwQAADAJXwwN3Kly+ALgLr/ZBieCFKxy+9JoAABwRyKQAQBur9yCY8fZt68OAADuAAQyoIi6lZmxzG2DA8oXVjkAAAAoAAIZgCyyC3uENwAAgMJHIANKMO4/AwAAMBdPWQQAAAAAkzBDBhQBt2smixkzAACA24sZMgAAAAAwCTNkQDF346zXrTyYY2f8OWVYXKTqtvvlYR8AAAAFRyADANw5+I4yAEAJQyADipnbMXO1++T5W7/eObd/eEv84xsAAJQIBDKgMDjov+rzkA0AAIDijUAGOFpeM0HifiwAAICSikAGmOjmGbDCDGa3+1H5hEkAAAD7EcgAE5h1KSKXQAIAANxZCGTAjXjCG+yVj0tSAQAAckIgA+5AzGQBAACUDAQyoJDcHKJuvqeKkGUnZisBAEAJcMtfJQQgezvjzxHCAAAAkCtmyFDyFPSeH+4VAgAAQCEjkAG3iFkwE3A5IwAAKCYIZICDEdhuM8IaAAAoQghkKJ6+fFWypJtdBQAAAJArAhnubMx2AAAAoBjjKYsAAAAAYBJmyIAC4t6wIogZVwAAcIchkKHoyu4f14azpFa3vRQUc3zlAQAAcBACGQBIhC4AAGAKAhnMxz+EAQAAUELxUA8AAAAAMAmBDAAAAABMwiWLgB14siIAAAAKE4EMAFA08LUFAIBiiEsWgVzsjD/HrBgAAAAchhkyIB8IZcAdjtkzAEARxQwZgELBbCIAAID9mCEDskGwAIqRm2fPDGdJraQvX5Us6cygAQBMRSADgCIkt/9YEBxQPtdtclpf4nG5IwDARAQyAIWKf/ybJ6+ZXf5uAAC48xDIAOAOxuWzJmP2DADgYAQy4Ab84xe3U24zVrtPnpeTce22HxcAANxeBDLcHrn9V2aghLk5+N/4OsPiIlW//XVkhjPCmh0K+rnGzBoA4AY89v4m8+bNU82aNeXh4aHg4GDt2rXL7JKAIinzMfjMOv7PndyPO7k2AACKM2bIbvDRRx8pMjJSCxcuVHBwsKKjoxUeHq4jR46ocuXKZpcHB+Ifoo5VUmddivJ5dXPtJe3vzqEKel8aM3IAUCwRyG4wa9Ys9e/fX88//7wkaeHChYqJidF7772nUaNGmVwdHKEo/4O5KHJkvx0VGAoSJovjeVVSQ/Vtx+XdAFDiEMj+KzU1VXv27NHo0aOty5ycnBQWFqa4uLgs469evaqrV69aX1+8eFGSdO7cOaWlpWV7jLS0NCUnJ+uvv/6Sq6trIb8DO8SONe/YDpZmGEpWsv7SNbla0vMcfykl4zZUVXxkWDKUnJysSykZcjLurN5tPPRnkd5/pju5x1L2fWha3ef2F3IL7P2cKPJWDLnth0wznJWsEP21+qWi2eO2E8yuIE93zL8pijF67HjFuceXLl2SJBmGkedYAtl//fnnn0pPT5evr6/Ncl9fXx0+fDjL+ClTpmj8+PFZlgcEBDisRuTXHLMLKOaWml1ACUCPHY/PCccryj2eb3YBAIqJS5cuqWzZsrmOIZAV0OjRoxUZGWl9nZGRoXPnzqlChQqyWCzZbpOUlKRq1arp119/lbe39+0qtUShx45Ffx2PHjsePXY8eux49Njx6LHjFeceG4ahS5cuqUqVKnmOJZD9V8WKFeXs7KyzZ8/aLD979qz8/PyyjHd3d5e7u7vNMh8fn3wdy9vbu9iddHcaeuxY9Nfx6LHj0WPHo8eOR48djx47XnHtcV4zY5l47P1/ubm5qVmzZtq0aZN1WUZGhjZt2qSQkBATKwMAAABQXDFDdoPIyEj17t1bQUFBat68uaKjo/X3339bn7oIAAAAAIWJQHaDHj166I8//tDYsWOVkJCgJk2aaP369Vke9FFQ7u7uGjduXJZLHVF46LFj0V/Ho8eOR48djx47Hj12PHrsePT4OouRn2cxAgAAAAAKHfeQAQAAAIBJCGQAAAAAYBICGQAAAACYhEAGAAAAACYhkBWCqVOnymKxaPjw4ZKkEydOyGKxZPuzatWqHPfTp0+fLOPbt29/m97FnSUqKipLL+655x7r+pSUFA0ZMkQVKlRQ6dKl1a1btyxf6n0zwzA0duxY+fv7y9PTU2FhYTp69Kij38odK7cenzt3Ti+88ILq1asnT09PVa9eXS+++KIuXryY6z45h23ldR63bt06y/qBAwfmuk/OY1u59ZjP4sLz+++/65lnnlGFChXk6empRo0aaffu3db1BT0v582bp5o1a8rDw0PBwcHatWuXI9/GHS23HqelpenVV19Vo0aNVKpUKVWpUkXPPfecTp8+nes+8/oMKmnyOo8L+rvPeXxdXv3N6fP4zTffzHGfJeUc5rH3t+j777/Xv/71L917773WZdWqVdOZM2dsxi1atEhvvvmmHn300Vz31759ey1evNj6uiQ/BrRBgwbauHGj9bWLy/9O1xEjRigmJkarVq1S2bJlNXToUD3++OPatm1bjvubPn263n77bb3//vsKCAjQ66+/rvDwcP3000/y8PBw6Hu5U+XU49OnT+v06dOaMWOG6tevr5MnT2rgwIE6ffq0Vq9enes+OYdt5XYeS1L//v01YcIE62svL69c98d5nFVOPeazuHCcP39eoaGhevjhh/Xll1+qUqVKOnr0qMqVK2cdU5Dz8qOPPlJkZKQWLlyo4OBgRUdHKzw8XEeOHFHlypVv19u7I+TV4+TkZP3www96/fXX1bhxY50/f17Dhg1Tp06dbP7Bm528PoNKivycx5L9v/ucx9flp783fx5/+eWXioiIULdu3XLdd4k4hw0U2KVLl4y7777biI2NNVq1amUMGzYsx7FNmjQx+vbtm+v+evfubXTu3Llwiyyixo0bZzRu3DjbdRcuXDBcXV2NVatWWZcdOnTIkGTExcVlu01GRobh5+dnvPnmmzb7cXd3N/7v//6vUGsvKnLrcXZWrlxpuLm5GWlpaTmO4Ry2lVeP8/rcuBnncVb2nsd8Ftvv1VdfNR588MEc1xf0vGzevLkxZMgQ6+v09HSjSpUqxpQpUwqn8CIkrx5nZ9euXYYk4+TJkzmOsff3ozjLT48L8rvPeXxdQc7hzp07G23atMl1TEk5h7lk8RYMGTJEHTp0UFhYWK7j9uzZo3379ikiIiLPfW7ZskWVK1dWvXr1NGjQIP3111+FVW6Rc/ToUVWpUkW1atVSr169dOrUKUnX+5mWlmbT93vuuUfVq1dXXFxctvuKj49XQkKCzTZly5ZVcHBwjtuUBDn1ODsXL16Ut7d3nv9linPYVl49XrZsmSpWrKiGDRtq9OjRSk5OznFfnMfZy+95zGdxwXz22WcKCgrSk08+qcqVK6tp06Z65513rOsLcl6mpqZqz549Nts4OTkpLCysRJ7LefU4OxcvXpTFYpGPj0+u4+z5nC/O8ttje373OY//x95z+OzZs4qJicnX53FJOIcJZAW0YsUK/fDDD5oyZUqeY//9738rMDBQLVq0yHVc+/bttXTpUm3atEnTpk3T1q1b9eijjyo9Pb2wyi4ygoODtWTJEq1fv14LFixQfHy8HnroIV26dEkJCQlyc3PL8n9Cvr6+SkhIyHZ/mct9fX3zvU1xl1uPb/bnn39q4sSJGjBgQK775By2lVePn376aX344YfavHmzRo8erQ8++EDPPPNMjvvjPM7KnvOYz+KC+eWXX7RgwQLdfffd+uqrrzRo0CC9+OKLev/99yUV7Lz8888/lZ6ezrn8X3n1+GYpKSl69dVX9dRTT8nb2zvH/drz+1Hc5afH9v7ucx7/j73n8Pvvv68yZcro8ccfz3W/JeYcNnuKrig6deqUUblyZePHH3+0Lsvp0qPk5GSjbNmyxowZM+w+zvHjxw1JxsaNG2+l3GLh/Pnzhre3t/Huu+8ay5YtM9zc3LKMuf/++41XXnkl2+23bdtmSDJOnz5ts/zJJ580unfv7pCai5obe3yjixcvGs2bNzfat29vpKam2rVPzmFbOfU406ZNmwxJxrFjx7Jdz3mct5x6zGdxwbm6uhohISE2y1544QXjgQceMAyjYOfl77//bkgytm/fbrP85ZdfNpo3b16I1RcNefX4RqmpqUbHjh2Npk2bGhcvXrTrOHl9BhVn9vQ4U16/+5zH/2Nvf+vVq2cMHTrU7uMU13OYGbIC2LNnjxITE3XffffJxcVFLi4u2rp1q95++225uLjY/JeU1atXKzk5Wc8995zdx6lVq5YqVqyoY8eOFWb5RZKPj4/q1q2rY8eOyc/PT6mpqbpw4YLNmLNnz8rPzy/b7TOX3/wkxty2KWlu7HGmS5cuqX379ipTpozWrFkjV1dXu/bJOWwrux7fKDg4WJJyXM95nLecesxnccH5+/urfv36NssCAwOtlw0V5LysWLGinJ2dOZf/K68eZ0pLS1P37t118uRJxcbG5jo7lp28PoOKs/z2+EZ5/e5zHv+PPf399ttvdeTIEfXr18/u4xTXc5hAVgCPPPKI9u/fr3379ll/goKC1KtXL+3bt0/Ozs7Wsf/+97/VqVMnVapUye7j/Pbbb/rrr7/k7+9fmOUXSZcvX9bx48fl7++vZs2aydXVVZs2bbKuP3LkiE6dOqWQkJBstw8ICJCfn5/NNklJSdq5c2eO25Q0N/ZYut6fdu3ayc3NTZ999lmBnuDHOWzr5h7fbN++fZKU43rO47zl1GM+iwsuNDRUR44csVn2888/q0aNGpIKdl66ubmpWbNmNttkZGRo06ZNJfJczqvH0v/C2NGjR7Vx40ZVqFDB7uPk9RlUnOWnxzfL63ef8/h/7Onvv//9bzVr1kyNGze2+zjF9hw2e4quuMjuksWjR48aFovF+PLLL7Pdpl69esYnn3xiGMb1JzaOHDnSiIuLM+Lj442NGzca9913n3H33XcbKSkpji7/jvPSSy8ZW7ZsMeLj441t27YZYWFhRsWKFY3ExETDMAxj4MCBRvXq1Y2vv/7a2L17txESEpJlqvzG/hqGYUydOtXw8fExPv30U+M///mP0blzZyMgIMC4cuXKbX1vd4rcenzx4kUjODjYaNSokXHs2DHjzJkz1p9r165Z98E5nLvcenzs2DFjwoQJxu7du434+Hjj008/NWrVqmW0bNnSZh+cx7nL67PCMPgsvlW7du0yXFxcjEmTJhlHjx41li1bZnh5eRkffvihdUx+zss2bdoYc+bMsb5esWKF4e7ubixZssT46aefjAEDBhg+Pj5GQkLCbX1/d4K8epyammp06tTJqFq1qrFv3z6bz+SrV69a93Nzj/Pz+1FS5NXj/P7ucx5nLz+fE4Zx/TYILy8vY8GCBdnup6SewwSyQpJdIBs9erRRrVo1Iz09PdttJBmLFy82DOP6/Q3t2rUzKlWqZLi6uho1atQw+vfvX+J+oTP16NHD8Pf3N9zc3Iy77rrL6NGjh819NVeuXDEGDx5slCtXzvDy8jK6du1qnDlzxmYfN/bXMK4/mvn11183fH19DXd3d+ORRx4xjhw5crve0h0ntx5v3rzZkJTtT3x8vHUfnMO5y63Hp06dMlq2bGmUL1/ecHd3N+rUqWO8/PLLWe4J4TzOXV6fFYbBZ3FhWLdundGwYUPD3d3duOeee4xFixbZrM/PeVmjRg1j3LhxNsvmzJljVK9e3XBzczOaN29u7Nixw9Fv5Y6VW4/j4+Nz/EzevHmzddzNPc7P70dJkluP8/u7z3mcs7w+JwzDMP71r38Znp6exoULF7LdR0k9hy2GYRi3fVoOAAAAAMA9ZAAAAABgFgIZAAAAAJiEQAYAAAAAJiGQAQAAAIBJCGQAAAAAYBICGQAAAACYhEAGAAAAACYhkAEAAACASQhkAIBi7ciRI/Lz89OlS5fyvU2fPn3UpUsXxxVlh5o1ayo6OrpQ99mzZ0/NnDmzUPcJACgYAhkA4I62ZcsWWSyWHH8efvjhXLcfPXq0XnjhBZUpUybP/SUkJNyOt5StJUuWyMfHJ8vy77//XgMGDCjUY40ZM0aTJk3SxYsXC3W/AAD7EcgAAHe0Fi1a6MyZM1l+/vWvf8lisWjw4ME5bnvq1Cl9/vnn6tOnT5Z1R44cybLPypUrO/CdFEylSpXk5eVVqPts2LChateurQ8//LBQ9wsAsB+BDABwR3Nzc5Ofn5/Nz/nz5zVy5Ei99tprevLJJ3PcduXKlWrcuLHuuuuuLOsqV66cZb9OTtn/32JGRoamTJmigIAAeXp6qnHjxlq9erV1XdWqVbVgwQKbbfbu3SsnJyedPHlSkjRr1iw1atRIpUqVUrVq1TR48GBdvnxZ0vVZu+eff14XL160ztZFRUVJynrJ4qlTp9S5c2eVLl1a3t7e6t69u86ePWtdHxUVpSZNmuiDDz5QzZo1VbZsWfXs2TPLJZsdO3bUihUrcuwdAOD2IJABAIqUCxcuqHPnzmrdurUmTpyY69hvv/1WQUFBt3zMKVOmaOnSpVq4cKEOHjyoESNG6JlnntHWrVvl5OSkp556SsuXL7fZZtmyZQoNDVWNGjUkSU5OTnr77bd18OBBvf/++/r666/1yiuvSLo+CxgdHS1vb2/rbN3IkSOz1JGRkaHOnTvr3Llz2rp1q2JjY/XLL7+oR48eNuOOHz+utWvX6vPPP9fnn3+urVu3aurUqTZjmjdvrl27dunq1au33B8AQMG5mF0AAAD5lZGRoaefflouLi5atmyZLBZLruNPnjyZYyCrWrWqzesaNWro4MGDWcZdvXpVkydP1saNGxUSEiJJqlWrlr777jv961//UqtWrdSrVy/NnDlTp06dUvXq1ZWRkaEVK1ZozJgx1v0MHz7c+ueaNWvqjTfe0MCBAzV//ny5ubmpbNmyslgs8vPzy/H9bNq0Sfv371d8fLyqVasmSVq6dKkaNGig77//Xvfff7+1T0uWLLHeN/fss89q06ZNmjRpknVfVapUUWpqqhISEqyhEQBw+xHIAABFxmuvvaa4uDjt2rXLGjZyc+XKFXl4eGS77ttvv7XZh6ura7bjjh07puTkZLVt29ZmeWpqqpo2bSpJatKkiQIDA7V8+XKNGjVKW7duVWJios3llBs3btSUKVN0+PBhJSUl6dq1a0pJSVFycnK+7xE7dOiQqlWrZg1jklS/fn35+Pjo0KFD1kBWs2ZNm/fm7++vxMREm315enpKkpKTk/N1bACAYxDIAABFwooVKzRjxgzFxMTo7rvvztc2FStW1Pnz57NdFxAQkO1TDW+WeZ9XTExMlnvR3N3drX/u1auXNZAtX75c7du3V4UKFSRJJ06c0D/+8Q8NGjRIkyZNUvny5fXdd98pIiJCqamphf7QjpvDpcViUUZGhs2yc+fOSbr+0BAAgHkIZACAO96+ffsUERGhqVOnKjw8PN/bNW3aVD/99NMtHbt+/fpyd3fXqVOn1KpVqxzHPf300xozZoz27Nmj1atXa+HChdZ1e/bsUUZGhmbOnGl9cMjKlStttndzc1N6enqutQQGBurXX3/Vr7/+ap0l++mnn3ThwgXVr1/frvd14MABVa1aVRUrVrRrOwBA4SKQAQDuaH/++ae6dOmi1q1b65lnnsnyXWHOzs45zvKEh4erX79+Sk9Pl7Ozs826xMREpaSk2CyrUKFCltmlMmXKaOTIkRoxYoQyMjL04IMP6uLFi9q2bZu8vb3Vu3dvSdcvE2zRooUiIiKUnp6uTp06WfdRp04dpaWlac6cOerYsaO2bdtmE9gyt798+bI2bdqkxo0by8vLK8vMWVhYmBo1aqRevXopOjpa165d0+DBg9WqVSu7H17y7bffql27dnZtAwAofDxlEQBwR4uJidHJkyf1xRdfyN/fP8tP5n1T2Xn00Ufl4uKijRs3ZllXr169LPvas2dPtvuZOHGiXn/9dU2ZMkWBgYFq3769YmJiFBAQYDOuV69e+vHHH9W1a1frPVqS1LhxY82aNUvTpk1Tw4YNtWzZMk2ZMsVm2xYtWmjgwIHq0aOHKlWqpOnTp2epw2Kx6NNPP1W5cuXUsmVLhYWFqVatWvroo49y7eHNUlJStHbtWvXv39+u7QAAhc9iGIZhdhEAADjKvHnz9Nlnn+mrr74yu5Q7xoIFC7RmzRpt2LDB7FIAoMTjkkUAQLH2z3/+UxcuXNClS5fy9WTGksDV1VVz5swxuwwAgJghAwAAAADTcA8ZAAAAAJiEQAYAAAAAJiGQAQAAAIBJCGQAAAAAYBICGQAAAACYhEAGAAAAACYhkAEAAACASQhkAAAAAGASAhkAAAAAmOT/Azvw6Yxo+O+sAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Height (Z) Distribution Plot\n", "plt.figure(figsize=(10, 5))\n", "plt.hist(ground_points[:, 2], bins=100, alpha=0.6, label='Ground Z')\n", "plt.hist(nonground_points[:, 2], bins=100, alpha=0.6, label='Non-Ground Z')\n", "plt.legend()\n", "plt.title(\"Z-Height Distribution\")\n", "plt.xlabel(\"Z (Elevation)\")\n", "plt.ylabel(\"Point Count\")\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 19:46:49,312 - INFO - RANSAC Ground Segmentation - Ready!\n", "2025-06-27 19:46:49,313 - INFO - ==================================================\n", "2025-06-27 19:46:49,314 - INFO - Data path: ../../data\n", "2025-06-27 19:46:49,315 - INFO - Project: ENEL/Castro\n", "2025-06-27 19:46:49,315 - INFO - Input path: ../../../data/processed/motali_de_castro/denoising/motali_de_castro_denoised.ply\n", "2025-06-27 19:46:49,316 - INFO - Output path: ../../data/ENEL/Castro/ground_segmentation\n", "2025-06-27 19:46:49,316 - INFO - Current run output: output_runs/Castro_ransac_20250627_193137\n", "2025-06-27 19:46:49,316 - INFO - RANSAC Parameters: threshold=0.2m, iterations=1000\n"]}], "source": ["# Final readiness print\n", "logger.info(\"RANSAC Ground Segmentation - Ready!\")\n", "logger.info(\"=\" * 50)\n", "logger.info(f\"Data path: {data_path}\")\n", "logger.info(f\"Project: {project_type}/{site_name}\")\n", "logger.info(f\"Input path: {input_path}\")\n", "logger.info(f\"Output path: {ground_seg_path}\")\n", "logger.info(f\"Current run output: {current_run_path}\")\n", "logger.info(f\"RANSAC Parameters: threshold={distance_threshold}m, iterations={num_iterations}\")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# Visualize the results\n", "import open3d as o3d\n", "import numpy as np\n", "\n", "# Create point cloud for ground\n", "pcd_ground = o3d.geometry.PointCloud()\n", "pcd_ground.points = o3d.utility.Vector3dVector(ground_points)\n", "pcd_ground.paint_uniform_color([0.0, 1.0, 0.0])  # Green\n", "\n", "# Create point cloud for non-ground\n", "pcd_nonground = o3d.geometry.PointCloud()\n", "pcd_nonground.points = o3d.utility.Vector3dVector(nonground_points)\n", "pcd_nonground.paint_uniform_color([1.0, 0.0, 0.0])  # Red\n", "\n", "# Show both together\n", "o3d.visualization.draw_geometries([pcd_ground, pcd_nonground],\n", "                                  window_name=\"Ground vs Non-Ground\",\n", "                                  point_show_normal=False)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}