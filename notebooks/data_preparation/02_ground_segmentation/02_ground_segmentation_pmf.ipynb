{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Segmentation - Progressive Morphological Filter (PMF)\n", "\n", "This notebook implements PMF-based ground segmentation for point cloud processing.\n", "\n", "**Method**: Progressive Morphological Filter  \n", "**Input Data**: Raw point cloud (.las, .laz, .pcd)  \n", "**Output**: Ground-removed / non-ground point cloud  \n", "**Format**: .ply (recommended for compatibility with Open3D + visualization), .pcd (preferred for PCL + ML pipelines)  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025\n", "**Project**: As Built Analytics for Solar Array Inspection\n", "\n", "## PMF Algorithm:\n", "- Based on <PERSON> et al. (2003) algorithm\n", "- Uses morphological operations on rasterized point cloud\n", "- Progressive window size increase\n", "- Best for complex terrain with vegetation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Imports"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import numpy as np\n", "import os\n", "import json\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import time\n", "import logging\n", "from pathlib import Path\n", "from datetime import datetime\n", "from scipy import ndimage"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Parameters"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Progressive Morphological Filter (PMF) – Parameter Documentation\n", "\n", "The **Progressive Morphological Filter (PMF)** is a commonly used ground segmentation algorithm, particularly in LiDAR and photogrammetric point clouds. It works by progressively increasing the size of a structuring element to remove non-ground points based on local elevation changes and terrain slope.\n", "\n", "Below are the tunable parameters used in our implementation:\n", "\n", "---\n", "## Source and References\n", "\n", "These parameters and the algorithm are based on research and implementations including:\n", "\n", "- **<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, & <PERSON>, <PERSON> (2003)**  \n", "  *A progressive morphological filter for removing nonground measurements from airborne LIDAR data.*  \n", "  [DOI:10.1109/TGRS.2003.810682](https://doi.org/10.1109/TGRS.2003.810682)\n", "\n", "- **PDAL PMF Filter Docs**  \n", "  https://pdal.io/stages/filters.pmf.html\n", "\n", "- **LAStools PMF Implementation**  \n", "  [https://rapidlasso.com/2013/11/07/morphological-ground-classification](https://rapidlasso.com/2013/11/07/morphological-ground-classification)\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters - these will be injected by Papermill\n", "site_name = \"Castro\"  # Site name for output file naming\n", "buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)\n", "point_cloud_path = \"../../../data/processed/motali_de_castro/denoising/motali_de_castro_denoised.ply\"  # Path to input point cloud file\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "\n", "# === PMF (Progressive Morphological Filter) Parameters ===\n", "# These parameters control how the morphological filter is applied to identify ground points \n", "# by simulating terrain smoothing across increasing window sizes.\n", "\n", "cell_size = 1.0  \n", "# Size of each grid cell when rasterizing the point cloud (in meters).\n", "# Smaller values retain finer surface detail but increase computation.\n", "# Recommended: 0.5 – 2.0 based on point density and terrain complexity.\n", "\n", "max_window_size = 33  \n", "# Maximum size (in raster units) of the morphological structuring element.\n", "# Determines the scale of features that can be removed (e.g., buildings, vegetation).\n", "# Larger values capture broader terrain variation but may oversmooth.\n", "\n", "slope = 0.15  \n", "# Maximum local slope (in radians) allowed during filtering.\n", "# Points exceeding this elevation change across a window are treated as non-ground.\n", "# Typical values: 0.1 – 0.3 for natural terrain.\n", "\n", "max_distance = 2.5  \n", "# Maximum elevation difference (in meters) between a point and the estimated ground surface \n", "# to still be classified as ground.\n", "# Helps in removing high outliers like trees and rooftops.\n", "\n", "initial_distance = 0.5  \n", "# Initial threshold (in meters) for elevation difference during early filtering iterations.\n", "# A tighter threshold avoids early misclassifications and stabilizes the progressive process.\n", "\n", "height_threshold_ratio = 0.1  \n", "# Proportion of the lowest height range used to seed initial ground estimation (0–1).\n", "# Typically set between 0.05 and 0.15 to capture the base terrain while ignoring outliers."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 18:44:09,687 - INFO - Checking base data path: ../../data, Exists: True\n", "2025-06-27 18:44:09,688 - INFO - Created current run output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks/data_preparation/02_ground_segmentation/output_runs/Castro_pmf_20250627_184409\n", "2025-06-27 18:44:09,689 - INFO - Custom point_cloud_path provided: ../../../data/processed/motali_de_castro/denoising/motali_de_castro_denoised.ply\n", "2025-06-27 18:44:09,689 - INFO - Input path exists: True\n", "2025-06-27 18:44:09,690 - INFO - Ground segmentation output path exists: True\n"]}], "source": ["# Set up paths with proper project organization\n", "base_path = Path('../..')\n", "data_path = base_path / 'data'\n", "logger.info(f\"Checking base data path: {data_path}, Exists: {data_path.exists()}\")\n", "\n", "# Create output directory structure for this run\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "output_runs_path = Path('output_runs')\n", "current_run_path = output_runs_path / f'{site_name}_pmf_{timestamp}'\n", "current_run_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# DEBUG: Confirm output run path creation\n", "logger.info(f\"Created current run output path: {current_run_path.resolve()}\")\n", "\n", "# Input and output paths following the specified organization\n", "if point_cloud_path:\n", "    input_path = Path(point_cloud_path)\n", "    logger.info(f\"Custom point_cloud_path provided: {input_path}\")\n", "    if not input_path.exists():\n", "        raise FileNotFoundError(f\"Input path does not exist: {input_path}\")\n", "else:\n", "    raw_path = data_path / project_type / site_name / 'raw'\n", "    input_path = raw_path\n", "    logger.info(f\"Using default input path: {input_path}\")\n", "\n", "ground_seg_path = data_path / project_type / site_name / 'ground_segmentation'\n", "ground_seg_path.mkdir(parents=True, exist_ok=True)\n", "\n", "# DEBUG: Confirm paths exist\n", "logger.info(f\"Input path exists: {input_path.exists()}\")\n", "logger.info(f\"Ground segmentation output path exists: {ground_seg_path.exists()}\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 19:34:38,952 - INFO - Reading PLY file: ../../../data/processed/motali_de_castro/denoising/motali_de_castro_denoised.ply\n", "2025-06-27 19:34:39,116 - INFO - Point cloud statistics:\n", "2025-06-27 19:34:39,117 - INFO - --------------------------------------------------\n", "2025-06-27 19:34:39,117 - INFO -   Loaded 4645041 points\n", "2025-06-27 19:34:39,127 - INFO -   X range: 707251.84 to 707837.91 (586.06m)\n", "2025-06-27 19:34:39,135 - INFO -   Y range: 4692831.66 to 4693173.16 (341.50m)\n", "2025-06-27 19:34:39,145 - INFO -   Z range: 48.25 to 68.12 (19.87m)\n"]}], "source": ["# Load Point Cloud data\n", "import open3d as o3d\n", "\n", "# Load Point Cloud (.ply)\n", "input_path = Path(point_cloud_path)\n", "logger.info(f\"Reading PLY file: {input_path}\")\n", "\n", "pcd = o3d.io.read_point_cloud(str(input_path))\n", "\n", "if not pcd.has_points():\n", "    raise ValueError(\"Loaded PLY file contains no points.\")\n", "\n", "points = np.asarray(pcd.points)\n", "\n", "# Display basic statistics\n", "logger.info(f\"Point cloud statistics:\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"  Loaded {points.shape[0]} points\")\n", "\n", "x, y, z = points[:, 0], points[:, 1], points[:, 2]\n", "\n", "logger.info(f\"  X range: {x.min():.2f} to {x.max():.2f} ({x.max()-x.min():.2f}m)\")\n", "logger.info(f\"  Y range: {y.min():.2f} to {y.max():.2f} ({y.max()-y.min():.2f}m)\")\n", "logger.info(f\"  Z range: {z.min():.2f} to {z.max():.2f} ({z.max()-z.min():.2f}m)\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ground points: 3243969\n", "Non-ground points: 1401072\n"]}], "source": ["from scipy.ndimage import grey_erosion, grey_dilation\n", "\n", "# Grid the point cloud (2D raster)\n", "min_xy = np.min(points[:, :2], axis=0)\n", "max_xy = np.max(points[:, :2], axis=0)\n", "dims = np.ceil((max_xy - min_xy) / cell_size).astype(int)\n", "grid = np.full(dims, np.nan)\n", "\n", "# Populate raster with lowest Z value per cell\n", "for x, y, z in points:\n", "    xi = int((x - min_xy[0]) / cell_size)\n", "    yi = int((y - min_xy[1]) / cell_size)\n", "    if 0 <= xi < dims[0] and 0 <= yi < dims[1]:\n", "        if np.isnan(grid[xi, yi]) or z < grid[xi, yi]:\n", "            grid[xi, yi] = z\n", "\n", "# Fill holes\n", "filled_grid = ndimage.grey_closing(np.nan_to_num(grid, nan=np.nanmin(grid)), size=3)\n", "\n", "# Morphological opening (erosion then dilation)\n", "opened = grey_dilation(grey_erosion(filled_grid, size=max_window_size), size=max_window_size)\n", "\n", "# Ground mask based on slope threshold\n", "z_diff = filled_grid - opened\n", "ground_mask_2d = z_diff < slope\n", "\n", "# Reconstruct full ground point mask\n", "ground_mask = []\n", "for x, y, z in points:\n", "    xi = int((x - min_xy[0]) / cell_size)\n", "    yi = int((y - min_xy[1]) / cell_size)\n", "    if 0 <= xi < dims[0] and 0 <= yi < dims[1]:\n", "        if ground_mask_2d[xi, yi]:\n", "            ground_mask.append(True)\n", "        else:\n", "            ground_mask.append(False)\n", "ground_mask = np.array(ground_mask)\n", "\n", "ground_points = points[ground_mask]\n", "nonground_points = points[~ground_mask]\n", "\n", "logger.info(f\"Ground points: {ground_points.shape[0]}\")\n", "logger.info(f\"Non-ground points: {nonground_points.shape[0]}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 18:46:31,160 - INFO - Saved: output_runs/Castro_pmf_20250627_184409/analysis_output/Castro_ground.ply\n", "2025-06-27 18:46:31,247 - INFO - Saved: output_runs/Castro_pmf_20250627_184409/analysis_output/Castro_nonground.ply\n"]}], "source": ["# ## Save Output .PLY\n", "def save_ply(path, points_array):\n", "    pc = o3d.geometry.PointCloud()\n", "    pc.points = o3d.utility.Vector3dVector(points_array)\n", "    o3d.io.write_point_cloud(str(path), pc)\n", "    logger.info(f\"Saved: {path}\")\n", "\n", "# Save the point clouds to the appropriate output paths\n", "analysis_output = current_run_path / 'analysis_output'\n", "analysis_output.mkdir(parents=True, exist_ok=True)\n", "\n", "# Save ground and nonground points to the analysis_output directory\n", "save_ply(analysis_output / f\"{site_name}_ground.ply\", ground_points)\n", "save_ply(analysis_output / f\"{site_name}_nonground.ply\", nonground_points)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 18:46:58,577 - INFO - Ground Ratio: 0.6984\n", "2025-06-27 18:46:58,577 - INFO - Non-Ground Ratio: 0.3016\n"]}], "source": ["# Ground/Non-Ground ratio\n", "ground_count = ground_points.shape[0]\n", "nonground_count = nonground_points.shape[0]\n", "total_points = ground_count + nonground_count\n", "\n", "ground_ratio = ground_count / total_points\n", "logger.info(f\"Ground Ratio: {ground_ratio:.4f}\")\n", "\n", "nonground_ratio = nonground_count / total_points\n", "logger.info(f\"Non-Ground Ratio: {nonground_ratio:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 18:47:30,120 - INFO - ground_z_mean:{ground_z_mean}\n", "2025-06-27 18:47:30,120 - INFO - nonground_z_mean:54.29559630978997\n", "2025-06-27 18:47:30,120 - INFO - z_separation: 1.8781530452221773\n"]}], "source": ["ground_z_mean = ground_points[:, 2].mean()\n", "nonground_z_mean = nonground_points[:, 2].mean()\n", "z_separation = nonground_z_mean - ground_z_mean\n", "\n", "logger.info(\"ground_z_mean:{ground_z_mean}\")\n", "logger.info(f\"nonground_z_mean:{nonground_z_mean}\")\n", "logger.info(f\"z_separation: {z_separation}\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 18:47:45,822 - INFO -   Bounding Box Sizes (X, Y, Z):\n", "2025-06-27 18:47:45,822 - INFO - --------------------------------------------------\n", "2025-06-27 18:47:45,823 - INFO -   Ground:     [578.986  289.994   12.1195]\n", "2025-06-27 18:47:45,823 - INFO -   Non-Ground: [585.839 341.498  19.697]\n"]}], "source": ["# Bounding Box Stats\n", "def bounding_box_stats(points):\n", "    min_bound = np.min(points, axis=0)\n", "    max_bound = np.max(points, axis=0)\n", "    return max_bound - min_bound\n", "\n", "ground_bbox = bounding_box_stats(ground_points)\n", "nonground_bbox = bounding_box_stats(nonground_points)\n", "\n", "logger.info(\"  Bounding Box Sizes (X, Y, Z):\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"  Ground:     {ground_bbox}\")\n", "logger.info(f\"  Non-Ground: {nonground_bbox}\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Height (Z) Distribution Plot\n", "plt.figure(figsize=(10, 5))\n", "plt.hist(ground_points[:, 2], bins=100, alpha=0.6, label='Ground Z')\n", "plt.hist(nonground_points[:, 2], bins=100, alpha=0.6, label='Non-Ground Z')\n", "plt.legend()\n", "plt.title(\"Z-Height Distribution\")\n", "plt.xlabel(\"Z (Elevation)\")\n", "plt.ylabel(\"Point Count\")\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# Save parameters to JSON for reproducibility\n", "parameters = {\n", "    \"run_info\": {\n", "        \"timestamp\": timestamp,\n", "        \"site_name\": site_name,\n", "        \"project_type\": project_type,\n", "        \"method\": \"PMF\",\n", "        \"notebook_version\": \"1.1\"\n", "    },\n", "    \"pmf_parameters\": {\n", "        \"cell_size\": cell_size,\n", "        \"max_window_size\": max_window_size,\n", "        \"slope\": slope,\n", "        \"max_distance\": max_distance,\n", "        \"initial_distance\": initial_distance,\n", "        \"height_threshold_ratio\": height_threshold_ratio\n", "    },\n", "    \"processing_parameters\": {\n", "        \"buffer_radius\": buffer_radius\n", "    },\n", "    \"paths\": {\n", "        \"input_path\": str(input_path),\n", "        \"output_path\": str(ground_seg_path),\n", "        \"run_output_path\": str(current_run_path)\n", "    }\n", "}\n", "\n", "# Save parameters to file\n", "params_file = current_run_path / \"parameters.json\"\n", "with open(params_file, 'w') as f:\n", "    json.dump(parameters, f, indent=2)\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-27 18:49:32,177 - INFO - PMF Ground Segmentation - Ready!\n", "2025-06-27 18:49:32,178 - INFO - ==================================================\n", "2025-06-27 18:49:32,178 - INFO - Project: ENEL/Castro\n", "2025-06-27 18:49:32,179 - INFO - --------------------------------------------------\n", "2025-06-27 18:49:32,179 - INFO - Input path: ../../../data/processed/motali_de_castro/denoising/motali_de_castro_denoised.ply\n", "2025-06-27 18:49:32,179 - INFO - Output path: ../../data/ENEL/Castro/ground_segmentation\n", "2025-06-27 18:49:32,179 - INFO - Current run output: output_runs/Castro_pmf_20250627_184409\n", "2025-06-27 18:49:32,180 - INFO - PMF Parameters: cell_size=1.0m, max_window=33, slope=0.15\n"]}], "source": ["# Final readiness logger.info\n", "logger.info(\"PMF Ground Segmentation - Ready!\")\n", "logger.info(\"=\" * 50)\n", "logger.info(f\"Project: {project_type}/{site_name}\")\n", "logger.info(\"-\" * 50)\n", "logger.info(f\"Input path: {input_path}\")\n", "logger.info(f\"Output path: {ground_seg_path}\")\n", "logger.info(f\"Current run output: {current_run_path}\")\n", "logger.info(f\"PMF Parameters: cell_size={cell_size}m, max_window={max_window_size}, slope={slope}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize the results\n", "import open3d as o3d\n", "import numpy as np\n", "\n", "# Create point cloud for ground\n", "pcd_ground = o3d.geometry.PointCloud()\n", "pcd_ground.points = o3d.utility.Vector3dVector(ground_points)\n", "pcd_ground.paint_uniform_color([0.0, 1.0, 0.0])  # Green\n", "\n", "# Create point cloud for non-ground\n", "pcd_nonground = o3d.geometry.PointCloud()\n", "pcd_nonground.points = o3d.utility.Vector3dVector(nonground_points)\n", "pcd_nonground.paint_uniform_color([1.0, 0.0, 0.0])  # Red\n", "\n", "# Show both together\n", "o3d.visualization.draw_geometries([pcd_ground, pcd_nonground],\n", "                                  window_name=\"Ground vs Non-Ground\",\n", "                                  point_show_normal=False)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}