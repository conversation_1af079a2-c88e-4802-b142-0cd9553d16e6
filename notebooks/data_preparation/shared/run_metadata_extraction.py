#!/usr/bin/env python3
"""
Master script to run metadata extraction for CAD and IFC files using Papermill.

This script executes metadata extraction notebooks for specified project sites
with customizable parameters and standardized output formats.
"""

import papermill as pm
import pandas as pd
from datetime import datetime
from pathlib import Path
import json
import argparse
from typing import Dict, List, Optional

# Project site configurations
SITE_CONFIGS = {
    "ENEL": {
        "Trino": {
            "coordinate_system": "EPSG:32643",  # UTM Zone 43N
            "cad_files": "data/ENEL/Trino/raw/",
            "ifc_files": "data/ENEL/Trino/raw/",
            "pile_pattern": r"PILE_(\d+)"
        },
        "Castro": {
            "coordinate_system": "EPSG:32643",
            "cad_files": "data/ENEL/Castro/raw/",
            "ifc_files": "data/ENEL/Castro/raw/",
            "pile_pattern": r"PILE_(\d+)"
        },
        "Mudjar": {
            "coordinate_system": "EPSG:32643",
            "cad_files": "data/ENEL/Mudjar/raw/",
            "ifc_files": "data/ENEL/Mudjar/raw/",
            "pile_pattern": r"PILE_(\d+)"
        },
        "Giorgio": {
            "coordinate_system": "EPSG:32643",
            "cad_files": "data/ENEL/Giorgio/raw/",
            "ifc_files": "data/ENEL/Giorgio/raw/",
            "pile_pattern": r"PILE_(\d+)"
        }
    },
    "USA": {
        "McCarthy": {
            "coordinate_system": "EPSG:32612",  # UTM Zone 12N
            "cad_files": "data/USA/McCarthy/raw/",
            "ifc_files": "data/USA/McCarthy/raw/",
            "pile_pattern": r"PILE_(\d+)"
        },
        "RPCS": {
            "coordinate_system": "EPSG:32614",  # UTM Zone 14N
            "cad_files": "data/USA/RPCS/raw/",
            "ifc_files": "data/USA/RPCS/raw/",
            "pile_pattern": r"PILE_(\d+)"
        },
        "RES": {
            "coordinate_system": "EPSG:32613",  # UTM Zone 13N
            "cad_files": "data/USA/RES/raw/",
            "ifc_files": "data/USA/RES/raw/",
            "pile_pattern": r"PILE_(\d+)"
        }
    }
}

def run_cad_extraction(project_type: str, site_name: str, site_config: Dict, **kwargs):
    """
    Run CAD metadata extraction for a specific site.
    
    Parameters:
    -----------
    project_type : str
        Project type ("ENEL", "USA")
    site_name : str
        Site name
    site_config : dict
        Site-specific configuration
    **kwargs : dict
        Additional parameters to override defaults
        
    Returns:
    --------
    str : Path to output notebook
    """
    
    # Create timestamp for this run
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Define input and output paths
    input_notebook = "metadata_extraction_cad.ipynb"
    output_notebook = f"output_runs/{site_name}_cad_{timestamp}_executed.ipynb"
    
    # Ensure output directory exists
    Path("output_runs").mkdir(exist_ok=True)
    
    # Base parameters
    parameters = {
        'site_name': site_name,
        'project_type': project_type,
        'coordinate_system': site_config['coordinate_system'],
        'target_crs': 'EPSG:4326',  # WGS84
        'pile_name_pattern': site_config['pile_pattern'],
        'include_attributes': True,
        'include_geometry': True,
        'coordinate_transform': True,
        'export_csv': True,
        'export_json': True,
        'export_parquet': True
    }
    
    # Override with any provided kwargs
    parameters.update(kwargs)
    
    print(f"Executing CAD metadata extraction for {project_type}/{site_name}")
    print(f"Output notebook: {output_notebook}")
    
    try:
        # Execute notebook with parameters
        pm.execute_notebook(
            input_notebook,
            output_notebook,
            parameters=parameters,
            log_output=True
        )
        
        print(f"CAD extraction completed for {site_name}")
        return output_notebook
        
    except Exception as e:
        print(f"CAD extraction failed for {site_name}: {e}")
        return None

def run_ifc_extraction(project_type: str, site_name: str, site_config: Dict, **kwargs):
    """
    Run IFC metadata extraction for a specific site.
    
    Parameters:
    -----------
    project_type : str
        Project type ("ENEL", "USA")
    site_name : str
        Site name
    site_config : dict
        Site-specific configuration
    **kwargs : dict
        Additional parameters to override defaults
        
    Returns:
    --------
    str : Path to output notebook
    """
    
    # Create timestamp for this run
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Define input and output paths
    input_notebook = "metadata_extraction_ifc.ipynb"
    output_notebook = f"output_runs/{site_name}_ifc_{timestamp}_executed.ipynb"
    
    # Ensure output directory exists
    Path("output_runs").mkdir(exist_ok=True)
    
    # Base parameters
    parameters = {
        'site_name': site_name,
        'project_type': project_type,
        'coordinate_system': site_config['coordinate_system'],
        'target_crs': 'EPSG:4326',  # WGS84
        'pile_filter_keywords': ["tracker pile", "pile", "column"],
        'include_properties': True,
        'include_geometry': True,
        'include_materials': True,
        'coordinate_transform': True,
        'target_element_types': ["IfcColumn", "IfcPile", "IfcBuildingElementProxy"],
        'export_csv': True,
        'export_json': True,
        'export_parquet': True
    }
    
    # Override with any provided kwargs
    parameters.update(kwargs)
    
    print(f"Executing IFC metadata extraction for {project_type}/{site_name}")
    print(f"Output notebook: {output_notebook}")
    
    try:
        # Execute notebook with parameters
        pm.execute_notebook(
            input_notebook,
            output_notebook,
            parameters=parameters,
            log_output=True
        )
        
        print(f"IFC extraction completed for {site_name}")
        return output_notebook
        
    except Exception as e:
        print(f"IFC extraction failed for {site_name}: {e}")
        return None

def run_all_extractions_for_site(project_type: str, site_name: str, methods: Optional[List[str]] = None):
    """
    Run all metadata extraction methods for a specific site.
    
    Parameters:
    -----------
    project_type : str
        Project type ("ENEL", "USA")
    site_name : str
        Site name
    methods : list, optional
        List of methods to run. If None, runs all methods.
        
    Returns:
    --------
    dict : Results summary
    """
    
    if methods is None:
        methods = ["CAD", "IFC"]
    
    if project_type not in SITE_CONFIGS:
        raise ValueError(f"Unknown project type: {project_type}")
    
    if site_name not in SITE_CONFIGS[project_type]:
        raise ValueError(f"Unknown site: {site_name} for project {project_type}")
    
    site_config = SITE_CONFIGS[project_type][site_name]
    
    results = {
        'site': f"{project_type}/{site_name}",
        'timestamp': datetime.now().isoformat(),
        'methods': {}
    }
    
    print(f"\n=== Running metadata extraction for {project_type}/{site_name} ===")
    print(f"Methods: {', '.join(methods)}")
    
    for method in methods:
        if method.upper() == "CAD":
            output_notebook = run_cad_extraction(project_type, site_name, site_config)
            results['methods']['CAD'] = {
                'output_notebook': output_notebook,
                'success': output_notebook is not None
            }
        elif method.upper() == "IFC":
            output_notebook = run_ifc_extraction(project_type, site_name, site_config)
            results['methods']['IFC'] = {
                'output_notebook': output_notebook,
                'success': output_notebook is not None
            }
    
    return results

def run_all_sites(project_types: Optional[List[str]] = None, methods: Optional[List[str]] = None):
    """
    Run metadata extraction for all configured sites.
    
    Parameters:
    -----------
    project_types : list, optional
        List of project types to process. If None, processes all.
    methods : list, optional
        List of methods to run. If None, runs all methods.
        
    Returns:
    --------
    dict : Complete results summary
    """
    
    if project_types is None:
        project_types = list(SITE_CONFIGS.keys())
    
    if methods is None:
        methods = ["CAD", "IFC"]
    
    all_results = {
        'execution_timestamp': datetime.now().isoformat(),
        'methods_executed': methods,
        'sites': {}
    }
    
    for project_type in project_types:
        if project_type not in SITE_CONFIGS:
            print(f"Warning: Unknown project type {project_type}, skipping")
            continue
            
        for site_name in SITE_CONFIGS[project_type]:
            try:
                site_results = run_all_extractions_for_site(project_type, site_name, methods)
                all_results['sites'][f"{project_type}/{site_name}"] = site_results
            except Exception as e:
                print(f"Error processing {project_type}/{site_name}: {e}")
                all_results['sites'][f"{project_type}/{site_name}"] = {
                    'error': str(e),
                    'success': False
                }
    
    # Save results summary
    results_file = f"output_runs/metadata_extraction_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w') as f:
        json.dump(all_results, f, indent=2)
    
    print(f"\n=== Metadata Extraction Summary ===")
    print(f"Results saved to: {results_file}")
    
    return all_results

def main():
    """Command line interface for the metadata extraction runner."""
    
    parser = argparse.ArgumentParser(description='Run metadata extraction for CAD/IFC files')
    parser.add_argument('--project-type', choices=['ENEL', 'USA'], help='Project type to process')
    parser.add_argument('--site-name', help='Specific site to process')
    parser.add_argument('--methods', nargs='+', choices=['CAD', 'IFC'], 
                       help='Methods to run (default: all)')
    parser.add_argument('--all-sites', action='store_true', help='Run for all configured sites')
    
    args = parser.parse_args()
    
    if args.all_sites:
        # Run for all sites
        project_types = [args.project_type] if args.project_type else None
        run_all_sites(project_types, args.methods)
        
    elif args.project_type and args.site_name:
        # Run for specific site
        run_all_extractions_for_site(args.project_type, args.site_name, args.methods)
        
    else:
        # Interactive mode - show available options
        print("Available project sites:")
        for project_type, sites in SITE_CONFIGS.items():
            print(f"\n{project_type}:")
            for site_name, config in sites.items():
                print(f"  - {site_name} (CRS: {config['coordinate_system']})")
        
        print("\nUsage examples:")
        print("  python run_metadata_extraction.py --all-sites")
        print("  python run_metadata_extraction.py --project-type ENEL --site-name Trino")
        print("  python run_metadata_extraction.py --project-type USA --site-name McCarthy --methods CAD")

if __name__ == "__main__":
    main()
