{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CAD-Guided Pile Detection with Spatial Anchors\n", "\n", "Enhanced pile detection that uses CAD metadata as spatial anchors instead of blind detection.\n", "\n", "## Key Improvements:\n", "- **Spatial Anchoring**: Use CAD tracker positions to guide detection\n", "- **Expected <PERSON><PERSON> Coordinates**: Generate 23,972 expected pile locations from 548 trackers\n", "- **Zone-Based Detection**: Focus detection on expected areas\n", "- **Immediate Validation**: Real-time comparison against expected quantities\n", "- **Quality Assurance**: Flag missing piles and unexpected detections\n", "\n", "## Approach:\n", "```\n", "CAD Metadata → Expected Pile Coordinates → Spatial Search Zones → Guided Detection\n", "```\n", "\n", "Instead of:\n", "```\n", "Point Cloud → Blind Detection → Post-Assignment\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import laspy\n", "from pathlib import Path\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import json\n", "import mlflow\n", "from sklearn.cluster import DBSCAN\n", "from sklearn.neighbors import NearestNeighbors\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configuration\n", "project_name = \"castro_cad_guided_detection\"\n", "site_name = \"montal<PERSON>_di_castro\"\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "print(f\"CAD-Guided Pile Detection - {site_name.title()}\")\n", "print(f\"Timestamp: {timestamp}\")\n", "print(\"=\" * 60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load CAD Metadata and Generate Expected Pile Coordinates"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_cad_metadata():\n", "    \"\"\"Load enhanced CAD metadata with tracker information.\"\"\"\n", "    \n", "    # Try to load the most recent enhanced CAD extraction\n", "    cad_files = [\n", "        \"../output_runs/cad_metadata/updated_cad_extraction_with_foundation_piles_*.csv\",\n", "        \"../data/raw/motali_de_castro/cad/enhanced_output/cad_extraction_pile_20250630_143601.csv\"\n", "    ]\n", "    \n", "    cad_df = None\n", "    for file_pattern in cad_files:\n", "        try:\n", "            if '*' in file_pattern:\n", "                matching_files = list(Path().glob(file_pattern))\n", "                if matching_files:\n", "                    cad_file = matching_files[0]\n", "                    cad_df = pd.read_csv(cad_file)\n", "                    print(f\"Loaded CAD metadata from: {cad_file.name}\")\n", "                    break\n", "            else:\n", "                cad_file = Path(file_pattern)\n", "                if cad_file.exists():\n", "                    cad_df = pd.read_csv(cad_file)\n", "                    print(f\"Loaded CAD metadata from: {cad_file.name}\")\n", "                    break\n", "        except Exception as e:\n", "            print(f\"Could not load {file_pattern}: {e}\")\n", "            continue\n", "    \n", "    if cad_df is None:\n", "        raise FileNotFoundError(\"No CAD metadata file found\")\n", "    \n", "    return cad_df\n", "\n", "def generate_pile_grid_for_tracker(tracker_info):\n", "    \"\"\"Generate expected pile coordinates for a single tracker.\"\"\"\n", "    \n", "    center_x = tracker_info['x_coord']\n", "    center_y = tracker_info['y_coord']\n", "    tracker_type = tracker_info.get('layer_name', '')\n", "    rotation = tracker_info.get('rotation', 0.0)\n", "    \n", "    # Tracker configuration (piles per tracker and layout)\n", "    tracker_configs = {\n", "        'CVT_Tracker 1x52 int': {'piles': 52, 'rows': 2, 'cols': 26, 'spacing_x': 2.0, 'spacing_y': 4.0},\n", "        'CVT_Tracker 1x52 ext': {'piles': 52, 'rows': 2, 'cols': 26, 'spacing_x': 2.0, 'spacing_y': 4.0},\n", "        'CVT_Tracker 1X52 Edge': {'piles': 52, 'rows': 2, 'cols': 26, 'spacing_x': 2.0, 'spacing_y': 4.0},\n", "        'CVT_Tracker 1x26 int': {'piles': 26, 'rows': 1, 'cols': 26, 'spacing_x': 2.0, 'spacing_y': 0.0},\n", "        'CVT_Tracker 1x26 ext': {'piles': 26, 'rows': 1, 'cols': 26, 'spacing_x': 2.0, 'spacing_y': 0.0}\n", "    }\n", "    \n", "    config = tracker_configs.get(tracker_type, {'piles': 26, 'rows': 1, 'cols': 26, 'spacing_x': 2.0, 'spacing_y': 0.0})\n", "    \n", "    pile_positions = []\n", "    \n", "    # Generate grid of pile positions\n", "    for row in range(config['rows']):\n", "        for col in range(config['cols']):\n", "            # Calculate relative position\n", "            rel_x = (col - config['cols']/2) * config['spacing_x']\n", "            rel_y = (row - config['rows']/2) * config['spacing_y']\n", "            \n", "            # Apply rotation if needed\n", "            if rotation != 0:\n", "                cos_r = np.cos(np.radians(rotation))\n", "                sin_r = np.sin(np.radians(rotation))\n", "                rotated_x = rel_x * cos_r - rel_y * sin_r\n", "                rotated_y = rel_x * sin_r + rel_y * cos_r\n", "                rel_x, rel_y = rotated_x, rotated_y\n", "            \n", "            # Calculate absolute position\n", "            pile_x = center_x + rel_x\n", "            pile_y = center_y + rel_y\n", "            \n", "            pile_positions.append({\n", "                'x': pile_x,\n", "                'y': pile_y,\n", "                'z': tracker_info.get('z_coord', 0.0),  # Use tracker Z as reference\n", "                'tracker_id': tracker_info['entity_id'],\n", "                'tracker_type': tracker_type,\n", "                'pile_index': len(pile_positions),\n", "                'expected': True\n", "            })\n", "    \n", "    return pile_positions\n", "\n", "def generate_all_expected_pile_coordinates(cad_df):\n", "    \"\"\"Generate expected pile coordinates for all trackers.\"\"\"\n", "    \n", "    # Filter for tracker entities\n", "    tracker_patterns = ['CVT_Tracker 1x52 int', 'CVT_Tracker 1x52 ext', 'CVT_Tracker 1X52 Edge',\n", "                       'CVT_Tracker 1x26 int', 'CVT_Tracker 1x26 ext']\n", "    \n", "    trackers = cad_df[cad_df['layer_name'].isin(tracker_patterns)]\n", "    \n", "    print(f\"Found {len(trackers)} trackers in CAD data\")\n", "    \n", "    all_expected_piles = []\n", "    \n", "    for _, tracker in trackers.iterrows():\n", "        pile_positions = generate_pile_grid_for_tracker(tracker)\n", "        all_expected_piles.extend(pile_positions)\n", "    \n", "    expected_piles_df = pd.DataFrame(all_expected_piles)\n", "    \n", "    print(f\"Generated {len(expected_piles_df)} expected pile coordinates\")\n", "    \n", "    # Summary by tracker type\n", "    print(f\"\\nExpected piles by tracker type:\")\n", "    tracker_summary = expected_piles_df['tracker_type'].value_counts()\n", "    for tracker_type, count in tracker_summary.items():\n", "        print(f\"  {tracker_type}: {count} piles\")\n", "    \n", "    return expected_piles_df\n", "\n", "# Load CAD metadata and generate expected pile coordinates\n", "cad_df = load_cad_metadata()\n", "expected_piles_df = generate_all_expected_pile_coordinates(cad_df)\n", "\n", "print(f\"\\nCAD metadata loaded: {len(cad_df)} entities\")\n", "print(f\"Expected pile coordinates generated: {len(expected_piles_df)} locations\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Load Point Cloud Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_point_cloud_data():\n", "    \"\"\"Load and preprocess point cloud data.\"\"\"\n", "    \n", "    # Point cloud file path\n", "    las_file_path = Path(\"../data/raw/motali_de_castro/point_clouds/Area1_Part2_Points.las\")\n", "    \n", "    if not las_file_path.exists():\n", "        raise FileNotFoundError(f\"Point cloud file not found: {las_file_path}\")\n", "    \n", "    print(f\"Loading point cloud: {las_file_path.name}\")\n", "    \n", "    # Load LAS file\n", "    las_file = laspy.read(las_file_path)\n", "    \n", "    # Extract coordinates\n", "    points = np.vstack((las_file.x, las_file.y, las_file.z)).transpose()\n", "    \n", "    print(f\"Loaded {len(points):,} points\")\n", "    print(f\"Point cloud bounds:\")\n", "    print(f\"  X: {points[:, 0].min():.2f} to {points[:, 0].max():.2f}\")\n", "    print(f\"  Y: {points[:, 1].min():.2f} to {points[:, 1].max():.2f}\")\n", "    print(f\"  Z: {points[:, 2].min():.2f} to {points[:, 2].max():.2f}\")\n", "    \n", "    return points, las_file\n", "\n", "# Load point cloud\n", "points, las_file = load_point_cloud_data()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Create Spatial Search Zones"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_detection_zones(expected_piles_df, search_radius=2.0):\n", "    \"\"\"Create spatial search zones around expected pile locations.\"\"\"\n", "    \n", "    detection_zones = []\n", "    \n", "    for _, pile in expected_piles_df.iterrows():\n", "        zone = {\n", "            'center_x': pile['x'],\n", "            'center_y': pile['y'],\n", "            'center_z': pile['z'],\n", "            'search_radius': search_radius,\n", "            'tracker_id': pile['tracker_id'],\n", "            'tracker_type': pile['tracker_type'],\n", "            'pile_index': pile['pile_index'],\n", "            'priority': 'high',  # Expected location\n", "            'zone_id': f\"{pile['tracker_id']}_{pile['pile_index']}\"\n", "        }\n", "        detection_zones.append(zone)\n", "    \n", "    zones_df = pd.DataFrame(detection_zones)\n", "    \n", "    print(f\"Created {len(zones_df)} detection zones\")\n", "    print(f\"Search radius: {search_radius} meters\")\n", "    \n", "    return zones_df\n", "\n", "def extract_points_in_zone(points, zone, z_tolerance=5.0):\n", "    \"\"\"Extract point cloud subset within a detection zone.\"\"\"\n", "    \n", "    center_x = zone['center_x']\n", "    center_y = zone['center_y']\n", "    center_z = zone['center_z']\n", "    radius = zone['search_radius']\n", "    \n", "    # Calculate distances\n", "    dx = points[:, 0] - center_x\n", "    dy = points[:, 1] - center_y\n", "    dz = np.abs(points[:, 2] - center_z)\n", "    \n", "    # Horizontal distance\n", "    horizontal_distance = np.sqrt(dx**2 + dy**2)\n", "    \n", "    # Filter points within zone\n", "    zone_mask = (horizontal_distance <= radius) & (dz <= z_tolerance)\n", "    zone_points = points[zone_mask]\n", "    \n", "    return zone_points\n", "\n", "# Create detection zones\n", "search_radius = 2.0  # meters\n", "detection_zones_df = create_detection_zones(expected_piles_df, search_radius)\n", "\n", "print(f\"\\nDetection zones created: {len(detection_zones_df)}\")\n", "print(f\"Zone coverage area: {len(detection_zones_df) * np.pi * search_radius**2:.1f} m²\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. CAD-Guided Pile Detection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def detect_pile_in_zone(zone_points, min_points=50, cluster_eps=0.3):\n", "    \"\"\"Detect pile in a specific zone using clustering.\"\"\"\n", "    \n", "    if len(zone_points) < min_points:\n", "        return None\n", "    \n", "    # Apply DBSCAN clustering to find pile-like structures\n", "    clustering = DBSCAN(eps=cluster_eps, min_samples=min_points//2)\n", "    cluster_labels = clustering.fit_predict(zone_points)\n", "    \n", "    # Find the largest cluster (most likely to be a pile)\n", "    unique_labels = np.unique(cluster_labels)\n", "    valid_labels = unique_labels[unique_labels != -1]  # Exclude noise\n", "    \n", "    if len(valid_labels) == 0:\n", "        return None\n", "    \n", "    # Get the largest cluster\n", "    cluster_sizes = [(label, np.sum(cluster_labels == label)) for label in valid_labels]\n", "    largest_cluster_label = max(cluster_sizes, key=lambda x: x[1])[0]\n", "    \n", "    # Extract pile points\n", "    pile_points = zone_points[cluster_labels == largest_cluster_label]\n", "    \n", "    # Calculate pile properties\n", "    pile_center = np.mean(pile_points, axis=0)\n", "    pile_height = pile_points[:, 2].max() - pile_points[:, 2].min()\n", "    pile_point_count = len(pile_points)\n", "    \n", "    # Calculate pile confidence based on point density and height\n", "    confidence = min(1.0, (pile_point_count / min_points) * (pile_height / 2.0))\n", "    \n", "    pile_detection = {\n", "        'detected': True,\n", "        'center_x': pile_center[0],\n", "        'center_y': pile_center[1],\n", "        'center_z': pile_center[2],\n", "        'height': pile_height,\n", "        'point_count': pile_point_count,\n", "        'confidence': confidence,\n", "        'cluster_label': largest_cluster_label\n", "    }\n", "    \n", "    return pile_detection\n", "\n", "def run_cad_guided_detection(points, detection_zones_df, sample_size=1000):\n", "    \"\"\"Run CAD-guided pile detection on all zones.\"\"\"\n", "    \n", "    detection_results = []\n", "    \n", "    # Sample zones for demonstration (process all in production)\n", "    sample_zones = detection_zones_df.sample(n=min(sample_size, len(detection_zones_df)))\n", "    \n", "    print(f\"Running CAD-guided detection on {len(sample_zones)} zones...\")\n", "    \n", "    for idx, (_, zone) in enumerate(sample_zones.iterrows()):\n", "        if idx % 100 == 0:\n", "            print(f\"  Processed {idx}/{len(sample_zones)} zones\")\n", "        \n", "        # Extract points in zone\n", "        zone_points = extract_points_in_zone(points, zone)\n", "        \n", "        # Attempt pile detection in zone\n", "        pile_detection = detect_pile_in_zone(zone_points)\n", "        \n", "        # Record result\n", "        result = {\n", "            'zone_id': zone['zone_id'],\n", "            'tracker_id': zone['tracker_id'],\n", "            'tracker_type': zone['tracker_type'],\n", "            'expected_x': zone['center_x'],\n", "            'expected_y': zone['center_y'],\n", "            'expected_z': zone['center_z'],\n", "            'zone_point_count': len(zone_points),\n", "            'detection_timestamp': datetime.now().isoformat()\n", "        }\n", "        \n", "        if pile_detection:\n", "            result.update({\n", "                'pile_detected': True,\n", "                'detected_x': pile_detection['center_x'],\n", "                'detected_y': pile_detection['center_y'],\n", "                'detected_z': pile_detection['center_z'],\n", "                'pile_height': pile_detection['height'],\n", "                'pile_point_count': pile_detection['point_count'],\n", "                'detection_confidence': pile_detection['confidence'],\n", "                'distance_from_expected': np.sqrt(\n", "                    (pile_detection['center_x'] - zone['center_x'])**2 + \n", "                    (pile_detection['center_y'] - zone['center_y'])**2\n", "                )\n", "            })\n", "        else:\n", "            result.update({\n", "                'pile_detected': False,\n", "                'detected_x': None,\n", "                'detected_y': None,\n", "                'detected_z': None,\n", "                'pile_height': None,\n", "                'pile_point_count': None,\n", "                'detection_confidence': 0.0,\n", "                'distance_from_expected': None\n", "            })\n", "        \n", "        detection_results.append(result)\n", "    \n", "    results_df = pd.DataFrame(detection_results)\n", "    \n", "    print(f\"\\nCAD-guided detection completed\")\n", "    print(f\"Total zones processed: {len(results_df)}\")\n", "    \n", "    return results_df\n", "\n", "# Run CAD-guided detection\n", "detection_results_df = run_cad_guided_detection(points, detection_zones_df, sample_size=500)\n", "\n", "print(f\"Detection results: {len(detection_results_df)} zones processed\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}