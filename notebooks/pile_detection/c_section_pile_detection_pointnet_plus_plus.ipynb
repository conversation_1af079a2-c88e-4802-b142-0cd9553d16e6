{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# C-Section Pile Detection (PointNet++)\n", "\n", "This notebook implements deep learning-based C-section pile detection using PointNet++ architecture as part of the pile detection stage. It processes ground-filtered or aligned point clouds and detects C-section pile structures using hierarchical feature learning.\n", "\n", "**Stage**: <PERSON>le Detection  \n", "**Input Data**: Ground-filtered or aligned point cloud  \n", "**Output**: Pile center coordinates + types (C-section, cylindrical, etc.)  \n", "**Format**: .csv (columns: x, y, z, pile_type, confidence, etc.)  \n", "**Model**: PointNet++ for hierarchical point-wise classification  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Process Overview:\n", "1. **Load Ground-Filtered Data**: Import processed point cloud from ground segmentation or alignment\n", "2. **Patch Generation**: Create overlapping 3D patches for PointNet++ processing\n", "3. **C-Section Detection**: Apply PointNet++ model for hierarchical point-wise classification\n", "4. **Post-Processing**: Cluster detected points and extract pile centers\n", "5. **Export Results**: Save detected pile data in .csv format"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install torch torchvision torch-geometric torch-points3d open3d matplotlib numpy scipy pandas\n", "!pip install scikit-learn plotly"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import os\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import open3d as o3d\n", "from sklearn.neighbors import NearestNeighbors\n", "from sklearn.cluster import DBSCAN\n", "from scipy.spatial import ConvexHull\n", "from scipy.spatial.distance import cdist\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.utils.data import Dataset, DataLoader\n", "from torch_geometric.nn import PointConv, global_max_pool, global_mean_pool\n", "from torch_geometric.data import Data, Batch\n", "from torch_geometric.nn import knn_graph\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. C-<PERSON>le Characteristics\n", "\n", "Define the geometric characteristics of C-section piles for detection."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class CSectionPileConfig:\n", "    \"\"\"\n", "    Configuration for C-section pile detection using PointNet++.\n", "    \"\"\"\n", "    def __init__(self):\n", "        # C-section dimensions (typical ranges)\n", "        self.flange_width_range = (0.05, 0.3)  # meters\n", "        self.web_height_range = (0.1, 0.8)  # meters\n", "        self.thickness_range = (0.005, 0.03)  # meters\n", "        self.opening_width_range = (0.08, 0.25)  # meters (gap between flanges)\n", "        self.height_range = (0.1, 1.0)  # meters\n", "        \n", "        # Detection parameters\n", "        self.patch_size = 2.0  # meters\n", "        self.min_points_per_patch = 100\n", "        self.overlap_ratio = 0.5\n", "        \n", "        # PointNet++ parameters\n", "        self.num_points = 1024\n", "        self.num_classes = 2  # pile vs non-pile\n", "        \n", "        # Set abstraction parameters\n", "        self.sa_npoints = [512, 128, None]  # Number of points in each SA layer\n", "        self.sa_radius = [0.2, 0.4, None]   # Radius for each SA layer\n", "        self.sa_nsample = [64, 64, None]    # Number of samples in each SA layer\n", "        self.sa_mlps = [[64, 64, 128], [128, 128, 256], [256, 512, 1024]]  # MLP dimensions\n", "        \n", "        # Feature propagation parameters\n", "        self.fp_mlps = [[256, 256], [256, 128], [128, 128, 128]]  # FP MLP dimensions\n", "\n", "config = CSectionPileConfig()\n", "print(\"C-Section Pile Detection Configuration (PointNet++):\")\n", "print(f\"Flange width range: {config.flange_width_range} m\")\n", "print(f\"Web height range: {config.web_height_range} m\")\n", "print(f\"Opening width range: {config.opening_width_range} m\")\n", "print(f\"Thickness range: {config.thickness_range} m\")\n", "print(f\"Patch size: {config.patch_size} m\")\n", "print(f\"Set abstraction layers: {len(config.sa_npoints)}\")\n", "print(f\"Feature propagation layers: {len(config.fp_mlps)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Point Cloud Patch Generation\n", "\n", "Generate patches from point clouds for training and inference."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_patches_from_point_cloud(points, colors=None, patch_size=2.0, overlap_ratio=0.5, min_points=100):\n", "    \"\"\"\n", "    Generate overlapping patches from a point cloud.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud coordinates (N, 3)\n", "    colors : numpy.ndarray, optional\n", "        RGB colors (N, 3)\n", "    patch_size : float\n", "        Size of each patch in meters\n", "    overlap_ratio : float\n", "        Overlap ratio between adjacent patches\n", "    min_points : int\n", "        Minimum number of points per patch\n", "        \n", "    Returns:\n", "    --------\n", "    patches : list\n", "        List of patch dictionaries containing points, colors, and metadata\n", "    \"\"\"\n", "    # Calculate bounds\n", "    min_coords = points.min(axis=0)\n", "    max_coords = points.max(axis=0)\n", "    \n", "    # Calculate step size\n", "    step_size = patch_size * (1 - overlap_ratio)\n", "    \n", "    patches = []\n", "    patch_id = 0\n", "    \n", "    # Generate grid of patch centers\n", "    x_centers = np.arange(min_coords[0], max_coords[0], step_size)\n", "    y_centers = np.arange(min_coords[1], max_coords[1], step_size)\n", "    \n", "    for x_center in x_centers:\n", "        for y_center in y_centers:\n", "            # Define patch bounds\n", "            x_min = x_center - patch_size / 2\n", "            x_max = x_center + patch_size / 2\n", "            y_min = y_center - patch_size / 2\n", "            y_max = y_center + patch_size / 2\n", "            \n", "            # Find points within patch\n", "            mask = ((points[:, 0] >= x_min) & (points[:, 0] <= x_max) &\n", "                   (points[:, 1] >= y_min) & (points[:, 1] <= y_max))\n", "            \n", "            patch_points = points[mask]\n", "            \n", "            if len(patch_points) >= min_points:\n", "                # Center the patch points\n", "                patch_center = np.array([x_center, y_center, patch_points[:, 2].mean()])\n", "                centered_points = patch_points - patch_center\n", "                \n", "                patch_data = {\n", "                    'id': patch_id,\n", "                    'points': centered_points,\n", "                    'original_points': patch_points,\n", "                    'center': patch_center,\n", "                    'bounds': (x_min, y_min, x_max, y_max),\n", "                    'num_points': len(patch_points)\n", "                }\n", "                \n", "                if colors is not None:\n", "                    patch_data['colors'] = colors[mask]\n", "                \n", "                patches.append(patch_data)\n", "                patch_id += 1\n", "    \n", "    print(f\"Generated {len(patches)} patches from point cloud\")\n", "    return patches"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. C-Section Pile Geometric Analysis\n", "\n", "Analyze point cloud patches for C-section pile characteristics."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_c_section_geometry(points, tolerance=0.05):\n", "    \"\"\"\n", "    Analyze point cloud patch for C-section pile geometry.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Point cloud patch (N, 3)\n", "    tolerance : float\n", "        Tolerance for geometric analysis\n", "        \n", "    Returns:\n", "    --------\n", "    features : dict\n", "        Geometric features indicating C-section characteristics\n", "    \"\"\"\n", "    if len(points) < 10:\n", "        return None\n", "    \n", "    # Project points to XY plane for cross-section analysis\n", "    xy_points = points[:, :2]\n", "    \n", "    # Find convex hull\n", "    try:\n", "        hull = ConvexHull(xy_points)\n", "        hull_points = xy_points[hull.vertices]\n", "    except:\n", "        return None\n", "    \n", "    # Calculate bounding box\n", "    min_coords = points.min(axis=0)\n", "    max_coords = points.max(axis=0)\n", "    dimensions = max_coords - min_coords\n", "    \n", "    # Analyze cross-sectional shape for C-section\n", "    # For C-section, we expect:\n", "    # 1. U-shaped or C-shaped cross-section\n", "    # 2. Two flanges connected by a web\n", "    # 3. Opening on one side\n", "    # 4. Specific width-to-height ratios\n", "    \n", "    # Calculate aspect ratios\n", "    xy_aspect = dimensions[0] / dimensions[1] if dimensions[1] > 0 else 0\n", "    if xy_aspect < 1:\n", "        xy_aspect = 1 / xy_aspect\n", "    \n", "    # Analyze point distribution for C-section pattern\n", "    # Cluster points in cross-section\n", "    clustering = DBSCAN(eps=tolerance, min_samples=5)\n", "    cluster_labels = clustering.fit_predict(xy_points)\n", "    \n", "    unique_labels = np.unique(cluster_labels)\n", "    num_clusters = len(unique_labels[unique_labels >= 0])  # Exclude noise (-1)\n", "    \n", "    # Calculate density distribution for C-shape detection\n", "    center_x, center_y = xy_points.mean(axis=0)\n", "    \n", "    # Divide into radial sectors to detect C-shape opening\n", "    angles = np.arctan2(xy_points[:, 1] - center_y, xy_points[:, 0] - center_x)\n", "    angle_bins = np.linspace(-np.pi, np.pi, 16)\n", "    angle_hist, _ = np.histogram(angles, bins=angle_bins)\n", "    \n", "    # Find potential opening (sector with low point density)\n", "    min_density_sector = np.argmin(angle_hist)\n", "    opening_ratio = angle_hist[min_density_sector] / (angle_hist.max() + 1e-6)\n", "    \n", "    # Calculate concavity measure\n", "    # C-sections should have higher concavity than solid shapes\n", "    hull_area = hull.volume if hasattr(hull, 'volume') else 0\n", "    point_area = len(points) * tolerance * tolerance  # Approximate area\n", "    concavity = 1 - (point_area / (hull_area + 1e-6))\n", "    \n", "    # Analyze symmetry (C-sections often have some symmetry)\n", "    # Check for reflection symmetry across different axes\n", "    reflected_x = np.column_stack([-xy_points[:, 0], xy_points[:, 1]])\n", "    reflected_y = np.column_stack([xy_points[:, 0], -xy_points[:, 1]])\n", "    \n", "    # Calculate symmetry scores (simplified)\n", "    x_symmetry = np.mean(np.min(cdist(xy_points, reflected_x), axis=1))\n", "    y_symmetry = np.mean(np.min(cdist(xy_points, reflected_y), axis=1))\n", "    \n", "    # Calculate features\n", "    features = {\n", "        'num_points': len(points),\n", "        'dimensions': dimensions,\n", "        'xy_aspect_ratio': xy_aspect,\n", "        'height': dimensions[2],\n", "        'width': max(dimensions[0], dimensions[1]),\n", "        'thickness': min(dimensions[0], dimensions[1]),\n", "        'num_clusters': num_clusters,\n", "        'hull_area': hull_area,\n", "        'opening_ratio': opening_ratio,\n", "        'concavity': concavity,\n", "        'x_symmetry': x_symmetry,\n", "        'y_symmetry': y_symmetry,\n", "        'compactness': len(points) / hull_area if hull_area > 0 else 0,\n", "        'c_shape_score': opening_ratio * concavity * (1 - min(x_symmetry, y_symmetry))\n", "    }\n", "    \n", "    return features"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. PointNet++ Model for C-Section Pile Detection\n", "\n", "Implement PointNet++ architecture for hierarchical point cloud feature learning.\n", "\n", "Note: The PointNet++ architecture is identical to the I-section version but will be trained specifically for C-section pile characteristics."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import the PointNet++ components from the I-section notebook\n", "# In practice, these would be in a shared module\n", "\n", "# For brevity, we'll reference the same SetAbstractionLayer and FeaturePropagationLayer\n", "# classes from the I-section notebook. In a real implementation, these would be\n", "# in a shared utils module.\n", "\n", "# Here we'll define the C-section specific model\n", "class PointNetPlusPlusCSectionPile(nn.Module):\n", "    \"\"\"\n", "    PointNet++ model for C-section pile detection.\n", "    \n", "    Note: This uses the same architecture as I-section but will be trained\n", "    specifically for C-section pile characteristics.\n", "    \"\"\"\n", "    def __init__(self, num_classes=2, dropout=0.5):\n", "        super(PointNetPlusPlusCSectionPile, self).__init__()\n", "        self.num_classes = num_classes\n", "        \n", "        # For demonstration, we'll use a simplified architecture\n", "        # In practice, you would import the full SetAbstraction and FeaturePropagation layers\n", "        \n", "        # Simplified feature extraction layers\n", "        self.feature_extractor = nn.Sequential(\n", "            nn.Conv1d(3, 64, 1),\n", "            nn.<PERSON><PERSON><PERSON>orm1d(64),\n", "            nn.ReLU(),\n", "            nn.Conv1d(64, 128, 1),\n", "            nn.<PERSON>chNorm1d(128),\n", "            nn.ReLU(),\n", "            nn.Conv1d(128, 256, 1),\n", "            nn.BatchNorm1d(256),\n", "            nn.ReLU(),\n", "            nn.Conv1d(256, 512, 1),\n", "            nn.BatchNorm1d(512),\n", "            nn.ReLU(),\n", "            nn.Conv1d(512, 1024, 1),\n", "            nn.<PERSON>ch<PERSON>orm1d(1024),\n", "            nn.ReLU()\n", "        )\n", "        \n", "        # Global classification (patch-level)\n", "        self.global_classifier = nn.Sequential(\n", "            nn.<PERSON>(1024, 512),\n", "            nn.BatchNorm1d(512),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.<PERSON><PERSON>(512, 256),\n", "            nn.BatchNorm1d(256),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(256, num_classes)\n", "        )\n", "        \n", "        # Point-wise classification\n", "        self.point_classifier = nn.Sequential(\n", "            nn.Conv1d(1024, 512, 1),\n", "            nn.BatchNorm1d(512),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.Conv1d(512, 256, 1),\n", "            nn.BatchNorm1d(256),\n", "            nn.ReLU(),\n", "            nn.Dropout(dropout),\n", "            nn.Conv1d(256, num_classes, 1)\n", "        )\n", "    \n", "    def forward(self, xyz):\n", "        \"\"\"\n", "        Parameters:\n", "        -----------\n", "        xyz : torch.Tensor\n", "            Point coordinates (B, N, 3)\n", "            \n", "        Returns:\n", "        --------\n", "        global_pred : torch.Tensor\n", "            Global classification logits (B, num_classes)\n", "        point_pred : torch.Tensor\n", "            Point-wise classification logits (B, N, num_classes)\n", "        \"\"\"\n", "        # Transpose for conv1d: (B, N, 3) -> (B, 3, N)\n", "        x = xyz.transpose(2, 1)\n", "        \n", "        # Extract features\n", "        features = self.feature_extractor(x)  # (B, 1024, N)\n", "        \n", "        # Global classification\n", "        global_features = torch.max(features, 2)[0]  # (B, 1024)\n", "        global_pred = self.global_classifier(global_features)\n", "        \n", "        # Point-wise classification\n", "        point_pred = self.point_classifier(features)  # (B, num_classes, N)\n", "        point_pred = point_pred.transpose(2, 1)  # (B, N, num_classes)\n", "        \n", "        return global_pred, point_pred\n", "\n", "print(\"PointNet++ C-Section Pile Detection Model defined\")\n", "print(\"Note: This is a simplified version. Full implementation would use\")\n", "print(\"the complete SetAbstraction and FeaturePropagation layers.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}