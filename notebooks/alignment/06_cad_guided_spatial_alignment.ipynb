{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CAD-Guided Spatial Alignment\n", "\n", "Uses extracted CAD metadata to guide point cloud alignment and spatial validation.\n", "\n", "**Purpose**: Leverage CAD tracker positions and module boundaries for improved point cloud registration  \n", "**Input**: CAD extraction results + Point cloud data  \n", "**Output**: Aligned point clouds with CAD reference validation  \n", "\n", "## Alignment Strategy:\n", "Based on actual CAD content analysis:\n", "- **Primary**: Use tracker coordinates (1,417 points) as alignment anchors\n", "- **Secondary**: Use module boundaries (161 areas) for spatial extent validation\n", "- **Validation**: Compare aligned point cloud features against CAD infrastructure\n", "\n", "## Integration with Existing Workflows:\n", "- Enhances ICP alignment with CAD reference points\n", "- Provides ground truth for neural network alignment validation\n", "- Enables hybrid alignment approaches using CAD + point cloud features\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Papermill parameters\n", "project_type = \"solar_alignment\"\n", "site_name = \"cad_guided\"\n", "cad_extraction_dir = \"../../output_runs/cad_metadata\"  # CAD extraction results\n", "point_cloud_dir = \"../../data/processed\"  # Point cloud data\n", "output_dir = \"../../output_runs/alignment\"\n", "coordinate_system = \"EPSG:32633\"  # Target coordinate system\n", "alignment_method = \"cad_guided\"  # cad_guided, hybrid, validation"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CAD-Guided Spatial Alignment - Solar_Alignment\n", "Timestamp: 2025-07-01 14:14:22\n", "============================================================\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "import json\n", "from datetime import datetime\n", "import logging\n", "import open3d as o3d\n", "from sklearn.neighbors import NearestNeighbors\n", "from scipy.spatial.distance import cdist\n", "import matplotlib.pyplot as plt\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "print(f\"CAD-Guided Spatial Alignment - {project_type.title()}\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(\"=\" * 60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load CAD Reference Data"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== LOADING CAD REFERENCE DATA ===\n", "Loading CAD extraction from: solar_project_alignment_20250701_141156\n", "Loaded 1110 tracker coordinates\n", "Loaded 60 module boundaries\n", "Loaded 1182 total alignment entities\n", "\n", "Tracker coordinate analysis:\n", "  X range: 0.0 to 719651.3\n", "  Y range: 0.0 to 4694237.9\n", "  Z range: 0.0 to 1.6\n", "  Created reference point cloud with 1110 points\n"]}], "source": ["def load_latest_cad_extraction(extraction_dir):\n", "    \"\"\"Load the most recent CAD extraction results.\"\"\"\n", "    extraction_path = Path(extraction_dir)\n", "    \n", "    # Find latest solar alignment extraction\n", "    solar_dirs = list(extraction_path.glob(\"solar_project_alignment_*\"))\n", "    if not solar_dirs:\n", "        # Fallback to any CAD extraction\n", "        solar_dirs = list(extraction_path.glob(\"*alignment*\"))\n", "    \n", "    if not solar_dirs:\n", "        raise FileNotFoundError(f\"No CAD extraction results found in {extraction_dir}\")\n", "    \n", "    latest_dir = sorted(solar_dirs)[-1]\n", "    print(f\"Loading CAD extraction from: {latest_dir.name}\")\n", "    \n", "    # Load tracker coordinates (primary alignment points)\n", "    tracker_files = list(latest_dir.glob(\"tracker_coordinates_*.csv\"))\n", "    if tracker_files:\n", "        tracker_coords = pd.read_csv(tracker_files[0])\n", "        print(f\"Loaded {len(tracker_coords)} tracker coordinates\")\n", "    else:\n", "        tracker_coords = pd.DataFrame()\n", "        print(\"No tracker coordinates found\")\n", "    \n", "    # Load module boundaries (secondary validation)\n", "    module_files = list(latest_dir.glob(\"module_boundaries_*.csv\"))\n", "    if module_files:\n", "        module_boundaries = pd.read_csv(module_files[0])\n", "        print(f\"Loaded {len(module_boundaries)} module boundaries\")\n", "    else:\n", "        module_boundaries = pd.DataFrame()\n", "        print(\"No module boundaries found\")\n", "    \n", "    # Load complete alignment data\n", "    complete_files = list(latest_dir.glob(\"solar_alignment_data_*.csv\"))\n", "    if complete_files:\n", "        complete_data = pd.read_csv(complete_files[0])\n", "        print(f\"Loaded {len(complete_data)} total alignment entities\")\n", "    else:\n", "        complete_data = pd.DataFrame()\n", "        print(\"No complete alignment data found\")\n", "    \n", "    return {\n", "        'tracker_coords': tracker_coords,\n", "        'module_boundaries': module_boundaries,\n", "        'complete_data': complete_data,\n", "        'extraction_dir': latest_dir\n", "    }\n", "\n", "# Load CAD reference data\n", "print(\"=== LOADING CAD REFERENCE DATA ===\")\n", "try:\n", "    cad_data = load_latest_cad_extraction(cad_extraction_dir)\n", "    \n", "    # Analyze CAD reference points\n", "    if not cad_data['tracker_coords'].empty:\n", "        tracker_coords = cad_data['tracker_coords']\n", "        print(f\"\\nTracker coordinate analysis:\")\n", "        print(f\"  X range: {tracker_coords['x_coord'].min():.1f} to {tracker_coords['x_coord'].max():.1f}\")\n", "        print(f\"  Y range: {tracker_coords['y_coord'].min():.1f} to {tracker_coords['y_coord'].max():.1f}\")\n", "        print(f\"  Z range: {tracker_coords['z_coord'].min():.1f} to {tracker_coords['z_coord'].max():.1f}\")\n", "        \n", "        # Create reference point cloud from trackers\n", "        tracker_points = tracker_coords[['x_coord', 'y_coord', 'z_coord']].values\n", "        cad_reference_cloud = o3d.geometry.PointCloud()\n", "        cad_reference_cloud.points = o3d.utility.Vector3dVector(tracker_points)\n", "        print(f\"  Created reference point cloud with {len(tracker_points)} points\")\n", "    else:\n", "        print(\"No tracker coordinates available for alignment\")\n", "        cad_reference_cloud = None\n", "        \n", "except Exception as e:\n", "    logger.error(f\"Error loading CAD data: {e}\")\n", "    cad_data = None\n", "    cad_reference_cloud = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Load Point Cloud Data"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== LOADING POINT CLOUD DATA ===\n", "Found 6 point cloud files:\n", "  1. <PERSON>_ransac_pmf_ground.ply (14.5 MB)\n", "  2. <PERSON>_ransac_pmf_nonground.ply (8.3 MB)\n", "  3. motali_de_castro_denoised.ply (106.3 MB)\n", "  4. motali_de_castro_step1_statistical_filtered.ply (112.3 MB)\n", "  5. motali_de_castro_step2_radius_filtered.ply (110.5 MB)\n", "\n", "Loading sample from: Castro_ransac_pmf_ground.ply\n", "Loaded point cloud with 100000 points\n", "Point cloud analysis:\n", "  X range: 707256.7 to 707837.6\n", "  Y range: 4692831.7 to 4693147.0\n", "  Z range: 49.2 to 59.1\n"]}], "source": ["def discover_point_clouds(point_cloud_dir):\n", "    \"\"\"Discover available point cloud files.\"\"\"\n", "    pc_path = Path(point_cloud_dir)\n", "    \n", "    # Look for common point cloud formats\n", "    patterns = ['**/*.las', '**/*.laz', '**/*.ply', '**/*.pcd', '**/*.xyz']\n", "    point_clouds = []\n", "    \n", "    for pattern in patterns:\n", "        point_clouds.extend(pc_path.glob(pattern))\n", "    \n", "    return sorted(point_clouds)\n", "\n", "def load_point_cloud_sample(file_path, max_points=100000):\n", "    \"\"\"Load a sample of point cloud for alignment testing.\"\"\"\n", "    try:\n", "        if file_path.suffix.lower() in ['.las', '.laz']:\n", "            import laspy\n", "            las = laspy.read(file_path)\n", "            points = np.vstack([las.x, las.y, las.z]).transpose()\n", "            \n", "            # Sample if too large\n", "            if len(points) > max_points:\n", "                indices = np.random.choice(len(points), max_points, replace=False)\n", "                points = points[indices]\n", "            \n", "            # Create Open3D point cloud\n", "            pcd = o3d.geometry.PointCloud()\n", "            pcd.points = o3d.utility.Vector3dVector(points)\n", "            \n", "            return pcd\n", "            \n", "        elif file_path.suffix.lower() in ['.ply', '.pcd']:\n", "            pcd = o3d.io.read_point_cloud(str(file_path))\n", "            \n", "            # Sample if too large\n", "            if len(pcd.points) > max_points:\n", "                pcd = pcd.random_down_sample(max_points / len(pcd.points))\n", "            \n", "            return pcd\n", "        else:\n", "            print(f\"Unsupported format: {file_path.suffix}\")\n", "            return None\n", "            \n", "    except Exception as e:\n", "        logger.error(f\"Error loading {file_path}: {e}\")\n", "        return None\n", "\n", "# Discover and load point cloud data\n", "print(\"\\n=== LOADING POINT CLOUD DATA ===\")\n", "point_cloud_files = discover_point_clouds(point_cloud_dir)\n", "\n", "if not point_cloud_files:\n", "    print(f\"No point cloud files found in {point_cloud_dir}\")\n", "    target_cloud = None\n", "else:\n", "    print(f\"Found {len(point_cloud_files)} point cloud files:\")\n", "    for i, pc_file in enumerate(point_cloud_files[:5], 1):\n", "        print(f\"  {i}. {pc_file.name} ({pc_file.stat().st_size / 1024 / 1024:.1f} MB)\")\n", "    \n", "    # Load first point cloud for demonstration\n", "    target_file = point_cloud_files[0]\n", "    print(f\"\\nLoading sample from: {target_file.name}\")\n", "    \n", "    target_cloud = load_point_cloud_sample(target_file)\n", "    if target_cloud:\n", "        print(f\"Loaded point cloud with {len(target_cloud.points)} points\")\n", "        \n", "        # Analyze point cloud extent\n", "        points = np.asarray(target_cloud.points)\n", "        print(f\"Point cloud analysis:\")\n", "        print(f\"  X range: {points[:, 0].min():.1f} to {points[:, 0].max():.1f}\")\n", "        print(f\"  Y range: {points[:, 1].min():.1f} to {points[:, 1].max():.1f}\")\n", "        print(f\"  Z range: {points[:, 2].min():.1f} to {points[:, 2].max():.1f}\")\n", "    else:\n", "        print(\"Failed to load point cloud\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. CAD-Guided Alignment Analysis"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== CAD-POINT CLOUD ALIGNMENT ANALYSIS ===\n", "CAD reference points: 1110\n", "Point cloud points: 100000\n", "\n", "Alignment Statistics:\n", "  Close matches (<50.0m): 148 (13.3%)\n", "  Mean distance: 51562.36m\n", "  Median distance: 70.29m\n", "  Distance range: 48.48m to 4745837.30m\n", "  Alignment quality: Poor\n"]}], "source": ["def analyze_cad_point_cloud_alignment(cad_cloud, target_cloud, max_distance=50.0):\n", "    \"\"\"Analyze alignment between CAD reference points and point cloud.\"\"\"\n", "    if cad_cloud is None or target_cloud is None:\n", "        return None\n", "    \n", "    cad_points = np.asarray(cad_cloud.points)\n", "    pc_points = np.asarray(target_cloud.points)\n", "    \n", "    print(f\"\\n=== CAD-POINT CLOUD ALIGNMENT ANALYSIS ===\")\n", "    print(f\"CAD reference points: {len(cad_points)}\")\n", "    print(f\"Point cloud points: {len(pc_points)}\")\n", "    \n", "    # Find nearest neighbors between CAD and point cloud\n", "    nbrs = NearestNeighbors(n_neighbors=1, algorithm='kd_tree').fit(pc_points)\n", "    distances, indices = nbrs.kneighbors(cad_points)\n", "    \n", "    # Analyze alignment quality\n", "    distances = distances.flatten()\n", "    close_matches = distances < max_distance\n", "    \n", "    alignment_stats = {\n", "        'total_cad_points': len(cad_points),\n", "        'close_matches': np.sum(close_matches),\n", "        'match_percentage': (np.sum(close_matches) / len(cad_points)) * 100,\n", "        'mean_distance': np.mean(distances),\n", "        'median_distance': np.median(distances),\n", "        'min_distance': np.min(distances),\n", "        'max_distance': np.max(distances),\n", "        'std_distance': np.std(distances)\n", "    }\n", "    \n", "    print(f\"\\nAlignment Statistics:\")\n", "    print(f\"  Close matches (<{max_distance}m): {alignment_stats['close_matches']} ({alignment_stats['match_percentage']:.1f}%)\")\n", "    print(f\"  Mean distance: {alignment_stats['mean_distance']:.2f}m\")\n", "    print(f\"  Median distance: {alignment_stats['median_distance']:.2f}m\")\n", "    print(f\"  Distance range: {alignment_stats['min_distance']:.2f}m to {alignment_stats['max_distance']:.2f}m\")\n", "    \n", "    # Assess alignment quality\n", "    if alignment_stats['match_percentage'] > 80:\n", "        quality = \"Excellent\"\n", "    elif alignment_stats['match_percentage'] > 60:\n", "        quality = \"Good\"\n", "    elif alignment_stats['match_percentage'] > 40:\n", "        quality = \"Fair\"\n", "    else:\n", "        quality = \"Poor\"\n", "    \n", "    print(f\"  Alignment quality: {quality}\")\n", "    \n", "    return alignment_stats\n", "\n", "# Perform alignment analysis\n", "if cad_reference_cloud and target_cloud:\n", "    alignment_results = analyze_cad_point_cloud_alignment(cad_reference_cloud, target_cloud)\n", "else:\n", "    print(\"Cannot perform alignment analysis - missing CAD or point cloud data\")\n", "    alignment_results = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Alignment Recommendations and Next Steps"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "CAD-G<PERSON>DED SPATIAL ALIGNMENT - SUMMARY\n", "============================================================\n", "\n", "📊 Alignment Assessment:\n", "  CAD reference points: 1110\n", "  Successful matches: 148 (13.3%)\n", "  Average alignment error: 51562.36m\n", "\n", "🎯 Recommendations:\n", "  ❌ Poor CAD-point cloud correspondence\n", "  ❌ Check coordinate system compatibility\n", "  ❌ Consider manual alignment or different reference data\n", "\n", "🔧 Technical Next Steps:\n", "  1. Apply coordinate system transformation if needed (EPSG:32633)\n", "  2. Use tracker coordinates for ICP initialization\n", "  3. Validate alignment using module boundaries\n", "  4. Integrate with existing alignment workflows (ICP, neural network)\n", "  5. Implement hybrid alignment using CAD + point cloud features\n", "\n", "🔗 Integration Points:\n", "  - Enhance 01_icp_alignment.ipynb with CAD reference points\n", "  - Validate 02_neural_network_alignment.ipynb results against CAD\n", "  - Implement 03_hybrid_alignment.ipynb using CAD + ML features\n", "  - Use CAD boundaries for spatial validation in pile detection\n", "\n", "📅 Completed: 2025-07-01 14:14:22\n", "============================================================\n"]}], "source": ["print(\"\\n\" + \"=\" * 60)\n", "print(\"CAD-GUIDED SPATIAL ALIGNMENT - SUMMARY\")\n", "print(\"=\" * 60)\n", "\n", "if alignment_results:\n", "    print(f\"\\n📊 Alignment Assessment:\")\n", "    print(f\"  CAD reference points: {alignment_results['total_cad_points']}\")\n", "    print(f\"  Successful matches: {alignment_results['close_matches']} ({alignment_results['match_percentage']:.1f}%)\")\n", "    print(f\"  Average alignment error: {alignment_results['mean_distance']:.2f}m\")\n", "    \n", "    print(f\"\\n🎯 Recommendations:\")\n", "    \n", "    if alignment_results['match_percentage'] > 70:\n", "        print(f\"  ✅ Good CAD-point cloud correspondence detected\")\n", "        print(f\"  ✅ CAD reference points can be used for alignment refinement\")\n", "        print(f\"  ✅ Proceed with CAD-guided alignment workflows\")\n", "    elif alignment_results['match_percentage'] > 40:\n", "        print(f\"  ⚠️ Moderate CAD-point cloud correspondence\")\n", "        print(f\"  ⚠️ Consider coordinate system transformation\")\n", "        print(f\"  ⚠️ Use CAD points for validation rather than primary alignment\")\n", "    else:\n", "        print(f\"  ❌ Poor CAD-point cloud correspondence\")\n", "        print(f\"  ❌ Check coordinate system compatibility\")\n", "        print(f\"  ❌ Consider manual alignment or different reference data\")\n", "    \n", "    print(f\"\\n🔧 Technical Next Steps:\")\n", "    print(f\"  1. Apply coordinate system transformation if needed (EPSG:32633)\")\n", "    print(f\"  2. Use tracker coordinates for ICP initialization\")\n", "    print(f\"  3. Validate alignment using module boundaries\")\n", "    print(f\"  4. Integrate with existing alignment workflows (ICP, neural network)\")\n", "    print(f\"  5. Implement hybrid alignment using CAD + point cloud features\")\n", "    \n", "else:\n", "    print(f\"\\n❌ Alignment analysis not performed\")\n", "    print(f\"  Ensure CAD extraction and point cloud data are available\")\n", "    print(f\"  Run CAD extraction notebook first: 02_solar_cad_extraction.ipynb\")\n", "    print(f\"  Verify point cloud files in: {point_cloud_dir}\")\n", "\n", "print(f\"\\n🔗 Integration Points:\")\n", "print(f\"  - Enhance 01_icp_alignment.ipynb with CAD reference points\")\n", "print(f\"  - Validate 02_neural_network_alignment.ipynb results against CAD\")\n", "print(f\"  - Implement 03_hybrid_alignment.ipynb using CAD + ML features\")\n", "print(f\"  - Use CAD boundaries for spatial validation in pile detection\")\n", "\n", "print(f\"\\n📅 Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(\"=\" * 60)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}