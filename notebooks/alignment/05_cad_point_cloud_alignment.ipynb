{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CAD to Point Cloud Alignment\n", "\n", "This notebook implements alignment of point clouds to CAD coordinate systems using extracted pile positions as reference points. It bridges CAD metadata extraction and pile detection workflows by providing georeferenced, aligned point clouds.\n", "\n", "**Stage**: Alignment  \n", "**Input Data**: Ground-filtered point cloud + CAD alignment points  \n", "**Output**: Aligned point cloud in CAD coordinate system  \n", "**Method**: ICP alignment using CAD reference points  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters\n", "project_type = \"foundation_analysis\"\n", "site_name = \"main_site\"\n", "ground_method = \"csf\"\n", "cad_alignment_file = \"motali_de_castro_main_site_alignment_points.json\"\n", "input_point_cloud = \"ground_filtered_points.las\"\n", "coordinate_system = \"EPSG:32633\"\n", "output_dir = \"../../output_runs/alignment\"\n", "mlflow_experiment_name = \"cad_alignment\"\n", "enable_visualization = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Environment Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import json\n", "import logging\n", "from pathlib import Path\n", "from datetime import datetime\n", "import time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import open3d as o3d\n", "import laspy\n", "import mlflow\n", "import matplotlib.pyplot as plt\n", "from sklearn.neighbors import NearestNeighbors\n", "from scipy.spatial.distance import cdist"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configuration and Paths"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup paths\n", "base_path = Path('../..')\n", "cad_metadata_path = base_path / 'output_runs' / 'cad_metadata'\n", "ground_seg_path = base_path / 'data' / 'processed' / 'ground_segmentation'\n", "output_path = Path(output_dir) / project_type / site_name\n", "output_path.mkdir(parents=True, exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "# MLflow setup\n", "mlflow.set_experiment(mlflow_experiment_name)\n", "run_name = f\"cad_alignment_{project_type}_{site_name}_{ground_method}\"\n", "mlflow.start_run(run_name=run_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load CAD Alignment Points"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Find CAD alignment file\n", "cad_file_path = None\n", "for file_path in cad_metadata_path.glob('*alignment_points.json'):\n", "    if site_name in file_path.name or cad_alignment_file in file_path.name:\n", "        cad_file_path = file_path\n", "        break\n", "\n", "if not cad_file_path:\n", "    raise FileNotFoundError(f\"CAD alignment file not found in {cad_metadata_path}\")\n", "\n", "print(f\"Loading CAD alignment points from: {cad_file_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load CAD alignment data\n", "with open(cad_file_path, 'r') as f:\n", "    cad_data = json.load(f)\n", "\n", "pile_points = np.array([[p['x'], p['y'], p['z']] for p in cad_data['pile_points']])\n", "foundation_points = np.array([[p['x'], p['y'], p['z']] for p in cad_data['foundation_points']])\n", "\n", "print(f\"Loaded {len(pile_points)} pile points\")\n", "print(f\"Loaded {len(foundation_points)} foundation points\")\n", "print(f\"CAD coordinate system: {cad_data['metadata']['coordinate_system']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Combine reference points\n", "if len(pile_points) > 0 and len(foundation_points) > 0:\n", "    cad_reference_points = np.vstack([pile_points, foundation_points])\n", "elif len(pile_points) > 0:\n", "    cad_reference_points = pile_points\n", "elif len(foundation_points) > 0:\n", "    cad_reference_points = foundation_points\n", "else:\n", "    raise ValueError(\"No CAD reference points available\")\n", "\n", "print(f\"Total CAD reference points: {len(cad_reference_points)}\")\n", "print(f\"CAD bounds: X[{cad_reference_points[:, 0].min():.2f}, {cad_reference_points[:, 0].max():.2f}]\")\n", "print(f\"           Y[{cad_reference_points[:, 1].min():.2f}, {cad_reference_points[:, 1].max():.2f}]\")\n", "print(f\"           Z[{cad_reference_points[:, 2].min():.2f}, {cad_reference_points[:, 2].max():.2f}]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Ground-Filtered Point Cloud"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_ground_filtered_file(site_name, method, search_paths):\n", "    \"\"\"Find ground-filtered point cloud file.\"\"\"\n", "    \n", "    # Common naming patterns\n", "    patterns = [\n", "        f\"{site_name}_ground_filtered_{method}.las\",\n", "        f\"{site_name}*{method}*.las\",\n", "        f\"*{method}*nonground*.ply\",\n", "        f\"*{method}*ground_filtered*.ply\"\n", "    ]\n", "    \n", "    for search_path in search_paths:\n", "        for pattern in patterns:\n", "            files = list(search_path.glob(pattern))\n", "            if files:\n", "                return files[0]\n", "    \n", "    return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Search for ground-filtered point cloud\n", "search_paths = [\n", "    ground_seg_path,\n", "    ground_seg_path / 'output_runs',\n", "    base_path / 'data' / 'processed',\n", "    base_path / 'notebooks' / 'data_preparation' / '02_ground_segmentation' / 'output_runs'\n", "]\n", "\n", "# Add subdirectories in output_runs\n", "for path in search_paths.copy():\n", "    if path.exists():\n", "        search_paths.extend([p for p in path.iterdir() if p.is_dir()])\n", "\n", "point_cloud_file = find_ground_filtered_file(site_name, ground_method, search_paths)\n", "\n", "if not point_cloud_file:\n", "    raise FileNotFoundError(f\"Ground-filtered point cloud not found for {site_name} with method {ground_method}\")\n", "\n", "print(f\"Loading point cloud from: {point_cloud_file}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_point_cloud(file_path):\n", "    \"\"\"Load point cloud from various formats.\"\"\"\n", "    \n", "    file_path = Path(file_path)\n", "    ext = file_path.suffix.lower()\n", "    \n", "    if ext in ['.las', '.laz']:\n", "        las = laspy.read(file_path)\n", "        points = np.vstack([las.x, las.y, las.z]).T\n", "        colors = None\n", "        if hasattr(las, 'red') and hasattr(las, 'green') and hasattr(las, 'blue'):\n", "            colors = np.vstack([las.red, las.green, las.blue]).T / 65535.0\n", "    \n", "    elif ext == '.ply':\n", "        pcd = o3d.io.read_point_cloud(str(file_path))\n", "        points = np.asarray(pcd.points)\n", "        colors = np.asarray(pcd.colors) if pcd.has_colors() else None\n", "    \n", "    else:\n", "        raise ValueError(f\"Unsupported file format: {ext}\")\n", "    \n", "    return points, colors"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load point cloud\n", "pc_points, pc_colors = load_point_cloud(point_cloud_file)\n", "\n", "print(f\"Loaded {len(pc_points):,} points\")\n", "print(f\"Point cloud bounds: X[{pc_points[:, 0].min():.2f}, {pc_points[:, 0].max():.2f}]\")\n", "print(f\"                   Y[{pc_points[:, 1].min():.2f}, {pc_points[:, 1].max():.2f}]\")\n", "print(f\"                   Z[{pc_points[:, 2].min():.2f}, {pc_points[:, 2].max():.2f}]\")\n", "print(f\"Has colors: {pc_colors is not None}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Point Cloud Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Downsample point cloud for alignment\n", "def downsample_point_cloud(points, voxel_size=0.05):\n", "    \"\"\"Downsample point cloud using voxel grid.\"\"\"\n", "    \n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(points)\n", "    \n", "    downsampled = pcd.voxel_down_sample(voxel_size)\n", "    return np.asarray(downsampled.points)\n", "\n", "# Downsample for faster alignment\n", "voxel_size = 0.1\n", "pc_downsampled = downsample_point_cloud(pc_points, voxel_size)\n", "\n", "print(f\"Original points: {len(pc_points):,}\")\n", "print(f\"Downsampled points: {len(pc_downsampled):,}\")\n", "print(f\"Reduction factor: {len(pc_points) / len(pc_downsampled):.1f}x\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extract Candidate Points from Point Cloud"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_pile_candidates(points, height_threshold=2.0, cluster_radius=1.0):\n", "    \"\"\"Extract potential pile points from point cloud.\"\"\"\n", "    \n", "    # Filter points above ground level\n", "    z_min = points[:, 2].min()\n", "    elevated_points = points[points[:, 2] > (z_min + height_threshold)]\n", "    \n", "    if len(elevated_points) == 0:\n", "        return np.array([])\n", "    \n", "    # Simple clustering to find pile-like structures\n", "    from sklearn.cluster import DBSCAN\n", "    \n", "    clustering = DBSCAN(eps=cluster_radius, min_samples=10)\n", "    labels = clustering.fit_predict(elevated_points[:, :2])  # Cluster in XY plane\n", "    \n", "    # Extract cluster centers\n", "    candidates = []\n", "    for label in set(labels):\n", "        if label == -1:  # Skip noise\n", "            continue\n", "        \n", "        cluster_points = elevated_points[labels == label]\n", "        center = cluster_points.mean(axis=0)\n", "        candidates.append(center)\n", "    \n", "    return np.array(candidates) if candidates else np.array([])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract pile candidates from point cloud\n", "pc_candidates = extract_pile_candidates(pc_downsampled)\n", "\n", "print(f\"Extracted {len(pc_candidates)} pile candidates from point cloud\")\n", "if len(pc_candidates) > 0:\n", "    print(f\"Candidate bounds: X[{pc_candidates[:, 0].min():.2f}, {pc_candidates[:, 0].max():.2f}]\")\n", "    print(f\"                  Y[{pc_candidates[:, 1].min():.2f}, {pc_candidates[:, 1].max():.2f}]\")\n", "    print(f\"                  Z[{pc_candidates[:, 2].min():.2f}, {pc_candidates[:, 2].max():.2f}]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Point Correspondence Matching"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def find_point_correspondences(source_points, target_points, max_distance=5.0):\n", "    \"\"\"Find correspondences between source and target points.\"\"\"\n", "    \n", "    if len(source_points) == 0 or len(target_points) == 0:\n", "        return np.array([]), np.array([])\n", "    \n", "    # Calculate pairwise distances\n", "    distances = cdist(source_points[:, :2], target_points[:, :2])  # Use XY only\n", "    \n", "    # Find best matches\n", "    correspondences = []\n", "    used_targets = set()\n", "    \n", "    for i in range(len(source_points)):\n", "        # Find closest unused target point\n", "        valid_targets = [j for j in range(len(target_points)) if j not in used_targets]\n", "        if not valid_targets:\n", "            break\n", "        \n", "        target_distances = distances[i, valid_targets]\n", "        min_idx = np.argmin(target_distances)\n", "        min_distance = target_distances[min_idx]\n", "        \n", "        if min_distance <= max_distance:\n", "            target_idx = valid_targets[min_idx]\n", "            correspondences.append((i, target_idx))\n", "            used_targets.add(target_idx)\n", "    \n", "    if not correspondences:\n", "        return np.array([]), np.array([])\n", "    \n", "    source_indices, target_indices = zip(*correspondences)\n", "    return source_points[list(source_indices)], target_points[list(target_indices)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Find correspondences between point cloud candidates and CAD points\n", "pc_matched, cad_matched = find_point_correspondences(pc_candidates, cad_reference_points)\n", "\n", "print(f\"Found {len(pc_matched)} point correspondences\")\n", "if len(pc_matched) > 0:\n", "    distances = np.linalg.norm(pc_matched[:, :2] - cad_matched[:, :2], axis=1)\n", "    print(f\"Mean correspondence distance: {distances.mean():.2f}m\")\n", "    print(f\"Max correspondence distance: {distances.max():.2f}m\")\n", "    print(f\"Min correspondence distance: {distances.min():.2f}m\")\n", "else:\n", "    print(\"Warning: No correspondences found. Alignment may not be possible.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Transformation Calculation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_transformation(source_points, target_points):\n", "    \"\"\"Calculate transformation matrix from source to target points.\"\"\"\n", "    \n", "    if len(source_points) < 3:\n", "        raise ValueError(\"Need at least 3 point correspondences for transformation\")\n", "    \n", "    # Center both point sets\n", "    source_centroid = source_points.mean(axis=0)\n", "    target_centroid = target_points.mean(axis=0)\n", "    \n", "    source_centered = source_points - source_centroid\n", "    target_centered = target_points - target_centroid\n", "    \n", "    # Calculate rotation using SVD\n", "    H = source_centered.T @ target_centered\n", "    U, S, Vt = np.linalg.svd(H)\n", "    R = Vt.T @ U.T\n", "    \n", "    # Ensure proper rotation (det(R) = 1)\n", "    if np.linalg.det(R) < 0:\n", "        Vt[-1, :] *= -1\n", "        R = Vt.T @ U.T\n", "    \n", "    # Calculate translation\n", "    t = target_centroid - R @ source_centroid\n", "    \n", "    # Create homogeneous transformation matrix\n", "    T = np.eye(4)\n", "    T[:3, :3] = R\n", "    T[:3, 3] = t\n", "    \n", "    return T, R, t"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate transformation if we have enough correspondences\n", "if len(pc_matched) >= 3:\n", "    T_matrix, R_matrix, t_vector = calculate_transformation(pc_matched, cad_matched)\n", "    \n", "    print(\"Transformation calculated successfully\")\n", "    print(f\"Rotation matrix:\")\n", "    print(R_matrix)\n", "    print(f\"Translation vector: {t_vector}\")\n", "    \n", "    # Calculate transformation quality\n", "    transformed_pc = (R_matrix @ pc_matched.T).T + t_vector\n", "    residuals = np.linalg.norm(transformed_pc - cad_matched, axis=1)\n", "    rms_error = np.sqrt(np.mean(residuals**2))\n", "    \n", "    print(f\"RMS alignment error: {rms_error:.3f}m\")\n", "    print(f\"Max residual: {residuals.max():.3f}m\")\n", "    print(f\"Mean residual: {residuals.mean():.3f}m\")\n", "    \n", "else:\n", "    print(f\"Insufficient correspondences ({len(pc_matched)}) for transformation calculation\")\n", "    print(\"Using identity transformation (no alignment)\")\n", "    T_matrix = np.eye(4)\n", "    R_matrix = np.eye(3)\n", "    t_vector = np.zeros(3)\n", "    rms_error = float('inf')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Apply Transformation to Full Point Cloud"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def apply_transformation(points, R, t):\n", "    \"\"\"Apply transformation to point cloud.\"\"\"\n", "    return (R @ points.T).T + t\n", "\n", "# Apply transformation to full point cloud\n", "aligned_points = apply_transformation(pc_points, R_matrix, t_vector)\n", "\n", "print(f\"Applied transformation to {len(aligned_points):,} points\")\n", "print(f\"Aligned bounds: X[{aligned_points[:, 0].min():.2f}, {aligned_points[:, 0].max():.2f}]\")\n", "print(f\"               Y[{aligned_points[:, 1].min():.2f}, {aligned_points[:, 1].max():.2f}]\")\n", "print(f\"               Z[{aligned_points[:, 2].min():.2f}, {aligned_points[:, 2].max():.2f}]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Aligned Point Cloud"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create output filenames\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "output_las = output_path / f\"{site_name}_cad_aligned_{ground_method}_{timestamp}.las\"\n", "output_ply = output_path / f\"{site_name}_cad_aligned_{ground_method}_{timestamp}.ply\"\n", "output_json = output_path / f\"{site_name}_alignment_metadata_{timestamp}.json\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save as LAS file\n", "def save_las_file(points, colors, output_path, coordinate_system):\n", "    \"\"\"Save point cloud as LAS file.\"\"\"\n", "    \n", "    header = laspy.LasHeader(point_format=3, version=\"1.2\")\n", "    header.add_extra_dim(laspy.ExtraBytesParams(name=\"alignment_quality\", type=np.float32))\n", "    \n", "    las = laspy.<PERSON>(header)\n", "    las.x = points[:, 0]\n", "    las.y = points[:, 1] \n", "    las.z = points[:, 2]\n", "    \n", "    if colors is not None:\n", "        las.red = (colors[:, 0] * 65535).astype(np.uint16)\n", "        las.green = (colors[:, 1] * 65535).astype(np.uint16)\n", "        las.blue = (colors[:, 2] * 65535).astype(np.uint16)\n", "    \n", "    las.write(output_path)\n", "\n", "save_las_file(aligned_points, pc_colors, output_las, coordinate_system)\n", "print(f\"Saved aligned point cloud: {output_las}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save as PLY file\n", "aligned_pcd = o3d.geometry.PointCloud()\n", "aligned_pcd.points = o3d.utility.Vector3dVector(aligned_points)\n", "if pc_colors is not None:\n", "    aligned_pcd.colors = o3d.utility.Vector3dVector(pc_colors)\n", "\n", "o3d.io.write_point_cloud(str(output_ply), aligned_pcd)\n", "print(f\"Saved aligned point cloud: {output_ply}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Alignment Metadata"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create alignment metadata\n", "alignment_metadata = {\n", "    'metadata': {\n", "        'creation_timestamp': datetime.now().isoformat(),\n", "        'site_name': site_name,\n", "        'project_type': project_type,\n", "        'ground_method': ground_method,\n", "        'coordinate_system': coordinate_system,\n", "        'alignment_method': 'cad_reference_points'\n", "    },\n", "    'input_files': {\n", "        'point_cloud': str(point_cloud_file),\n", "        'cad_alignment': str(cad_file_path)\n", "    },\n", "    'output_files': {\n", "        'aligned_las': str(output_las),\n", "        'aligned_ply': str(output_ply)\n", "    },\n", "    'alignment_quality': {\n", "        'correspondences_found': len(pc_matched),\n", "        'rms_error_meters': float(rms_error) if rms_error != float('inf') else None,\n", "        'transformation_matrix': T_matrix.tolist(),\n", "        'rotation_matrix': R_matrix.tolist(),\n", "        'translation_vector': t_vector.tolist()\n", "    },\n", "    'statistics': {\n", "        'input_points': len(pc_points),\n", "        'output_points': len(aligned_points),\n", "        'cad_reference_points': len(cad_reference_points),\n", "        'pile_candidates_extracted': len(pc_candidates)\n", "    }\n", "}\n", "\n", "with open(output_json, 'w') as f:\n", "    json.dump(alignment_metadata, f, indent=2)\n", "\n", "print(f\"Saved alignment metadata: {output_json}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## MLflow Logging"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Log parameters and metrics to MLflow\n", "mlflow.log_param(\"site_name\", site_name)\n", "mlflow.log_param(\"project_type\", project_type)\n", "mlflow.log_param(\"ground_method\", ground_method)\n", "mlflow.log_param(\"coordinate_system\", coordinate_system)\n", "mlflow.log_param(\"voxel_size\", voxel_size)\n", "\n", "mlflow.log_metric(\"input_points\", len(pc_points))\n", "mlflow.log_metric(\"cad_reference_points\", len(cad_reference_points))\n", "mlflow.log_metric(\"correspondences_found\", len(pc_matched))\n", "mlflow.log_metric(\"pile_candidates_extracted\", len(pc_candidates))\n", "\n", "if rms_error != float('inf'):\n", "    mlflow.log_metric(\"rms_alignment_error\", rms_error)\n", "    mlflow.log_metric(\"alignment_success\", 1)\n", "else:\n", "    mlflow.log_metric(\"alignment_success\", 0)\n", "\n", "# Log artifacts\n", "mlflow.log_artifact(str(output_json))\n", "mlflow.log_artifact(str(output_las))\n", "mlflow.log_artifact(str(output_ply))\n", "\n", "print(\"Logged results to MLflow\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary and Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\nCAD to Point Cloud Alignment Summary\")\n", "print(\"=\" * 50)\n", "print(f\"Site: {site_name}\")\n", "print(f\"Ground segmentation method: {ground_method}\")\n", "print(f\"Input points: {len(pc_points):,}\")\n", "print(f\"CAD reference points: {len(cad_reference_points)}\")\n", "print(f\"Point correspondences: {len(pc_matched)}\")\n", "\n", "if rms_error != float('inf'):\n", "    print(f\"Alignment RMS error: {rms_error:.3f}m\")\n", "    print(\"Alignment status: SUCCESS\")\n", "else:\n", "    print(\"Alignment status: FAILED (insufficient correspondences)\")\n", "\n", "print(f\"\\nOutput files:\")\n", "print(f\"- Aligned LAS: {output_las.name}\")\n", "print(f\"- Aligned PLY: {output_ply.name}\")\n", "print(f\"- Metadata: {output_json.name}\")\n", "\n", "mlflow.end_run()\n", "print(\"\\nAlignment workflow completed\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}