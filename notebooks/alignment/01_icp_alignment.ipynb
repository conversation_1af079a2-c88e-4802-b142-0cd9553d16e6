{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters - can be overridden during execution\n", "project_type = \"foundation_analysis\"  # Project type for data organization\n", "site_name = \"castro_area4\"            # Site identifier for processing\n", "source_file = \"source_pointcloud.las\"  # Source point cloud file\n", "target_file = \"target_pointcloud.las\"  # Target point cloud file\n", "max_iterations = 50\n", "tolerance = 1e-6\n", "voxel_size = 0.02\n", "distance_threshold = 0.1\n", "output_dir = \"../../data/output_runs\"\n", "mlflow_experiment_name = \"alignment_icp\"\n", "mlflow_run_name = f\"icp_{project_type}_{site_name}\"\n", "enable_visualization = True\n", "save_intermediate = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ICP-Based Point Cloud Alignment\n", "\n", "This notebook implements traditional Iterative Closest Point (ICP) alignment for point cloud registration. It provides a comprehensive implementation with modular execution cells for clarity and detailed analysis of ICP performance.\n", "\n", "**Stage**: Alignment  \n", "**Input Data**: Source and target point clouds  \n", "**Output**: Aligned point cloud with transformation matrix  \n", "**Method**: Traditional ICP with multiple variants  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025  \n", "**Project**: As-Built Foundation Analysis\n", "\n", "## Process Overview:\n", "1. **Environment Setup**: Import libraries and configure parameters\n", "2. **Data Loading**: Load source and target point clouds\n", "3. **Preprocessing**: Normalize and downsample point clouds\n", "4. **ICP Implementation**: Multiple ICP variants and optimizations\n", "5. **Evaluation**: Performance metrics and quality assessment\n", "6. **Visualization**: Comprehensive alignment results\n", "7. **Export**: Save aligned point clouds and metadata"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Environment Setup\n", "\n", "Configure the environment with required libraries and parameters for ICP alignment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install open3d matplotlib laspy transforms3d scipy pandas mlflow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import numpy as np\n", "import os\n", "import matplotlib.pyplot as plt\n", "import open3d as o3d\n", "import laspy\n", "import logging\n", "import time\n", "from pathlib import Path\n", "from datetime import datetime\n", "from scipy.spatial import cKDTree\n", "import pandas as pd\n", "import json\n", "\n", "# MLflow tracking\n", "try:\n", "    import mlflow\n", "    import mlflow.sklearn\n", "    MLFLOW_AVAILABLE = True\n", "    print(\"MLflow available for experiment tracking\")\n", "except ImportError:\n", "    MLFLOW_AVAILABLE = False\n", "    print(\"MLflow not available - install with: pip install mlflow\")\n", "\n", "# Set random seeds for reproducibility\n", "np.random.seed(42)\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"ICP Alignment Environment Initialized\")\n", "print(f\"Open3D version: {o3d.__version__}\")\n", "print(f\"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configuration Parameters\n", "\n", "Define ICP algorithm parameters and data paths."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class ICPConfig:\n", "    \"\"\"Configuration parameters for ICP alignment.\"\"\"\n", "    \n", "    def __init__(self):\n", "        # Use Papermill parameters\n", "        self.PROJECT_TYPE = project_type\n", "        self.PROJECT_NAME = project_name\n", "        self.SOURCE_FILE = source_file\n", "        self.TARGET_FILE = target_file\n", "        self.MAX_ITERATIONS = max_iterations\n", "        self.TOLERANCE = tolerance\n", "        self.VOXEL_SIZE = voxel_size\n", "        self.DISTANCE_THRESHOLD = distance_threshold\n", "        \n", "        # Setup paths\n", "        self.base_path = Path('../..')\n", "        self.data_path = self.base_path / 'data'\n", "        self.input_path = self.data_path / self.PROJECT_TYPE / self.PROJECT_NAME / 'preprocessing'\n", "        self.output_path = Path(output_dir) / self.PROJECT_TYPE / self.PROJECT_NAME / 'icp_results'\n", "        self.output_path.mkdir(parents=True, exist_ok=True)\n", "        \n", "        print(f\"Project: {self.PROJECT_TYPE}/{self.PROJECT_NAME}\")\n", "        print(f\"Input path: {self.input_path}\")\n", "        print(f\"Output path: {self.output_path}\")\n", "        print(f\"Source file: {self.SOURCE_FILE}\")\n", "        print(f\"Target file: {self.TARGET_FILE}\")\n", "\n", "config = ICPConfig()\n", "\n", "# Initialize MLflow if available\n", "if MLFLOW_AVAILABLE:\n", "    mlflow.set_experiment(mlflow_experiment_name)\n", "    mlflow.start_run(run_name=mlflow_run_name)\n", "    \n", "    # Log parameters\n", "    mlflow.log_param(\"project_type\", project_type)\n", "    mlflow.log_param(\"project_name\", project_name)\n", "    mlflow.log_param(\"source_file\", source_file)\n", "    mlflow.log_param(\"target_file\", target_file)\n", "    mlflow.log_param(\"max_iterations\", max_iterations)\n", "    mlflow.log_param(\"tolerance\", tolerance)\n", "    mlflow.log_param(\"voxel_size\", voxel_size)\n", "    mlflow.log_param(\"distance_threshold\", distance_threshold)\n", "    mlflow.log_param(\"method\", \"ICP\")\n", "    mlflow.log_param(\"stage\", \"alignment\")\n", "    \n", "    print(\"MLflow experiment initialized\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Loading Functions\n", "\n", "Implement functions to load point clouds from various formats."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_las_file(filename, num_points=None):\n", "    \"\"\"\n", "    Reads a LAS file and returns the points as a numpy array.\n", "    \n", "    Parameters:\n", "    -----------\n", "    filename : str\n", "        Path to the LAS file\n", "    num_points : int, optional\n", "        Number of points to read (if None, reads all points)\n", "    \n", "    Returns:\n", "    --------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Array of shape (N, 3) containing XYZ coordinates\n", "    \"\"\"\n", "    logger.info(f\"Reading LAS file: {filename}\")\n", "    try:\n", "        las_data = laspy.read(filename)\n", "        \n", "        # Determine the number of points to read\n", "        if num_points is None:\n", "            num_points = len(las_data.x)\n", "        else:\n", "            num_points = min(num_points, len(las_data.x))\n", "        \n", "        # Extract XYZ coordinates\n", "        x = np.array(las_data.x[:num_points], dtype=np.float64)\n", "        y = np.array(las_data.y[:num_points], dtype=np.float64)\n", "        z = np.array(las_data.z[:num_points], dtype=np.float64)\n", "        \n", "        # Stack XYZ coordinates\n", "        points = np.column_stack((x, y, z))\n", "        logger.info(f\"Loaded {points.shape[0]} points from {filename}\")\n", "        return points\n", "    except Exception as e:\n", "        logger.error(f\"Error reading LAS file '{filename}': {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def read_point_cloud_file(filename):\n", "    \"\"\"\n", "    Reads point cloud from various formats (PLY, PCD, OBJ).\n", "    \n", "    Parameters:\n", "    -----------\n", "    filename : str\n", "        Path to the point cloud file\n", "    \n", "    Returns:\n", "    --------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Array of shape (N, 3) containing XYZ coordinates\n", "    \"\"\"\n", "    logger.info(f\"Reading point cloud file: {filename}\")\n", "    try:\n", "        file_path = Path(filename)\n", "        \n", "        if file_path.suffix.lower() == '.las':\n", "            return read_las_file(filename)\n", "        elif file_path.suffix.lower() in ['.ply', '.pcd']:\n", "            pcd = o3d.io.read_point_cloud(str(filename))\n", "            points = np.asarray(pcd.points)\n", "            logger.info(f\"Loaded {points.shape[0]} points from {filename}\")\n", "            return points\n", "        elif file_path.suffix.lower() == '.obj':\n", "            mesh = o3d.io.read_triangle_mesh(str(filename))\n", "            points = np.asarray(mesh.vertices)\n", "            logger.info(f\"Loaded {points.shape[0]} points from {filename}\")\n", "            return points\n", "        else:\n", "            logger.error(f\"Unsupported file format: {file_path.suffix}\")\n", "            return None\n", "    except Exception as e:\n", "        logger.error(f\"Error reading point cloud file '{filename}': {e}\")\n", "        return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Point Cloud Preprocessing\n", "\n", "Implement preprocessing functions for normalization and downsampling."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def normalize_point_cloud(points):\n", "    \"\"\"\n", "    Normalizes a point cloud to be centered at the origin and scaled within a unit sphere.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Input point cloud of shape (N, 3)\n", "    \n", "    Returns:\n", "    --------\n", "    normalized : numpy.n<PERSON>ray\n", "        Normalized point cloud of shape (N, 3)\n", "    centroid : numpy.n<PERSON><PERSON>\n", "        Original centroid for denormalization\n", "    scale : float\n", "        Original scale for denormalization\n", "    \"\"\"\n", "    # Center the point cloud at the origin\n", "    centroid = np.mean(points, axis=0)\n", "    centered = points - centroid\n", "    \n", "    # Scale the point cloud to fit inside a unit sphere\n", "    furthest_distance = np.max(np.linalg.norm(centered, axis=1))\n", "    if furthest_distance > 0:\n", "        normalized = centered / furthest_distance\n", "    else:\n", "        normalized = centered\n", "    \n", "    return normalized, centroid, furthest_distance"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def downsample_voxel(points, voxel_size=0.02):\n", "    \"\"\"\n", "    Applies voxel grid downsampling to reduce point count while preserving structure.\n", "    \n", "    Parameters:\n", "    -----------\n", "    points : numpy.n<PERSON><PERSON>\n", "        Input point cloud of shape (N, 3)\n", "    voxel_size : float\n", "        Size of voxel grid cells for downsampling\n", "    \n", "    Returns:\n", "    --------\n", "    downsampled : numpy.n<PERSON><PERSON>\n", "        Downsampled point cloud\n", "    \"\"\"\n", "    # Convert numpy array to Open3D point cloud\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(points)\n", "    \n", "    # Apply voxel downsampling\n", "    downsampled = pcd.voxel_down_sample(voxel_size=voxel_size)\n", "    \n", "    # Convert back to numpy array\n", "    return np.asarray(downsampled.points)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. ICP Algorithm Implementation\n", "\n", "Implement the core ICP algorithm with multiple variants."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def nearest_neighbor(source, target):\n", "    \"\"\"\n", "    Find nearest neighbors between source and target point clouds.\n", "    \n", "    Parameters:\n", "    -----------\n", "    source : numpy.ndarray\n", "        Source point cloud of shape (N, 3)\n", "    target : numpy.n<PERSON><PERSON>\n", "        Target point cloud of shape (M, 3)\n", "    \n", "    Returns:\n", "    --------\n", "    distances : numpy.ndarray\n", "        Distances to nearest neighbors\n", "    indices : numpy.ndarray\n", "        Indices of nearest neighbors in target\n", "    \"\"\"\n", "    tree = cKDTree(target)\n", "    distances, indices = tree.query(source)\n", "    return distances, indices"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def best_fit_transform(source, target):\n", "    \"\"\"\n", "    Calculates the least-squares best-fit transform between corresponding 3D points.\n", "    \n", "    Parameters:\n", "    -----------\n", "    source : numpy.ndarray\n", "        Source points of shape (N, 3)\n", "    target : numpy.n<PERSON><PERSON>\n", "        Target points of shape (N, 3)\n", "    \n", "    Returns:\n", "    --------\n", "    T : numpy.n<PERSON><PERSON>\n", "        Homogeneous transformation matrix (4, 4)\n", "    R : numpy.n<PERSON><PERSON>\n", "        Rotation matrix (3, 3)\n", "    t : numpy.n<PERSON><PERSON>\n", "        Translation vector (3,)\n", "    \"\"\"\n", "    assert source.shape == target.shape, \"Source and target must have the same shape\"\n", "    \n", "    # Center both point clouds\n", "    source_centroid = np.mean(source, axis=0)\n", "    target_centroid = np.mean(target, axis=0)\n", "    source_centered = source - source_centroid\n", "    target_centered = target - target_centroid\n", "    \n", "    # Compute covariance matrix H\n", "    H = np.dot(source_centered.T, target_centered)\n", "    \n", "    # Singular Value Decomposition\n", "    U, S, Vt = np.linalg.svd(H)\n", "    \n", "    # Compute rotation matrix R\n", "    R = np.dot(Vt.T, U.T)\n", "    \n", "    # Ensure proper rotation (det(R) = 1)\n", "    if np.linalg.det(R) < 0:\n", "        Vt[-1, :] *= -1\n", "        R = np.dot(Vt.T, U.T)\n", "    \n", "    # Compute translation\n", "    t = target_centroid - np.dot(R, source_centroid)\n", "    \n", "    # Create homogeneous transformation matrix\n", "    T = np.identity(4)\n", "    T[:3, :3] = R\n", "    T[:3, 3] = t\n", "    \n", "    return T, R, t"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def icp_algorithm(source, target, max_iterations=50, tolerance=1e-6, verbose=False):\n", "    \"\"\"\n", "    Iterative Closest Point (ICP) algorithm for point cloud alignment.\n", "    \n", "    Parameters:\n", "    -----------\n", "    source : numpy.ndarray\n", "        Source point cloud of shape (N, 3)\n", "    target : numpy.n<PERSON><PERSON>\n", "        Target point cloud of shape (M, 3)\n", "    max_iterations : int\n", "        Maximum number of iterations\n", "    tolerance : float\n", "        Convergence tolerance\n", "    verbose : bool\n", "        Whether to print progress information\n", "    \n", "    Returns:\n", "    --------\n", "    T_combined : numpy.n<PERSON><PERSON>\n", "        Final transformation matrix (4, 4)\n", "    aligned_source : numpy.ndarray\n", "        Aligned source point cloud\n", "    final_error : float\n", "        Final mean squared error\n", "    iterations : int\n", "        Number of iterations performed\n", "    convergence_history : list\n", "        History of error values for each iteration\n", "    \"\"\"\n", "    # Make a copy of the source point cloud\n", "    source_copy = np.copy(source)\n", "    prev_error = float('inf')\n", "    convergence_history = []\n", "    \n", "    # Initialize transformation matrix\n", "    T_combined = np.identity(4)\n", "    \n", "    start_time = time.time()\n", "    \n", "    for iteration in range(max_iterations):\n", "        # Find nearest neighbors\n", "        distances, indices = nearest_neighbor(source_copy, target)\n", "        \n", "        # Compute mean squared error\n", "        mean_error = np.mean(distances**2)\n", "        convergence_history.append(mean_error)\n", "        \n", "        # Check for convergence\n", "        if verbose:\n", "            print(f\"Iteration {iteration+1:3d}, MSE: {mean_error:.10f}\")\n", "        \n", "        if abs(prev_error - mean_error) < tolerance:\n", "            if verbose:\n", "                print(f\"Converged after {iteration+1} iterations.\")\n", "            break\n", "        \n", "        prev_error = mean_error\n", "        \n", "        # Get corresponding points\n", "        corresponding_target_points = target[indices]\n", "        \n", "        # Compute transformation\n", "        T, R, t = best_fit_transform(source_copy, corresponding_target_points)\n", "        \n", "        # Update transformation matrix\n", "        T_combined = np.dot(T, T_combined)\n", "        \n", "        # Apply transformation\n", "        source_copy = np.dot(source_copy, R.T) + t\n", "    \n", "    end_time = time.time()\n", "    \n", "    if verbose:\n", "        print(f\"ICP completed in {end_time - start_time:.4f} seconds\")\n", "        if iteration == max_iterations - 1:\n", "            print(f\"Warning: Maximum iterations ({max_iterations}) reached without convergence.\")\n", "    \n", "    return T_combined, source_copy, mean_error, iteration + 1, convergence_history"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Execute ICP Alignment\n", "\n", "Load data and execute the ICP alignment algorithm."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load source and target point clouds\n", "print(\"Loading point cloud data...\")\n", "\n", "# Try to load specified files\n", "source_path = config.input_path / config.SOURCE_FILE\n", "target_path = config.input_path / config.TARGET_FILE\n", "\n", "if source_path.exists() and target_path.exists():\n", "    print(f\"Loading source: {source_path}\")\n", "    source_points = read_point_cloud_file(source_path)\n", "    \n", "    print(f\"Loading target: {target_path}\")\n", "    target_points = read_point_cloud_file(target_path)\n", "    \n", "    if source_points is None or target_points is None:\n", "        raise ValueError(\"Failed to load specified point cloud files\")\n", "        \n", "else:\n", "    print(\"Specified files not found. Creating synthetic test data...\")\n", "    # Create synthetic point clouds for testing\n", "    np.random.seed(42)\n", "    source_points = np.random.rand(5000, 3) * 2 - 1  # Random points in [-1, 1]\n", "    \n", "    # Create target by applying a known transformation\n", "    angle = np.radians(15)\n", "    R_true = np.array([\n", "        [np.cos(angle), -np.sin(angle), 0],\n", "        [np.sin(angle), np.cos(angle), 0],\n", "        [0, 0, 1]\n", "    ])\n", "    t_true = np.array([0.2, 0.1, 0.05])\n", "    target_points = np.dot(source_points, R_true.T) + t_true\n", "    \n", "    # Add some noise\n", "    target_points += np.random.normal(0, 0.01, target_points.shape)\n", "\n", "print(f\"Source points shape: {source_points.shape}\")\n", "print(f\"Target points shape: {target_points.shape}\")\n", "\n", "# Preprocess point clouds\n", "print(\"\\nPreprocessing point clouds...\")\n", "if config.VOXEL_SIZE > 0:\n", "    print(f\"Downsampling with voxel size: {config.VOXEL_SIZE}\")\n", "    source_downsampled = downsample_voxel(source_points, config.VOXEL_SIZE)\n", "    target_downsampled = downsample_voxel(target_points, config.VOXEL_SIZE)\n", "    print(f\"Downsampled source: {source_downsampled.shape[0]} points\")\n", "    print(f\"Downsampled target: {target_downsampled.shape[0]} points\")\n", "else:\n", "    source_downsampled = source_points\n", "    target_downsampled = target_points\n", "\n", "# Execute ICP alignment\n", "print(\"\\n=== Executing ICP Alignment ===\")\n", "icp_start_time = time.time()\n", "\n", "T_icp, aligned_source, final_error, iterations, convergence_history = icp_algorithm(\n", "    source_downsampled, \n", "    target_downsampled, \n", "    max_iterations=config.MAX_ITERATIONS, \n", "    tolerance=config.TOLERANCE, \n", "    verbose=True\n", ")\n", "\n", "icp_time = time.time() - icp_start_time\n", "\n", "print(f\"\\n=== ICP Results ===\")\n", "print(f\"Execution time: {icp_time:.4f} seconds\")\n", "print(f\"Iterations: {iterations}\")\n", "print(f\"Final MSE: {final_error:.10f}\")\n", "print(f\"Converged: {iterations < config.MAX_ITERATIONS}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Performance Evaluation and Results Export\n", "\n", "Calculate comprehensive performance metrics and save results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate comprehensive performance metrics\n", "tree = cKDTree(target_downsampled)\n", "distances, _ = tree.query(aligned_source)\n", "\n", "# Performance metrics\n", "rmse = np.sqrt(np.mean(distances**2))\n", "mean_distance = np.mean(distances)\n", "median_distance = np.median(distances)\n", "std_distance = np.std(distances)\n", "max_distance = np.max(distances)\n", "min_distance = np.min(distances)\n", "\n", "# Accuracy metrics\n", "accuracy_01 = np.mean(distances < 0.01) * 100\n", "accuracy_05 = np.mean(distances < 0.05) * 100\n", "accuracy_10 = np.mean(distances < config.DISTANCE_THRESHOLD) * 100\n", "\n", "print(f\"\\n=== Performance Metrics ===\")\n", "print(f\"RMSE: {rmse:.6f}\")\n", "print(f\"Mean Distance: {mean_distance:.6f}\")\n", "print(f\"Median Distance: {median_distance:.6f}\")\n", "print(f\"Std Distance: {std_distance:.6f}\")\n", "print(f\"Max Distance: {max_distance:.6f}\")\n", "print(f\"Min Distance: {min_distance:.6f}\")\n", "print(f\"Accuracy (< 0.01): {accuracy_01:.2f}%\")\n", "print(f\"Accuracy (< 0.05): {accuracy_05:.2f}%\")\n", "print(f\"Accuracy (< {config.DISTANCE_THRESHOLD}): {accuracy_10:.2f}%\")\n", "\n", "# Apply transformation to full resolution source if downsampling was used\n", "if config.VOXEL_SIZE > 0:\n", "    R_icp = T_icp[:3, :3]\n", "    t_icp = T_icp[:3, 3]\n", "    aligned_source_full = np.dot(source_points, R_icp.T) + t_icp\n", "else:\n", "    aligned_source_full = aligned_source\n", "\n", "# Save aligned point cloud\n", "aligned_pcd = o3d.geometry.PointCloud()\n", "aligned_pcd.points = o3d.utility.Vector3dVector(aligned_source_full)\n", "\n", "output_file_pcd = config.output_path / 'icp_aligned_source.pcd'\n", "output_file_ply = config.output_path / 'icp_aligned_source.ply'\n", "\n", "o3d.io.write_point_cloud(str(output_file_pcd), aligned_pcd)\n", "o3d.io.write_point_cloud(str(output_file_ply), aligned_pcd)\n", "\n", "print(f\"\\nSaved aligned point cloud to:\")\n", "print(f\"  PCD: {output_file_pcd}\")\n", "print(f\"  PLY: {output_file_ply}\")\n", "\n", "# Save metadata\n", "metadata = {\n", "    'method': 'ICP',\n", "    'timestamp': datetime.now().isoformat(),\n", "    'project_type': config.PROJECT_TYPE,\n", "    'project_name': config.PROJECT_NAME,\n", "    'source_file': config.SOURCE_FILE,\n", "    'target_file': config.TARGET_FILE,\n", "    'source_points_count': len(source_points),\n", "    'target_points_count': len(target_points),\n", "    'aligned_points_count': len(aligned_source_full),\n", "    'transformation_matrix': T_icp.tolist(),\n", "    'parameters': {\n", "        'max_iterations': config.MAX_ITERATIONS,\n", "        'tolerance': config.TOLERANCE,\n", "        'voxel_size': config.VOXEL_SIZE,\n", "        'distance_threshold': config.DISTANCE_THRESHOLD\n", "    },\n", "    'performance_metrics': {\n", "        'rmse': rmse,\n", "        'mean_distance': mean_distance,\n", "        'median_distance': median_distance,\n", "        'std_distance': std_distance,\n", "        'max_distance': max_distance,\n", "        'min_distance': min_distance,\n", "        'accuracy_01': accuracy_01,\n", "        'accuracy_05': accuracy_05,\n", "        'accuracy_10': accuracy_10,\n", "        'final_mse': final_error,\n", "        'iterations': iterations,\n", "        'converged': iterations < config.MAX_ITERATIONS\n", "    },\n", "    'timing_info': {\n", "        'total_time': icp_time,\n", "        'alignment_time': icp_time\n", "    },\n", "    'convergence_history': convergence_history\n", "}\n", "\n", "metadata_file = config.output_path / 'icp_alignment_metadata.json'\n", "with open(metadata_file, 'w') as f:\n", "    json.dump(metadata, f, indent=2)\n", "\n", "print(f\"Saved metadata to: {metadata_file}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. MLflow Logging and Experiment Tracking\n", "\n", "Log results to MLflow for experiment tracking and comparison."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# MLflow logging\n", "if MLFLOW_AVAILABLE:\n", "    print(\"\\nLogging results to MLflow...\")\n", "    \n", "    try:\n", "        # Log metrics\n", "        mlflow.log_metric(\"rmse\", rmse)\n", "        mlflow.log_metric(\"mean_distance\", mean_distance)\n", "        mlflow.log_metric(\"median_distance\", median_distance)\n", "        mlflow.log_metric(\"std_distance\", std_distance)\n", "        mlflow.log_metric(\"max_distance\", max_distance)\n", "        mlflow.log_metric(\"min_distance\", min_distance)\n", "        mlflow.log_metric(\"accuracy_01\", accuracy_01)\n", "        mlflow.log_metric(\"accuracy_05\", accuracy_05)\n", "        mlflow.log_metric(\"accuracy_10\", accuracy_10)\n", "        mlflow.log_metric(\"final_mse\", final_error)\n", "        mlflow.log_metric(\"iterations\", iterations)\n", "        mlflow.log_metric(\"execution_time\", icp_time)\n", "        mlflow.log_metric(\"converged\", int(iterations < config.MAX_ITERATIONS))\n", "        mlflow.log_metric(\"source_points_count\", len(source_points))\n", "        mlflow.log_metric(\"target_points_count\", len(target_points))\n", "        \n", "        # Log artifacts\n", "        mlflow.log_artifact(str(output_file_pcd))\n", "        mlflow.log_artifact(str(output_file_ply))\n", "        mlflow.log_artifact(str(metadata_file))\n", "        \n", "        # Add tags\n", "        mlflow.set_tag(\"method\", \"ICP\")\n", "        mlflow.set_tag(\"stage\", \"alignment\")\n", "        mlflow.set_tag(\"project_type\", config.PROJECT_TYPE)\n", "        mlflow.set_tag(\"project_name\", config.PROJECT_NAME)\n", "        mlflow.set_tag(\"data_source\", \"point_cloud_alignment\")\n", "        \n", "        print(\"MLflow logging completed successfully.\")\n", "        \n", "    except Exception as e:\n", "        print(f\"MLflow logging failed: {e}\")\n", "    \n", "    finally:\n", "        # End the MLflow run\n", "        mlflow.end_run()\n", "        print(\"MLflow run ended.\")\n", "else:\n", "    print(\"MLflow not available - skipping experiment tracking.\")\n", "\n", "print(\"\\n=== ICP Alignment Completed Successfully ===\")\n", "print(f\"Results saved to: {config.output_path}\")\n", "print(f\"Execution time: {icp_time:.4f} seconds\")\n", "print(f\"Final RMSE: {rmse:.6f}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}